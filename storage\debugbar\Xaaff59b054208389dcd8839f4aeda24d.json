{"__meta": {"id": "Xaaff59b054208389dcd8839f4aeda24d", "datetime": "2025-07-09 15:30:24", "utime": 1752049824.656197, "method": "GET", "uri": "/administrator/ordercancelhistories/datatable.json?draw=1&columns%5B0%5D%5Bdata%5D=DT_RowIndex&columns%5B0%5D%5Bname%5D=id&columns%5B0%5D%5Bsearchable%5D=true&columns%5B0%5D%5Borderable%5D=true&columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B1%5D%5Bdata%5D=order.order_code&columns%5B1%5D%5Bname%5D=order.order_code&columns%5B1%5D%5Bsearchable%5D=false&columns%5B1%5D%5Borderable%5D=true&columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B2%5D%5Bdata%5D=user.fullname&columns%5B2%5D%5Bname%5D=user.fullname&columns%5B2%5D%5Bsearchable%5D=false&columns%5B2%5D%5Borderable%5D=true&columns%5B2%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B2%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B3%5D%5Bdata%5D=created_at&columns%5B3%5D%5Bname%5D=created_at&columns%5B3%5D%5Bsearchable%5D=false&columns%5B3%5D%5Borderable%5D=true&columns%5B3%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B3%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B4%5D%5Bdata%5D=action&columns%5B4%5D%5Bname%5D=action&columns%5B4%5D%5Bsearchable%5D=false&columns%5B4%5D%5Borderable%5D=false&columns%5B4%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B4%5D%5Bsearch%5D%5Bregex%5D=false&order%5B0%5D%5Bcolumn%5D=0&order%5B0%5D%5Bdir%5D=asc&start=0&length=10&search%5Bvalue%5D=&search%5Bregex%5D=false&no_order=20185&_=1752049795640", "ip": "127.0.0.1"}, "php": {"version": "8.3.6", "interface": "cli-server"}, "messages": {"count": 32, "messages": [{"message": "[15:30:23] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1752049823.44856, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:24] LOG.warning: Creation of dynamic property App\\Http\\Controllers\\Generator\\OrderCancelHistoryController::$user is deprecated in D:\\Topan\\cms-juraganbeku\\app\\Http\\Controllers\\CoreController.php on line 56", "message_html": null, "is_string": false, "label": "warning", "time": 1752049824.387741, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:24] LOG.warning: Creation of dynamic property Yajra\\DataTables\\Processors\\DataProcessor::$onlyColumns is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\Processors\\DataProcessor.php on line 83", "message_html": null, "is_string": false, "label": "warning", "time": 1752049824.613355, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:24] LOG.warning: Creation of dynamic property Yajra\\DataTables\\Processors\\DataProcessor::$makeHidden is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\Processors\\DataProcessor.php on line 87", "message_html": null, "is_string": false, "label": "warning", "time": 1752049824.613498, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:24] LOG.warning: Creation of dynamic property Yajra\\DataTables\\Processors\\DataProcessor::$makeVisible is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\Processors\\DataProcessor.php on line 88", "message_html": null, "is_string": false, "label": "warning", "time": 1752049824.613621, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:24] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049824.634932, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:24] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049824.635069, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:24] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049824.635177, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:24] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049824.635341, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:24] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049824.635442, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:24] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049824.635544, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:24] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049824.635647, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:24] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049824.635749, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:24] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049824.635856, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:24] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049824.635984, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:24] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049824.636091, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:24] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049824.636189, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:24] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049824.636293, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:24] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049824.636401, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:24] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049824.636514, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:24] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049824.636622, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:24] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049824.636725, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:24] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049824.636849, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:24] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049824.636954, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:24] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049824.637058, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:24] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049824.637161, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:24] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049824.637281, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:24] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049824.637382, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:24] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049824.637485, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:24] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049824.637587, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:24] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049824.637688, "xdebug_link": null, "collector": "log"}, {"message": "[15:30:24] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049824.637813, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1752049822.840695, "end": 1752049824.656313, "duration": 1.8156180381774902, "duration_str": "1.82s", "measures": [{"label": "Booting", "start": 1752049822.840695, "relative_start": 0, "end": 1752049823.387016, "relative_end": 1752049823.387016, "duration": 0.5463211536407471, "duration_str": "546ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1752049823.387041, "relative_start": 0.5463461875915527, "end": 1752049824.656317, "relative_end": 4.0531158447265625e-06, "duration": 1.2692759037017822, "duration_str": "1.27s", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 45359872, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET administrator/ordercancelhistories/datatable.json", "middleware": "web, auth, roles", "controller": "App\\Http\\Controllers\\Generator\\OrderCancelHistoryController@__datatable", "namespace": null, "prefix": "administrator/ordercancelhistories", "where": [], "as": "dashboard_ordercancelhistories_table", "file": "<a href=\"phpstorm://open?file=D:\\Topan\\cms-juraganbeku\\app\\Http\\Controllers\\Generator\\OrderCancelHistoryController.php&line=90\">\\app\\Http\\Controllers\\Generator\\OrderCancelHistoryController.php:90-93</a>"}, "queries": {"nb_statements": 47, "nb_failed_statements": 0, "accumulated_duration": 0.2351300000000001, "accumulated_duration_str": "235ms", "statements": [{"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'juragan_beku' and table_name = 'tbl_order_cancel'", "type": "query", "params": [], "bindings": ["juragan_beku", "tbl_order_cancel"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php", "line": 33}, {"index": 12, "namespace": null, "name": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php", "line": 16}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.012060000000000001, "duration_str": "12.06ms", "stmt_id": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php:33", "connection": "juragan_beku", "start_percent": 0, "width_percent": 5.129}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'juragan_beku' and table_name = 'conf_menu'", "type": "query", "params": [], "bindings": ["juragan_beku", "conf_menu"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php", "line": 33}, {"index": 12, "namespace": null, "name": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php", "line": 16}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\CoreController.php", "line": 61}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\Generator\\OrderCancelHistoryController.php", "line": 27}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}], "duration": 0.00319, "duration_str": "3.19ms", "stmt_id": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php:33", "connection": "juragan_beku", "start_percent": 5.129, "width_percent": 1.357}, {"sql": "select * from `conf_menu` where `parent_id` is null order by `menu_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\CoreController.php", "line": 61}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Generator\\OrderCancelHistoryController.php", "line": 27}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00289, "duration_str": "2.89ms", "stmt_id": "\\app\\Http\\Controllers\\CoreController.php:61", "connection": "juragan_beku", "start_percent": 6.486, "width_percent": 1.229}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'juragan_beku' and table_name = 'conf_group'", "type": "query", "params": [], "bindings": ["juragan_beku", "conf_group"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php", "line": 33}, {"index": 12, "namespace": null, "name": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php", "line": 16}, {"index": 20, "namespace": null, "name": "\\app\\Models\\Menu.php", "line": 39}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\CoreController.php", "line": 61}, {"index": 28, "namespace": null, "name": "\\app\\Http\\Controllers\\Generator\\OrderCancelHistoryController.php", "line": 27}], "duration": 0.00273, "duration_str": "2.73ms", "stmt_id": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php:33", "connection": "juragan_beku", "start_percent": 7.715, "width_percent": 1.161}, {"sql": "select `conf_group`.*, `conf_group_menu`.`menu_id` as `pivot_menu_id`, `conf_group_menu`.`group_id` as `pivot_group_id`, `conf_group_menu`.`id` as `pivot_id`, `conf_group_menu`.`is_addable` as `pivot_is_addable`, `conf_group_menu`.`is_editable` as `pivot_is_editable`, `conf_group_menu`.`is_deletable` as `pivot_is_deletable`, `conf_group_menu`.`is_viewable` as `pivot_is_viewable`, `conf_group_menu`.`created_at` as `pivot_created_at`, `conf_group_menu`.`updated_at` as `pivot_updated_at` from `conf_group` inner join `conf_group_menu` on `conf_group`.`id` = `conf_group_menu`.`group_id` where `conf_group_menu`.`deleted_at` is null and `conf_group_menu`.`menu_id` in ('12172b03-c059-47f6-8b3a-ec45fff0bfb0', '1e171ae1-4f20-47b8-898c-a3d7028a3ab8', '2412018e-b714-4252-8674-1f7ced3b4c8e', '2afdc4cd-6699-4fdb-8774-02fd3c52c7a9', '35bdff65-5393-4c72-a885-1026cdfdaed6', '3629349a-e969-47f6-ba61-2d75c110dba7', '3927daac-5667-4d64-9c1e-ba804d715ad6', '42ecc46f-d988-4079-8fcc-e80f086ae0b8', '6d3265c5-86a7-4f93-a5e5-dbacf271bf08', '6d34fee1-1985-4885-863d-a7f34134c662', '6e655b95-8adb-45e6-9bad-aedb2917aa95', '6eb47bc6-8aa1-4548-965e-b17280f8265e', '710fd92b-bb27-4596-96a1-cc4cc9d4da53', '7afebf9b-18a3-4663-945b-cbba8403c5e5', '7c8ba58d-5450-4607-859c-152617371562', '89c3ba3a-beca-4eaf-b825-9df62b18f100', '8b6c271e-5599-4599-a037-6f36cbaf5df4', '908d7755-397d-459a-8dd9-b5c1cf2933a1', '95a301cc-df41-4af8-a735-42a88252cc28', '9775dc51-db4d-4ff3-a0eb-bc02582b2519', '9963370e-03d2-47fc-bf10-d8afb45f2778', 'a663bdd0-dd56-435c-b01c-56914d355319', 'a858dc43-a5b2-47ac-8b19-2122748997d7', 'a99e9853-75af-41ad-ab42-e367c434342e', 'ab6c7821-03a9-4d1e-b62d-4aa1e75ac2bc', 'b1ce929b-2658-464d-8423-f83025eba65a', 'b6ae8a73-8645-411b-9f3a-664ca8d0d0fb', 'b88aa02c-a949-490e-82dd-dd99fde4384d', 'ba1d3b8c-b6e1-4a5c-b0ba-9fcb73895e79', 'd302114a-df52-4183-9416-17beeb3ed264', 'f8a46c4d-7463-464c-abd5-a2187680201c', 'fb06a851-d01d-4cfd-a738-fcbc90b2ea43', 'fb962710-d5f6-4ba6-aeb4-ef7a749091ed', 'fc77a5a4-0e46-4650-8ba9-a1b32a449466') and `conf_group`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["12172b03-c059-47f6-8b3a-ec45fff0bfb0", "1e171ae1-4f20-47b8-898c-a3d7028a3ab8", "2412018e-b714-4252-8674-1f7ced3b4c8e", "2afdc4cd-6699-4fdb-8774-02fd3c52c7a9", "35bdff65-5393-4c72-a885-1026cdfdaed6", "3629349a-e969-47f6-ba61-2d75c110dba7", "3927daac-5667-4d64-9c1e-ba804d715ad6", "42ecc46f-d988-4079-8fcc-e80f086ae0b8", "6d3265c5-86a7-4f93-a5e5-dbacf271bf08", "6d34fee1-1985-4885-863d-a7f34134c662", "6e655b95-8adb-45e6-9bad-aedb2917aa95", "6eb47bc6-8aa1-4548-965e-b17280f8265e", "710fd92b-bb27-4596-96a1-cc4cc9d4da53", "7afebf9b-18a3-4663-945b-cbba8403c5e5", "7c8ba58d-5450-4607-859c-152617371562", "89c3ba3a-beca-4eaf-b825-9df62b18f100", "8b6c271e-5599-4599-a037-6f36cbaf5df4", "908d7755-397d-459a-8dd9-b5c1cf2933a1", "95a301cc-df41-4af8-a735-42a88252cc28", "9775dc51-db4d-4ff3-a0eb-bc02582b2519", "9963370e-03d2-47fc-bf10-d8afb45f2778", "a663bdd0-dd56-435c-b01c-56914d355319", "a858dc43-a5b2-47ac-8b19-2122748997d7", "a99e9853-75af-41ad-ab42-e367c434342e", "ab6c7821-03a9-4d1e-b62d-4aa1e75ac2bc", "b1ce929b-2658-464d-8423-f83025eba65a", "b6ae8a73-8645-411b-9f3a-664ca8d0d0fb", "b88aa02c-a949-490e-82dd-dd99fde4384d", "ba1d3b8c-b6e1-4a5c-b0ba-9fcb73895e79", "d302114a-df52-4183-9416-17beeb3ed264", "f8a46c4d-7463-464c-abd5-a2187680201c", "fb06a851-d01d-4cfd-a738-fcbc90b2ea43", "fb962710-d5f6-4ba6-aeb4-ef7a749091ed", "fc77a5a4-0e46-4650-8ba9-a1b32a449466"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "\\app\\Http\\Controllers\\CoreController.php", "line": 61}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\Generator\\OrderCancelHistoryController.php", "line": 27}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.040479999999999995, "duration_str": "40.48ms", "stmt_id": "\\app\\Http\\Controllers\\CoreController.php:61", "connection": "juragan_beku", "start_percent": 8.876, "width_percent": 17.216}, {"sql": "select * from `conf_menu` where `conf_menu`.`parent_id` in ('12172b03-c059-47f6-8b3a-ec45fff0bfb0', '1e171ae1-4f20-47b8-898c-a3d7028a3ab8', '2412018e-b714-4252-8674-1f7ced3b4c8e', '2afdc4cd-6699-4fdb-8774-02fd3c52c7a9', '35bdff65-5393-4c72-a885-1026cdfdaed6', '3629349a-e969-47f6-ba61-2d75c110dba7', '3927daac-5667-4d64-9c1e-ba804d715ad6', '42ecc46f-d988-4079-8fcc-e80f086ae0b8', '6d3265c5-86a7-4f93-a5e5-dbacf271bf08', '6d34fee1-1985-4885-863d-a7f34134c662', '6e655b95-8adb-45e6-9bad-aedb2917aa95', '6eb47bc6-8aa1-4548-965e-b17280f8265e', '710fd92b-bb27-4596-96a1-cc4cc9d4da53', '7afebf9b-18a3-4663-945b-cbba8403c5e5', '7c8ba58d-5450-4607-859c-152617371562', '89c3ba3a-beca-4eaf-b825-9df62b18f100', '8b6c271e-5599-4599-a037-6f36cbaf5df4', '908d7755-397d-459a-8dd9-b5c1cf2933a1', '95a301cc-df41-4af8-a735-42a88252cc28', '9775dc51-db4d-4ff3-a0eb-bc02582b2519', '9963370e-03d2-47fc-bf10-d8afb45f2778', 'a663bdd0-dd56-435c-b01c-56914d355319', 'a858dc43-a5b2-47ac-8b19-2122748997d7', 'a99e9853-75af-41ad-ab42-e367c434342e', 'ab6c7821-03a9-4d1e-b62d-4aa1e75ac2bc', 'b1ce929b-2658-464d-8423-f83025eba65a', 'b6ae8a73-8645-411b-9f3a-664ca8d0d0fb', 'b88aa02c-a949-490e-82dd-dd99fde4384d', 'ba1d3b8c-b6e1-4a5c-b0ba-9fcb73895e79', 'd302114a-df52-4183-9416-17beeb3ed264', 'f8a46c4d-7463-464c-abd5-a2187680201c', 'fb06a851-d01d-4cfd-a738-fcbc90b2ea43', 'fb962710-d5f6-4ba6-aeb4-ef7a749091ed', 'fc77a5a4-0e46-4650-8ba9-a1b32a449466') and `conf_menu`.`deleted_at` is null order by `menu_order` asc", "type": "query", "params": [], "bindings": ["12172b03-c059-47f6-8b3a-ec45fff0bfb0", "1e171ae1-4f20-47b8-898c-a3d7028a3ab8", "2412018e-b714-4252-8674-1f7ced3b4c8e", "2afdc4cd-6699-4fdb-8774-02fd3c52c7a9", "35bdff65-5393-4c72-a885-1026cdfdaed6", "3629349a-e969-47f6-ba61-2d75c110dba7", "3927daac-5667-4d64-9c1e-ba804d715ad6", "42ecc46f-d988-4079-8fcc-e80f086ae0b8", "6d3265c5-86a7-4f93-a5e5-dbacf271bf08", "6d34fee1-1985-4885-863d-a7f34134c662", "6e655b95-8adb-45e6-9bad-aedb2917aa95", "6eb47bc6-8aa1-4548-965e-b17280f8265e", "710fd92b-bb27-4596-96a1-cc4cc9d4da53", "7afebf9b-18a3-4663-945b-cbba8403c5e5", "7c8ba58d-5450-4607-859c-152617371562", "89c3ba3a-beca-4eaf-b825-9df62b18f100", "8b6c271e-5599-4599-a037-6f36cbaf5df4", "908d7755-397d-459a-8dd9-b5c1cf2933a1", "95a301cc-df41-4af8-a735-42a88252cc28", "9775dc51-db4d-4ff3-a0eb-bc02582b2519", "9963370e-03d2-47fc-bf10-d8afb45f2778", "a663bdd0-dd56-435c-b01c-56914d355319", "a858dc43-a5b2-47ac-8b19-2122748997d7", "a99e9853-75af-41ad-ab42-e367c434342e", "ab6c7821-03a9-4d1e-b62d-4aa1e75ac2bc", "b1ce929b-2658-464d-8423-f83025eba65a", "b6ae8a73-8645-411b-9f3a-664ca8d0d0fb", "b88aa02c-a949-490e-82dd-dd99fde4384d", "ba1d3b8c-b6e1-4a5c-b0ba-9fcb73895e79", "d302114a-df52-4183-9416-17beeb3ed264", "f8a46c4d-7463-464c-abd5-a2187680201c", "fb06a851-d01d-4cfd-a738-fcbc90b2ea43", "fb962710-d5f6-4ba6-aeb4-ef7a749091ed", "fc77a5a4-0e46-4650-8ba9-a1b32a449466"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\CoreController.php", "line": 61}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\Generator\\OrderCancelHistoryController.php", "line": 27}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00382, "duration_str": "3.82ms", "stmt_id": "\\app\\Http\\Controllers\\CoreController.php:61", "connection": "juragan_beku", "start_percent": 26.092, "width_percent": 1.625}, {"sql": "select `conf_group`.*, `conf_group_menu`.`menu_id` as `pivot_menu_id`, `conf_group_menu`.`group_id` as `pivot_group_id`, `conf_group_menu`.`id` as `pivot_id`, `conf_group_menu`.`is_addable` as `pivot_is_addable`, `conf_group_menu`.`is_editable` as `pivot_is_editable`, `conf_group_menu`.`is_deletable` as `pivot_is_deletable`, `conf_group_menu`.`is_viewable` as `pivot_is_viewable`, `conf_group_menu`.`created_at` as `pivot_created_at`, `conf_group_menu`.`updated_at` as `pivot_updated_at` from `conf_group` inner join `conf_group_menu` on `conf_group`.`id` = `conf_group_menu`.`group_id` where `conf_group_menu`.`deleted_at` is null and `conf_group_menu`.`menu_id` in ('01dcecc0-c805-4bfe-a8ad-f523d7fffa26', '04bc1af2-bad8-468b-84c8-0c47a89bae8c', '04e863de-047a-4d8e-973c-e789797e4a4f', '05425918-a3ef-4756-88a2-e907ca98a81e', '09af7259-5b37-4716-87dd-b8d1fd422e63', '0a347679-1d34-4bd5-8c36-eae1fe1c916c', '0a5aaeef-b6cc-4672-b533-7026809842dd', '0c919881-3ab7-40e6-b446-ab9c57998718', '0ddb84c4-da19-4449-9528-dafa05bc0504', '0f3d0fc9-fe09-490b-bb11-207f4948af0a', '163ae8f3-3516-4e53-938e-287538f37482', '1b656c21-3078-499f-b44a-d8f7a21eb29f', '1b705957-9f8a-4bed-a7b6-855a26a45576', '1ced30ab-5fa2-40f2-ba6d-f7a038b61983', '1e372edd-6218-4752-89fd-4507f1078de1', '2b93aa72-d810-46ea-9679-957fe4f5d997', '2e51431a-b769-4d0d-a627-d944f0bd2957', '30c7e095-e70e-4266-814f-cc421663c6e6', '354a271e-7e20-40fc-ba5a-1f23db8c00f5', '3aeb613c-d5c4-448c-b376-503252c7aa48', '3d8ae353-3736-40b9-bb1b-44b60b67eb1c', '3ec2b653-f948-46a8-8c57-aa28c8435923', '40904c07-fe95-4a4f-93e2-4ca303de8647', '41646f45-ea80-4049-8d6b-afd1ebbac87c', '426eedee-9489-48b4-93f7-c6e591342236', '4296cf9b-3658-4625-842a-5b66171da48b', '435cce6d-9645-4243-814e-c9918615d5e4', '47b4ee0e-8478-464e-acf8-c3d7c612113c', '48092a19-ba5a-4dad-b20f-5cb59aedc777', '4975676e-a170-44c6-b3ad-4304a9be5eb8', '4d9d0b21-b8b2-4abf-ac97-1a9368604300', '4ecdb1cd-aeb4-4fd0-8485-338b7439dc9d', '4f1223e9-a91b-433d-ac70-e30bec7566ad', '536ec0f4-7462-4ceb-9b39-802455cc5764', '5874e370-59e5-4e6a-8e41-52546e466492', '59b49c35-3850-4a9d-8635-d31c77e3f4bc', '5af03417-8b42-4c89-bdfb-7110826d1441', '5d2d475f-60d4-4d60-978c-cfabae2cf488', '5e8771fe-bb86-438b-b196-c4da91e6cd10', '66da0bfd-7aa5-40f6-a76c-24194c4e786a', '6725a57f-7506-43df-92a6-9b29421b93b8', '6bb01a1d-2ec1-46c9-8b6f-47b9beb76e96', '6d1dce4c-9857-41f4-84a7-f469f554b804', '6eda8f6d-c16f-4137-8142-d8c6e942d8c7', '70fbab85-b7ff-4e67-8474-14459141bb23', '713480b2-6382-4387-8d29-31a3f1f103a2', '714c0690-9a04-401b-b80b-48ed26a1c645', '74c092e4-f8bb-4d6f-996d-b1c88406adf7', '75e3e698-2b7b-4499-b449-feca72328d2e', '7d9f2c14-aea1-4d6b-8a11-42d0a619f292', '7e61030c-af9c-4edd-984b-4c414fc05b45', '7ff92f91-6239-4529-84cb-c510eaaf7394', '81212dac-52a2-4f5a-9d02-985d5b0b811c', '814fc401-5b17-4101-9816-5bab2bd106b6', '81a05ce5-b3e7-460e-b679-92eb66688c00', '835ccc11-1949-4614-a50e-577c14a03347', '843d55ee-7c77-451f-88c1-e30f3b0248e4', '84aa075a-cbf0-4863-93a0-a2659a2cd8ed', '86110001-4b90-4979-923f-9de27628d239', '88df6019-8237-471b-a436-83cc1cb0056a', '8a7c26db-38c1-4dba-8d0a-e3b76201abc8', '8c0247b3-7f55-453d-91a5-9c5b41a94b94', '8e781df6-5a3f-4f0b-bea1-865c4a659a9b', '9297b20b-2f4f-44cc-b6dc-19b46c15f549', '92fa1318-1a87-4ecf-871e-293596788039', '9c7a42d5-daf8-48fe-aa83-fa1e5c2a857b', '9e8352da-5e4d-49be-910e-1ffc6cfa909f', '9faed98a-be0f-41a8-b766-12bf76d40a22', 'a022819b-21cf-450c-93a5-6ec9841d2743', 'a15ff6a6-42e8-4a60-bdbb-ea80f41a9dac', 'a79cf485-f37f-4328-b89b-b119339398ef', 'a987c846-5c72-447b-a68c-733498ed977a', 'acd0dd1c-a5b0-4049-affe-e95ebb296478', 'ae8388f0-5f1e-4723-8de6-e708293ec9c4', 'aecd278f-abed-4f66-b295-c7aa1b078e27', 'b0bd9344-18bf-4960-9eef-bed0b2da1546', 'b1a17b3b-208f-4345-810c-ece101c456a7', 'b58b7b6f-5c00-4977-9fbd-076996aab6d0', 'b948eccc-ac77-4cca-bcbf-cb22e39fe1c1', 'bb734894-6d11-465f-b58f-fbdbbcd3f210', 'c02e2a45-bc33-40ea-88ca-8fb995680830', 'c15fc73a-270c-4044-92d1-154d10a4e503', 'c28d32ca-ae7f-44be-8058-ebddf4cea773', 'c29e03e8-4c41-4f25-9d45-ca2ee037d2a8', 'c43d7685-d7fa-45c2-bc4d-bc9242d4b688', 'c45901c9-5b09-4904-bf85-8030ba2022ed', 'c56a1871-b751-4414-8b30-1614d1eb01b8', 'c6f57b96-9ce4-4f4c-9e7a-e673aa3ba05d', 'c8562214-65c1-4aaa-a7a5-90e8d247bf04', 'c98db184-64a9-448e-b4b5-f8f94e6bb3f6', 'c9961577-ba7a-4723-bfc8-66e0227d9655', 'cace2334-2bf3-448c-b648-076e1924d4d5', 'cb56118d-3ec1-45ff-9d02-90afacb29444', 'cd3961dc-f993-46e4-b8bb-e2f95b366f7c', 'd75631a4-7999-4d30-820e-68ee58aff55b', 'e0a4f8a8-7ea4-4f50-aed0-e8c6cde2f477', 'e0f71c5e-1081-472e-bff8-d696445331fe', 'e32d1b6a-6991-4822-a4b6-367d023be2ef', 'e3408275-6056-4c66-9fda-50d2c505f806', 'e6d88533-cf7f-4ef3-b23a-caeaa0c2c08f', 'e8895d0e-590b-445b-ae8d-1bad4f81180f', 'eadc51c2-6e22-401f-8780-2d898a8db951', 'f392d4c0-6807-41ff-81a8-6c82e8753c89', 'f4cc69d3-6dcc-4dc0-ada2-5c11b4253391', 'f594b66d-2f6d-4f16-8a4e-8c81539be1d8', 'fb3a59bb-2e5b-423f-aae6-2b54a491cdb7', 'fb3f014b-7f39-4ffe-a02a-86d490496ee4', 'fc6d2d25-de43-4ec1-bb16-df8a56986091', 'fc7448fc-3f39-4894-a8e3-d194c63ee73e', 'fe956fd4-b5ee-4b7d-a33f-e9e56328e493') and `conf_group`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["01dcecc0-c805-4bfe-a8ad-f523d7fffa26", "04bc1af2-bad8-468b-84c8-0c47a89bae8c", "04e863de-047a-4d8e-973c-e789797e4a4f", "05425918-a3ef-4756-88a2-e907ca98a81e", "09af7259-5b37-4716-87dd-b8d1fd422e63", "0a347679-1d34-4bd5-8c36-eae1fe1c916c", "0a5aaeef-b6cc-4672-b533-7026809842dd", "0c919881-3ab7-40e6-b446-ab9c57998718", "0ddb84c4-da19-4449-9528-dafa05bc0504", "0f3d0fc9-fe09-490b-bb11-207f4948af0a", "163ae8f3-3516-4e53-938e-287538f37482", "1b656c21-3078-499f-b44a-d8f7a21eb29f", "1b705957-9f8a-4bed-a7b6-855a26a45576", "1ced30ab-5fa2-40f2-ba6d-f7a038b61983", "1e372edd-6218-4752-89fd-4507f1078de1", "2b93aa72-d810-46ea-9679-957fe4f5d997", "2e51431a-b769-4d0d-a627-d944f0bd2957", "30c7e095-e70e-4266-814f-cc421663c6e6", "354a271e-7e20-40fc-ba5a-1f23db8c00f5", "3aeb613c-d5c4-448c-b376-503252c7aa48", "3d8ae353-3736-40b9-bb1b-44b60b67eb1c", "3ec2b653-f948-46a8-8c57-aa28c8435923", "40904c07-fe95-4a4f-93e2-4ca303de8647", "41646f45-ea80-4049-8d6b-afd1ebbac87c", "426eedee-9489-48b4-93f7-c6e591342236", "4296cf9b-3658-4625-842a-5b66171da48b", "435cce6d-9645-4243-814e-c9918615d5e4", "47b4ee0e-8478-464e-acf8-c3d7c612113c", "48092a19-ba5a-4dad-b20f-5cb59aedc777", "4975676e-a170-44c6-b3ad-4304a9be5eb8", "4d9d0b21-b8b2-4abf-ac97-1a9368604300", "4ecdb1cd-aeb4-4fd0-8485-338b7439dc9d", "4f1223e9-a91b-433d-ac70-e30bec7566ad", "536ec0f4-7462-4ceb-9b39-802455cc5764", "5874e370-59e5-4e6a-8e41-52546e466492", "59b49c35-3850-4a9d-8635-d31c77e3f4bc", "5af03417-8b42-4c89-bdfb-7110826d1441", "5d2d475f-60d4-4d60-978c-cfabae2cf488", "5e8771fe-bb86-438b-b196-c4da91e6cd10", "66da0bfd-7aa5-40f6-a76c-24194c4e786a", "6725a57f-7506-43df-92a6-9b29421b93b8", "6bb01a1d-2ec1-46c9-8b6f-47b9beb76e96", "6d1dce4c-9857-41f4-84a7-f469f554b804", "6eda8f6d-c16f-4137-8142-d8c6e942d8c7", "70fbab85-b7ff-4e67-8474-14459141bb23", "713480b2-6382-4387-8d29-31a3f1f103a2", "714c0690-9a04-401b-b80b-48ed26a1c645", "74c092e4-f8bb-4d6f-996d-b1c88406adf7", "75e3e698-2b7b-4499-b449-feca72328d2e", "7d9f2c14-aea1-4d6b-8a11-42d0a619f292", "7e61030c-af9c-4edd-984b-4c414fc05b45", "7ff92f91-6239-4529-84cb-c510eaaf7394", "81212dac-52a2-4f5a-9d02-985d5b0b811c", "814fc401-5b17-4101-9816-5bab2bd106b6", "81a05ce5-b3e7-460e-b679-92eb66688c00", "835ccc11-1949-4614-a50e-577c14a03347", "843d55ee-7c77-451f-88c1-e30f3b0248e4", "84aa075a-cbf0-4863-93a0-a2659a2cd8ed", "86110001-4b90-4979-923f-9de27628d239", "88df6019-8237-471b-a436-83cc1cb0056a", "8a7c26db-38c1-4dba-8d0a-e3b76201abc8", "8c0247b3-7f55-453d-91a5-9c5b41a94b94", "8e781df6-5a3f-4f0b-bea1-865c4a659a9b", "9297b20b-2f4f-44cc-b6dc-19b46c15f549", "92fa1318-1a87-4ecf-871e-293596788039", "9c7a42d5-daf8-48fe-aa83-fa1e5c2a857b", "9e8352da-5e4d-49be-910e-1ffc6cfa909f", "9faed98a-be0f-41a8-b766-12bf76d40a22", "a022819b-21cf-450c-93a5-6ec9841d2743", "a15ff6a6-42e8-4a60-bdbb-ea80f41a9dac", "a79cf485-f37f-4328-b89b-b119339398ef", "a987c846-5c72-447b-a68c-733498ed977a", "acd0dd1c-a5b0-4049-affe-e95ebb296478", "ae8388f0-5f1e-4723-8de6-e708293ec9c4", "aecd278f-abed-4f66-b295-c7aa1b078e27", "b0bd9344-18bf-4960-9eef-bed0b2da1546", "b1a17b3b-208f-4345-810c-ece101c456a7", "b58b7b6f-5c00-4977-9fbd-076996aab6d0", "b948eccc-ac77-4cca-bcbf-cb22e39fe1c1", "bb734894-6d11-465f-b58f-fbdbbcd3f210", "c02e2a45-bc33-40ea-88ca-8fb995680830", "c15fc73a-270c-4044-92d1-154d10a4e503", "c28d32ca-ae7f-44be-8058-ebddf4cea773", "c29e03e8-4c41-4f25-9d45-ca2ee037d2a8", "c43d7685-d7fa-45c2-bc4d-bc9242d4b688", "c45901c9-5b09-4904-bf85-8030ba2022ed", "c56a1871-b751-4414-8b30-1614d1eb01b8", "c6f57b96-9ce4-4f4c-9e7a-e673aa3ba05d", "c8562214-65c1-4aaa-a7a5-90e8d247bf04", "c98db184-64a9-448e-b4b5-f8f94e6bb3f6", "c9961577-ba7a-4723-bfc8-66e0227d9655", "cace2334-2bf3-448c-b648-076e1924d4d5", "cb56118d-3ec1-45ff-9d02-90afacb29444", "cd3961dc-f993-46e4-b8bb-e2f95b366f7c", "d75631a4-7999-4d30-820e-68ee58aff55b", "e0a4f8a8-7ea4-4f50-aed0-e8c6cde2f477", "e0f71c5e-1081-472e-bff8-d696445331fe", "e32d1b6a-6991-4822-a4b6-367d023be2ef", "e3408275-6056-4c66-9fda-50d2c505f806", "e6d88533-cf7f-4ef3-b23a-caeaa0c2c08f", "e8895d0e-590b-445b-ae8d-1bad4f81180f", "eadc51c2-6e22-401f-8780-2d898a8db951", "f392d4c0-6807-41ff-81a8-6c82e8753c89", "f4cc69d3-6dcc-4dc0-ada2-5c11b4253391", "f594b66d-2f6d-4f16-8a4e-8c81539be1d8", "fb3a59bb-2e5b-423f-aae6-2b54a491cdb7", "fb3f014b-7f39-4ffe-a02a-86d490496ee4", "fc6d2d25-de43-4ec1-bb16-df8a56986091", "fc7448fc-3f39-4894-a8e3-d194c63ee73e", "fe956fd4-b5ee-4b7d-a33f-e9e56328e493"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "\\app\\Http\\Controllers\\CoreController.php", "line": 61}, {"index": 24, "namespace": null, "name": "\\app\\Http\\Controllers\\Generator\\OrderCancelHistoryController.php", "line": 27}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.05246, "duration_str": "52.46ms", "stmt_id": "\\app\\Http\\Controllers\\CoreController.php:61", "connection": "juragan_beku", "start_percent": 27.717, "width_percent": 22.311}, {"sql": "select * from `conf_menu` where `route_name` = 'dashboard_ordercancelhistories_table' limit 1", "type": "query", "params": [], "bindings": ["dashboard_ordercancelhistories_table"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 23}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Controllers\\Generator\\OrderCancelHistoryController.php", "line": 27}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.0015, "duration_str": "1.5ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:23", "connection": "juragan_beku", "start_percent": 50.028, "width_percent": 0.638}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'juragan_beku' and table_name = 'conf_setting'", "type": "query", "params": [], "bindings": ["juragan_beku", "conf_setting"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php", "line": 33}, {"index": 12, "namespace": null, "name": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php", "line": 16}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\CoreController.php", "line": 25}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\Generator\\OrderCancelHistoryController.php", "line": 30}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}], "duration": 0.00254, "duration_str": "2.54ms", "stmt_id": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php:33", "connection": "juragan_beku", "start_percent": 50.666, "width_percent": 1.08}, {"sql": "select * from `conf_setting` where `conf_setting`.`deleted_at` is null and `parameter` in ('logo', 'app_name', 'app_name_short', 'footer', 'logo_icon')", "type": "query", "params": [], "bindings": ["logo", "app_name", "app_name_short", "footer", "logo_icon"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\CoreController.php", "line": 25}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Generator\\OrderCancelHistoryController.php", "line": 30}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00138, "duration_str": "1.38ms", "stmt_id": "\\app\\Http\\Controllers\\CoreController.php:25", "connection": "juragan_beku", "start_percent": 51.746, "width_percent": 0.587}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'juragan_beku' and table_name = 'conf_users'", "type": "query", "params": [], "bindings": ["juragan_beku", "conf_users"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php", "line": 33}, {"index": 12, "namespace": null, "name": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php", "line": 16}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 185}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 49}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}], "duration": 0.00181, "duration_str": "1.81ms", "stmt_id": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php:33", "connection": "juragan_beku", "start_percent": 52.333, "width_percent": 0.77}, {"sql": "select * from `conf_users` where `id` = 'c065dc93-6b2f-4bf5-b746-6bb48713d4bf' and `conf_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["c065dc93-6b2f-4bf5-b746-6bb48713d4bf"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00116, "duration_str": "1.16ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "juragan_beku", "start_percent": 53.103, "width_percent": 0.493}, {"sql": "select * from `conf_menu` where `conf_menu`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": "middleware", "name": "roles", "line": 23}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00328, "duration_str": "3.28ms", "stmt_id": "middleware::roles:23", "connection": "juragan_beku", "start_percent": 53.596, "width_percent": 1.395}, {"sql": "select `conf_group`.*, `conf_group_menu`.`menu_id` as `pivot_menu_id`, `conf_group_menu`.`group_id` as `pivot_group_id`, `conf_group_menu`.`id` as `pivot_id`, `conf_group_menu`.`is_addable` as `pivot_is_addable`, `conf_group_menu`.`is_editable` as `pivot_is_editable`, `conf_group_menu`.`is_deletable` as `pivot_is_deletable`, `conf_group_menu`.`is_viewable` as `pivot_is_viewable`, `conf_group_menu`.`created_at` as `pivot_created_at`, `conf_group_menu`.`updated_at` as `pivot_updated_at` from `conf_group` inner join `conf_group_menu` on `conf_group`.`id` = `conf_group_menu`.`group_id` where `conf_group_menu`.`deleted_at` is null and `group_id` = 'ff9759b5-2dc5-410b-b86c-f8146c1f66d8' and `conf_group_menu`.`menu_id` in ('01dcecc0-c805-4bfe-a8ad-f523d7fffa26', '04bc1af2-bad8-468b-84c8-0c47a89bae8c', '04e863de-047a-4d8e-973c-e789797e4a4f', '05425918-a3ef-4756-88a2-e907ca98a81e', '09af7259-5b37-4716-87dd-b8d1fd422e63', '0a347679-1d34-4bd5-8c36-eae1fe1c916c', '0a5aaeef-b6cc-4672-b533-7026809842dd', '0c919881-3ab7-40e6-b446-ab9c57998718', '0ddb84c4-da19-4449-9528-dafa05bc0504', '0f3d0fc9-fe09-490b-bb11-207f4948af0a', '12172b03-c059-47f6-8b3a-ec45fff0bfb0', '163ae8f3-3516-4e53-938e-287538f37482', '1b656c21-3078-499f-b44a-d8f7a21eb29f', '1b705957-9f8a-4bed-a7b6-855a26a45576', '1ced30ab-5fa2-40f2-ba6d-f7a038b61983', '1e171ae1-4f20-47b8-898c-a3d7028a3ab8', '1e372edd-6218-4752-89fd-4507f1078de1', '2412018e-b714-4252-8674-1f7ced3b4c8e', '2afdc4cd-6699-4fdb-8774-02fd3c52c7a9', '2b93aa72-d810-46ea-9679-957fe4f5d997', '2e51431a-b769-4d0d-a627-d944f0bd2957', '30c7e095-e70e-4266-814f-cc421663c6e6', '354a271e-7e20-40fc-ba5a-1f23db8c00f5', '35bdff65-5393-4c72-a885-1026cdfdaed6', '3629349a-e969-47f6-ba61-2d75c110dba7', '3927daac-5667-4d64-9c1e-ba804d715ad6', '3aeb613c-d5c4-448c-b376-503252c7aa48', '3d8ae353-3736-40b9-bb1b-44b60b67eb1c', '3ec2b653-f948-46a8-8c57-aa28c8435923', '40904c07-fe95-4a4f-93e2-4ca303de8647', '41646f45-ea80-4049-8d6b-afd1ebbac87c', '426eedee-9489-48b4-93f7-c6e591342236', '4296cf9b-3658-4625-842a-5b66171da48b', '42ecc46f-d988-4079-8fcc-e80f086ae0b8', '435cce6d-9645-4243-814e-c9918615d5e4', '4705379c-5fb2-4c32-9cd0-f8510de5f8c1', '47b4ee0e-8478-464e-acf8-c3d7c612113c', '48092a19-ba5a-4dad-b20f-5cb59aedc777', '4975676e-a170-44c6-b3ad-4304a9be5eb8', '4d9d0b21-b8b2-4abf-ac97-1a9368604300', '4ecdb1cd-aeb4-4fd0-8485-338b7439dc9d', '4f1223e9-a91b-433d-ac70-e30bec7566ad', '536ec0f4-7462-4ceb-9b39-802455cc5764', '5874e370-59e5-4e6a-8e41-52546e466492', '59b49c35-3850-4a9d-8635-d31c77e3f4bc', '5af03417-8b42-4c89-bdfb-7110826d1441', '5d2d475f-60d4-4d60-978c-cfabae2cf488', '5e8771fe-bb86-438b-b196-c4da91e6cd10', '66da0bfd-7aa5-40f6-a76c-24194c4e786a', '6725a57f-7506-43df-92a6-9b29421b93b8', '6bb01a1d-2ec1-46c9-8b6f-47b9beb76e96', '6d1dce4c-9857-41f4-84a7-f469f554b804', '6d3265c5-86a7-4f93-a5e5-dbacf271bf08', '6d34fee1-1985-4885-863d-a7f34134c662', '6e655b95-8adb-45e6-9bad-aedb2917aa95', '6eb47bc6-8aa1-4548-965e-b17280f8265e', '6eda8f6d-c16f-4137-8142-d8c6e942d8c7', '70fbab85-b7ff-4e67-8474-14459141bb23', '710fd92b-bb27-4596-96a1-cc4cc9d4da53', '713480b2-6382-4387-8d29-31a3f1f103a2', '714c0690-9a04-401b-b80b-48ed26a1c645', '74c092e4-f8bb-4d6f-996d-b1c88406adf7', '751def3a-b03e-44d2-83d3-bc5c4df78c9d', '75e3e698-2b7b-4499-b449-feca72328d2e', '7afebf9b-18a3-4663-945b-cbba8403c5e5', '7c8ba58d-5450-4607-859c-152617371562', '7d9f2c14-aea1-4d6b-8a11-42d0a619f292', '7e61030c-af9c-4edd-984b-4c414fc05b45', '7ff92f91-6239-4529-84cb-c510eaaf7394', '81212dac-52a2-4f5a-9d02-985d5b0b811c', '814fc401-5b17-4101-9816-5bab2bd106b6', '81a05ce5-b3e7-460e-b679-92eb66688c00', '835ccc11-1949-4614-a50e-577c14a03347', '843d55ee-7c77-451f-88c1-e30f3b0248e4', '84aa075a-cbf0-4863-93a0-a2659a2cd8ed', '86110001-4b90-4979-923f-9de27628d239', '88df6019-8237-471b-a436-83cc1cb0056a', '89c3ba3a-beca-4eaf-b825-9df62b18f100', '8a7c26db-38c1-4dba-8d0a-e3b76201abc8', '8b6c271e-5599-4599-a037-6f36cbaf5df4', '8c0247b3-7f55-453d-91a5-9c5b41a94b94', '8e781df6-5a3f-4f0b-bea1-865c4a659a9b', '908d7755-397d-459a-8dd9-b5c1cf2933a1', '9297b20b-2f4f-44cc-b6dc-19b46c15f549', '92fa1318-1a87-4ecf-871e-293596788039', '95a301cc-df41-4af8-a735-42a88252cc28', '9775dc51-db4d-4ff3-a0eb-bc02582b2519', '9963370e-03d2-47fc-bf10-d8afb45f2777', '9963370e-03d2-47fc-bf10-d8afb45f2778', '9c7a42d5-daf8-48fe-aa83-fa1e5c2a857b', '9e8352da-5e4d-49be-910e-1ffc6cfa909f', '9faed98a-be0f-41a8-b766-12bf76d40a22', 'a022819b-21cf-450c-93a5-6ec9841d2743', 'a15ff6a6-42e8-4a60-bdbb-ea80f41a9dac', 'a663bdd0-dd56-435c-b01c-56914d355319', 'a79cf485-f37f-4328-b89b-b119339398ef', 'a858dc43-a5b2-47ac-8b19-2122748997d7', 'a987c846-5c72-447b-a68c-733498ed977a', 'a99e9853-75af-41ad-ab42-e367c434342e', 'ab6c7821-03a9-4d1e-b62d-4aa1e75ac2bc', 'acd0dd1c-a5b0-4049-affe-e95ebb296478', 'ae8388f0-5f1e-4723-8de6-e708293ec9c4', 'aecd278f-abed-4f66-b295-c7aa1b078e27', 'b0bd9344-18bf-4960-9eef-bed0b2da1546', 'b1a17b3b-208f-4345-810c-ece101c456a7', 'b1ce929b-2658-464d-8423-f83025eba65a', 'b58b7b6f-5c00-4977-9fbd-076996aab6d0', 'b6ae8a73-8645-411b-9f3a-664ca8d0d0fb', 'b88aa02c-a949-490e-82dd-dd99fde4384d', 'b948eccc-ac77-4cca-bcbf-cb22e39fe1c1', 'ba1d3b8c-b6e1-4a5c-b0ba-9fcb73895e79', 'bb734894-6d11-465f-b58f-fbdbbcd3f210', 'c02e2a45-bc33-40ea-88ca-8fb995680830', 'c15fc73a-270c-4044-92d1-154d10a4e503', 'c28d32ca-ae7f-44be-8058-ebddf4cea773', 'c29e03e8-4c41-4f25-9d45-ca2ee037d2a8', 'c43d7685-d7fa-45c2-bc4d-bc9242d4b688', 'c45901c9-5b09-4904-bf85-8030ba2022ed', 'c56a1871-b751-4414-8b30-1614d1eb01b8', 'c6f57b96-9ce4-4f4c-9e7a-e673aa3ba05d', 'c8562214-65c1-4aaa-a7a5-90e8d247bf04', 'c98db184-64a9-448e-b4b5-f8f94e6bb3f6', 'c9961577-ba7a-4723-bfc8-66e0227d9655', 'cace2334-2bf3-448c-b648-076e1924d4d5', 'cb56118d-3ec1-45ff-9d02-90afacb29444', 'cd3961dc-f993-46e4-b8bb-e2f95b366f7c', 'd302114a-df52-4183-9416-17beeb3ed264', 'd75631a4-7999-4d30-820e-68ee58aff55b', 'e0a4f8a8-7ea4-4f50-aed0-e8c6cde2f477', 'e0f71c5e-1081-472e-bff8-d696445331fe', 'e32d1b6a-6991-4822-a4b6-367d023be2ef', 'e3408275-6056-4c66-9fda-50d2c505f806', 'e6d88533-cf7f-4ef3-b23a-caeaa0c2c08f', 'e8895d0e-590b-445b-ae8d-1bad4f81180f', 'eadc51c2-6e22-401f-8780-2d898a8db951', 'f392d4c0-6807-41ff-81a8-6c82e8753c89', 'f4cc69d3-6dcc-4dc0-ada2-5c11b4253391', 'f4cc69d3-6dcc-4dc0-ada2-5c11b4253392', 'f594b66d-2f6d-4f16-8a4e-8c81539be1d8', 'f8a46c4d-7463-464c-abd5-a2187680201c', 'fb06a851-d01d-4cfd-a738-fcbc90b2ea43', 'fb3a59bb-2e5b-423f-aae6-2b54a491cdb7', 'fb3f014b-7f39-4ffe-a02a-86d490496ee4', 'fb962710-d5f6-4ba6-aeb4-ef7a749091ed', 'fc6d2d25-de43-4ec1-bb16-df8a56986091', 'fc7448fc-3f39-4894-a8e3-d194c63ee73e', 'fc77a5a4-0e46-4650-8ba9-a1b32a449466', 'fe956fd4-b5ee-4b7d-a33f-e9e56328e493') and `conf_group`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["ff9759b5-2dc5-410b-b86c-f8146c1f66d8", "01dcecc0-c805-4bfe-a8ad-f523d7fffa26", "04bc1af2-bad8-468b-84c8-0c47a89bae8c", "04e863de-047a-4d8e-973c-e789797e4a4f", "05425918-a3ef-4756-88a2-e907ca98a81e", "09af7259-5b37-4716-87dd-b8d1fd422e63", "0a347679-1d34-4bd5-8c36-eae1fe1c916c", "0a5aaeef-b6cc-4672-b533-7026809842dd", "0c919881-3ab7-40e6-b446-ab9c57998718", "0ddb84c4-da19-4449-9528-dafa05bc0504", "0f3d0fc9-fe09-490b-bb11-207f4948af0a", "12172b03-c059-47f6-8b3a-ec45fff0bfb0", "163ae8f3-3516-4e53-938e-287538f37482", "1b656c21-3078-499f-b44a-d8f7a21eb29f", "1b705957-9f8a-4bed-a7b6-855a26a45576", "1ced30ab-5fa2-40f2-ba6d-f7a038b61983", "1e171ae1-4f20-47b8-898c-a3d7028a3ab8", "1e372edd-6218-4752-89fd-4507f1078de1", "2412018e-b714-4252-8674-1f7ced3b4c8e", "2afdc4cd-6699-4fdb-8774-02fd3c52c7a9", "2b93aa72-d810-46ea-9679-957fe4f5d997", "2e51431a-b769-4d0d-a627-d944f0bd2957", "30c7e095-e70e-4266-814f-cc421663c6e6", "354a271e-7e20-40fc-ba5a-1f23db8c00f5", "35bdff65-5393-4c72-a885-1026cdfdaed6", "3629349a-e969-47f6-ba61-2d75c110dba7", "3927daac-5667-4d64-9c1e-ba804d715ad6", "3aeb613c-d5c4-448c-b376-503252c7aa48", "3d8ae353-3736-40b9-bb1b-44b60b67eb1c", "3ec2b653-f948-46a8-8c57-aa28c8435923", "40904c07-fe95-4a4f-93e2-4ca303de8647", "41646f45-ea80-4049-8d6b-afd1ebbac87c", "426eedee-9489-48b4-93f7-c6e591342236", "4296cf9b-3658-4625-842a-5b66171da48b", "42ecc46f-d988-4079-8fcc-e80f086ae0b8", "435cce6d-9645-4243-814e-c9918615d5e4", "4705379c-5fb2-4c32-9cd0-f8510de5f8c1", "47b4ee0e-8478-464e-acf8-c3d7c612113c", "48092a19-ba5a-4dad-b20f-5cb59aedc777", "4975676e-a170-44c6-b3ad-4304a9be5eb8", "4d9d0b21-b8b2-4abf-ac97-1a9368604300", "4ecdb1cd-aeb4-4fd0-8485-338b7439dc9d", "4f1223e9-a91b-433d-ac70-e30bec7566ad", "536ec0f4-7462-4ceb-9b39-802455cc5764", "5874e370-59e5-4e6a-8e41-52546e466492", "59b49c35-3850-4a9d-8635-d31c77e3f4bc", "5af03417-8b42-4c89-bdfb-7110826d1441", "5d2d475f-60d4-4d60-978c-cfabae2cf488", "5e8771fe-bb86-438b-b196-c4da91e6cd10", "66da0bfd-7aa5-40f6-a76c-24194c4e786a", "6725a57f-7506-43df-92a6-9b29421b93b8", "6bb01a1d-2ec1-46c9-8b6f-47b9beb76e96", "6d1dce4c-9857-41f4-84a7-f469f554b804", "6d3265c5-86a7-4f93-a5e5-dbacf271bf08", "6d34fee1-1985-4885-863d-a7f34134c662", "6e655b95-8adb-45e6-9bad-aedb2917aa95", "6eb47bc6-8aa1-4548-965e-b17280f8265e", "6eda8f6d-c16f-4137-8142-d8c6e942d8c7", "70fbab85-b7ff-4e67-8474-14459141bb23", "710fd92b-bb27-4596-96a1-cc4cc9d4da53", "713480b2-6382-4387-8d29-31a3f1f103a2", "714c0690-9a04-401b-b80b-48ed26a1c645", "74c092e4-f8bb-4d6f-996d-b1c88406adf7", "751def3a-b03e-44d2-83d3-bc5c4df78c9d", "75e3e698-2b7b-4499-b449-feca72328d2e", "7afebf9b-18a3-4663-945b-cbba8403c5e5", "7c8ba58d-5450-4607-859c-152617371562", "7d9f2c14-aea1-4d6b-8a11-42d0a619f292", "7e61030c-af9c-4edd-984b-4c414fc05b45", "7ff92f91-6239-4529-84cb-c510eaaf7394", "81212dac-52a2-4f5a-9d02-985d5b0b811c", "814fc401-5b17-4101-9816-5bab2bd106b6", "81a05ce5-b3e7-460e-b679-92eb66688c00", "835ccc11-1949-4614-a50e-577c14a03347", "843d55ee-7c77-451f-88c1-e30f3b0248e4", "84aa075a-cbf0-4863-93a0-a2659a2cd8ed", "86110001-4b90-4979-923f-9de27628d239", "88df6019-8237-471b-a436-83cc1cb0056a", "89c3ba3a-beca-4eaf-b825-9df62b18f100", "8a7c26db-38c1-4dba-8d0a-e3b76201abc8", "8b6c271e-5599-4599-a037-6f36cbaf5df4", "8c0247b3-7f55-453d-91a5-9c5b41a94b94", "8e781df6-5a3f-4f0b-bea1-865c4a659a9b", "908d7755-397d-459a-8dd9-b5c1cf2933a1", "9297b20b-2f4f-44cc-b6dc-19b46c15f549", "92fa1318-1a87-4ecf-871e-293596788039", "95a301cc-df41-4af8-a735-42a88252cc28", "9775dc51-db4d-4ff3-a0eb-bc02582b2519", "9963370e-03d2-47fc-bf10-d8afb45f2777", "9963370e-03d2-47fc-bf10-d8afb45f2778", "9c7a42d5-daf8-48fe-aa83-fa1e5c2a857b", "9e8352da-5e4d-49be-910e-1ffc6cfa909f", "9faed98a-be0f-41a8-b766-12bf76d40a22", "a022819b-21cf-450c-93a5-6ec9841d2743", "a15ff6a6-42e8-4a60-bdbb-ea80f41a9dac", "a663bdd0-dd56-435c-b01c-56914d355319", "a79cf485-f37f-4328-b89b-b119339398ef", "a858dc43-a5b2-47ac-8b19-2122748997d7", "a987c846-5c72-447b-a68c-733498ed977a", "a99e9853-75af-41ad-ab42-e367c434342e", "ab6c7821-03a9-4d1e-b62d-4aa1e75ac2bc", "acd0dd1c-a5b0-4049-affe-e95ebb296478", "ae8388f0-5f1e-4723-8de6-e708293ec9c4", "aecd278f-abed-4f66-b295-c7aa1b078e27", "b0bd9344-18bf-4960-9eef-bed0b2da1546", "b1a17b3b-208f-4345-810c-ece101c456a7", "b1ce929b-2658-464d-8423-f83025eba65a", "b58b7b6f-5c00-4977-9fbd-076996aab6d0", "b6ae8a73-8645-411b-9f3a-664ca8d0d0fb", "b88aa02c-a949-490e-82dd-dd99fde4384d", "b948eccc-ac77-4cca-bcbf-cb22e39fe1c1", "ba1d3b8c-b6e1-4a5c-b0ba-9fcb73895e79", "bb734894-6d11-465f-b58f-fbdbbcd3f210", "c02e2a45-bc33-40ea-88ca-8fb995680830", "c15fc73a-270c-4044-92d1-154d10a4e503", "c28d32ca-ae7f-44be-8058-ebddf4cea773", "c29e03e8-4c41-4f25-9d45-ca2ee037d2a8", "c43d7685-d7fa-45c2-bc4d-bc9242d4b688", "c45901c9-5b09-4904-bf85-8030ba2022ed", "c56a1871-b751-4414-8b30-1614d1eb01b8", "c6f57b96-9ce4-4f4c-9e7a-e673aa3ba05d", "c8562214-65c1-4aaa-a7a5-90e8d247bf04", "c98db184-64a9-448e-b4b5-f8f94e6bb3f6", "c9961577-ba7a-4723-bfc8-66e0227d9655", "cace2334-2bf3-448c-b648-076e1924d4d5", "cb56118d-3ec1-45ff-9d02-90afacb29444", "cd3961dc-f993-46e4-b8bb-e2f95b366f7c", "d302114a-df52-4183-9416-17beeb3ed264", "d75631a4-7999-4d30-820e-68ee58aff55b", "e0a4f8a8-7ea4-4f50-aed0-e8c6cde2f477", "e0f71c5e-1081-472e-bff8-d696445331fe", "e32d1b6a-6991-4822-a4b6-367d023be2ef", "e3408275-6056-4c66-9fda-50d2c505f806", "e6d88533-cf7f-4ef3-b23a-caeaa0c2c08f", "e8895d0e-590b-445b-ae8d-1bad4f81180f", "eadc51c2-6e22-401f-8780-2d898a8db951", "f392d4c0-6807-41ff-81a8-6c82e8753c89", "f4cc69d3-6dcc-4dc0-ada2-5c11b4253391", "f4cc69d3-6dcc-4dc0-ada2-5c11b4253392", "f594b66d-2f6d-4f16-8a4e-8c81539be1d8", "f8a46c4d-7463-464c-abd5-a2187680201c", "fb06a851-d01d-4cfd-a738-fcbc90b2ea43", "fb3a59bb-2e5b-423f-aae6-2b54a491cdb7", "fb3f014b-7f39-4ffe-a02a-86d490496ee4", "fb962710-d5f6-4ba6-aeb4-ef7a749091ed", "fc6d2d25-de43-4ec1-bb16-df8a56986091", "fc7448fc-3f39-4894-a8e3-d194c63ee73e", "fc77a5a4-0e46-4650-8ba9-a1b32a449466", "fe956fd4-b5ee-4b7d-a33f-e9e56328e493"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": "middleware", "name": "roles", "line": 23}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00907, "duration_str": "9.07ms", "stmt_id": "middleware::roles:23", "connection": "juragan_beku", "start_percent": 54.991, "width_percent": 3.857}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'b88aa02c-a949-490e-82dd-dd99fde4384d' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["b88aa02c-a949-490e-82dd-dd99fde4384d"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00136, "duration_str": "1.36ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 58.848, "width_percent": 0.578}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'a663bdd0-dd56-435c-b01c-56914d355319' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["a663bdd0-dd56-435c-b01c-56914d355319"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.0012900000000000001, "duration_str": "1.29ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 59.427, "width_percent": 0.549}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'b6ae8a73-8645-411b-9f3a-664ca8d0d0fb' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["b6ae8a73-8645-411b-9f3a-664ca8d0d0fb"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00136, "duration_str": "1.36ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 59.975, "width_percent": 0.578}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'b6ae8a73-8645-411b-9f3a-664ca8d0d0fb' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["b6ae8a73-8645-411b-9f3a-664ca8d0d0fb"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.0012900000000000001, "duration_str": "1.29ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 60.554, "width_percent": 0.549}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '7afebf9b-18a3-4663-945b-cbba8403c5e5' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7afebf9b-18a3-4663-945b-cbba8403c5e5"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00108, "duration_str": "1.08ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 61.102, "width_percent": 0.459}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '3927daac-5667-4d64-9c1e-ba804d715ad6' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["3927daac-5667-4d64-9c1e-ba804d715ad6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00113, "duration_str": "1.13ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 61.562, "width_percent": 0.481}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '710fd92b-bb27-4596-96a1-cc4cc9d4da53' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["710fd92b-bb27-4596-96a1-cc4cc9d4da53"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00128, "duration_str": "1.28ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 62.042, "width_percent": 0.544}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'b88aa02c-a949-490e-82dd-dd99fde4384d' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["b88aa02c-a949-490e-82dd-dd99fde4384d"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00122, "duration_str": "1.22ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 62.587, "width_percent": 0.519}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'a99e9853-75af-41ad-ab42-e367c434342e' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["a99e9853-75af-41ad-ab42-e367c434342e"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.0014299999999999998, "duration_str": "1.43ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 63.106, "width_percent": 0.608}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'b6ae8a73-8645-411b-9f3a-664ca8d0d0fb' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["b6ae8a73-8645-411b-9f3a-664ca8d0d0fb"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.0009, "duration_str": "900μs", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 63.714, "width_percent": 0.383}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '908d7755-397d-459a-8dd9-b5c1cf2933a1' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["908d7755-397d-459a-8dd9-b5c1cf2933a1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00095, "duration_str": "950μs", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 64.096, "width_percent": 0.404}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '95a301cc-df41-4af8-a735-42a88252cc28' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["95a301cc-df41-4af8-a735-42a88252cc28"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00109, "duration_str": "1.09ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 64.5, "width_percent": 0.464}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'b88aa02c-a949-490e-82dd-dd99fde4384d' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["b88aa02c-a949-490e-82dd-dd99fde4384d"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00101, "duration_str": "1.01ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 64.964, "width_percent": 0.43}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '6e655b95-8adb-45e6-9bad-aedb2917aa95' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6e655b95-8adb-45e6-9bad-aedb2917aa95"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00102, "duration_str": "1.02ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 65.394, "width_percent": 0.434}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'b88aa02c-a949-490e-82dd-dd99fde4384d' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["b88aa02c-a949-490e-82dd-dd99fde4384d"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.0009, "duration_str": "900μs", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 65.827, "width_percent": 0.383}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '42ecc46f-d988-4079-8fcc-e80f086ae0b8' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["42ecc46f-d988-4079-8fcc-e80f086ae0b8"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00083, "duration_str": "830μs", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 66.21, "width_percent": 0.353}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'b1ce929b-2658-464d-8423-f83025eba65a' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["b1ce929b-2658-464d-8423-f83025eba65a"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00083, "duration_str": "830μs", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 66.563, "width_percent": 0.353}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '6eb47bc6-8aa1-4548-965e-b17280f8265e' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6eb47bc6-8aa1-4548-965e-b17280f8265e"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00101, "duration_str": "1.01ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 66.916, "width_percent": 0.43}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'b88aa02c-a949-490e-82dd-dd99fde4384d' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["b88aa02c-a949-490e-82dd-dd99fde4384d"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00086, "duration_str": "860μs", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 67.346, "width_percent": 0.366}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'fb06a851-d01d-4cfd-a738-fcbc90b2ea43' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["fb06a851-d01d-4cfd-a738-fcbc90b2ea43"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00087, "duration_str": "870μs", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 67.711, "width_percent": 0.37}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '6e655b95-8adb-45e6-9bad-aedb2917aa95' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6e655b95-8adb-45e6-9bad-aedb2917aa95"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00101, "duration_str": "1.01ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 68.081, "width_percent": 0.43}, {"sql": "select * from `conf_group_menu` as `gp` inner join `conf_menu` as `m` on `gp`.`menu_id` = `m`.`id` where `gp`.`deleted_at` is null and `gp`.`group_id` = 'ff9759b5-2dc5-410b-b86c-f8146c1f66d8'", "type": "query", "params": [], "bindings": ["ff9759b5-2dc5-410b-b86c-f8146c1f66d8"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "\\app\\Http\\Controllers\\CoreController.php", "line": 107}, {"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\CoreController.php", "line": 57}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 149}, {"index": 15, "namespace": "middleware", "name": "roles", "line": 47}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.00571, "duration_str": "5.71ms", "stmt_id": "\\app\\Http\\Controllers\\CoreController.php:107", "connection": "juragan_beku", "start_percent": 68.511, "width_percent": 2.428}, {"sql": "select * from `conf_group_menu` as `gp` inner join `conf_menu` as `m` on `gp`.`menu_id` = `m`.`id` where `gp`.`deleted_at` is null and `gp`.`group_id` = 'ff9759b5-2dc5-410b-b86c-f8146c1f66d8'", "type": "query", "params": [], "bindings": ["ff9759b5-2dc5-410b-b86c-f8146c1f66d8"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "\\app\\Http\\Controllers\\CoreController.php", "line": 107}, {"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\CoreController.php", "line": 75}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Generator\\OrderCancelHistoryController.php", "line": 92}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.0053, "duration_str": "5.3ms", "stmt_id": "\\app\\Http\\Controllers\\CoreController.php:107", "connection": "juragan_beku", "start_percent": 70.939, "width_percent": 2.254}, {"sql": "select * from `tbl_order_cancel` where `tbl_order_cancel`.`deleted_at` is null order by `created_at` desc limit 15", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Service\\Generator\\OrderCancelHistoryService.php", "line": 66}, {"index": 15, "namespace": null, "name": "\\app\\Repository\\Generator\\OrderCancelHistoryRepository.php", "line": 36}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\CoreController.php", "line": 100}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Controllers\\Generator\\OrderCancelHistoryController.php", "line": 92}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.006889999999999999, "duration_str": "6.89ms", "stmt_id": "\\app\\Service\\Generator\\OrderCancelHistoryService.php:66", "connection": "juragan_beku", "start_percent": 73.194, "width_percent": 2.93}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'juragan_beku' and table_name = 'tbl_order'", "type": "query", "params": [], "bindings": ["juragan_beku", "tbl_order"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php", "line": 33}, {"index": 12, "namespace": null, "name": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php", "line": 16}, {"index": 20, "namespace": null, "name": "\\app\\Models\\Generator\\OrderCancelHistory.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\app\\Service\\Generator\\OrderCancelHistoryService.php", "line": 66}, {"index": 28, "namespace": null, "name": "\\app\\Repository\\Generator\\OrderCancelHistoryRepository.php", "line": 36}], "duration": 0.00323, "duration_str": "3.23ms", "stmt_id": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php:33", "connection": "juragan_beku", "start_percent": 76.124, "width_percent": 1.374}, {"sql": "select * from `tbl_order` where `tbl_order`.`id` in ('1f7eb132-a254-46d8-932a-e6f5345a4d07', '7af1ff01-076e-4bcd-913d-c625e9ac0124', '8469a7b5-b6e0-42fb-a785-f9ae29c70fff', '89dba837-ef91-4d1e-ac3c-2b75b853ce85', '8a5613a7-719c-40d8-9553-999c1cf3e4f2', '939f4d07-bfc0-4785-84a4-702503673018', '93a7ad5a-5721-43c7-844f-d4c028fed771', '93ce78de-fb31-48f3-ae00-91b8725e2b13', '9d3f7ae0-6317-4c4a-80af-d2f4216d6f07', 'c10b8cdd-31e1-4684-a6b3-bfdaa480ea33', 'ca3a8b45-95a0-437f-a6da-cecc9fbf5356', 'e06244c8-6156-4a57-873b-8284ceb7c7d2', 'f145c92e-7498-4f8e-a313-3a0f2ae5d5e5', 'f84ba984-a4fc-4eb1-8d88-b48d23a6946e', 'ff4c5a7f-47d1-409d-b333-15fc9d984698') and `tbl_order`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1f7eb132-a254-46d8-932a-e6f5345a4d07", "7af1ff01-076e-4bcd-913d-c625e9ac0124", "8469a7b5-b6e0-42fb-a785-f9ae29c70fff", "89dba837-ef91-4d1e-ac3c-2b75b853ce85", "8a5613a7-719c-40d8-9553-999c1cf3e4f2", "939f4d07-bfc0-4785-84a4-702503673018", "93a7ad5a-5721-43c7-844f-d4c028fed771", "93ce78de-fb31-48f3-ae00-91b8725e2b13", "9d3f7ae0-6317-4c4a-80af-d2f4216d6f07", "c10b8cdd-31e1-4684-a6b3-bfdaa480ea33", "ca3a8b45-95a0-437f-a6da-cecc9fbf5356", "e06244c8-6156-4a57-873b-8284ceb7c7d2", "f145c92e-7498-4f8e-a313-3a0f2ae5d5e5", "f84ba984-a4fc-4eb1-8d88-b48d23a6946e", "ff4c5a7f-47d1-409d-b333-15fc9d984698"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Service\\Generator\\OrderCancelHistoryService.php", "line": 66}, {"index": 20, "namespace": null, "name": "\\app\\Repository\\Generator\\OrderCancelHistoryRepository.php", "line": 36}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\CoreController.php", "line": 100}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\Generator\\OrderCancelHistoryController.php", "line": 92}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00209, "duration_str": "2.09ms", "stmt_id": "\\app\\Service\\Generator\\OrderCancelHistoryService.php:66", "connection": "juragan_beku", "start_percent": 77.498, "width_percent": 0.889}, {"sql": "select * from `conf_users` where `conf_users`.`id` in (0, 0) and `conf_users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Service\\Generator\\OrderCancelHistoryService.php", "line": 66}, {"index": 20, "namespace": null, "name": "\\app\\Repository\\Generator\\OrderCancelHistoryRepository.php", "line": 36}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\CoreController.php", "line": 100}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\Generator\\OrderCancelHistoryController.php", "line": 92}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.01584, "duration_str": "15.84ms", "stmt_id": "\\app\\Service\\Generator\\OrderCancelHistoryService.php:66", "connection": "juragan_beku", "start_percent": 78.386, "width_percent": 6.737}, {"sql": "select * from `conf_group` where `conf_group`.`id` in ('', '0c8233e1-2175-452a-ba1a-8a14b9c3b00f', '17611e84-3f05-429f-81d0-b6e3a4fdb658', '3b2cce7c-c8e3-4d6d-a289-4809e985549e', '4aa39254-ce2c-4b67-9cee-d4ecbc6d6aef', '63f5b38f-11ee-4439-b86f-5eefcaaf8cab', '8888b855-0e42-46a6-aa79-a0e9c8aed957', '8bf7370b-e2ae-4304-8851-a07d0b2c27a5', '940952c5-19b3-4bb7-8088-91ec74251295', '9b27fced-93e8-4541-aaa5-3e536ca189d2', 'a126c5e2-3cec-496f-be95-50e5b863c4d1', 'fca7d997-63dc-4474-9b50-b6dce3e30208', 'ff9759b5-2dc5-410b-b86c-f8146c1f66d8') and `conf_group`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["", "0c8233e1-2175-452a-ba1a-8a14b9c3b00f", "17611e84-3f05-429f-81d0-b6e3a4fdb658", "3b2cce7c-c8e3-4d6d-a289-4809e985549e", "4aa39254-ce2c-4b67-9cee-d4ecbc6d6aef", "63f5b38f-11ee-4439-b86f-5eefcaaf8cab", "8888b855-0e42-46a6-aa79-a0e9c8aed957", "8bf7370b-e2ae-4304-8851-a07d0b2c27a5", "940952c5-19b3-4bb7-8088-91ec74251295", "9b27fced-93e8-4541-aaa5-3e536ca189d2", "a126c5e2-3cec-496f-be95-50e5b863c4d1", "fca7d997-63dc-4474-9b50-b6dce3e30208", "ff9759b5-2dc5-410b-b86c-f8146c1f66d8"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Service\\Generator\\OrderCancelHistoryService.php", "line": 66}, {"index": 25, "namespace": null, "name": "\\app\\Repository\\Generator\\OrderCancelHistoryRepository.php", "line": 36}, {"index": 26, "namespace": null, "name": "\\app\\Http\\Controllers\\CoreController.php", "line": 100}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\Generator\\OrderCancelHistoryController.php", "line": 92}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00152, "duration_str": "1.52ms", "stmt_id": "\\app\\Service\\Generator\\OrderCancelHistoryService.php:66", "connection": "juragan_beku", "start_percent": 85.123, "width_percent": 0.646}, {"sql": "select count(*) as aggregate from (select * from `tbl_order_cancel` where `tbl_order_cancel`.`deleted_at` is null order by `created_at` desc limit 15) count_row_table", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 215}, {"index": 15, "namespace": null, "name": "\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 190}, {"index": 16, "namespace": null, "name": "\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 139}, {"index": 17, "namespace": null, "name": "\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 99}, {"index": 18, "namespace": null, "name": "\\app\\Service\\Generator\\OrderCancelHistoryService.php", "line": 92}], "duration": 0.00176, "duration_str": "1.76ms", "stmt_id": "\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php:215", "connection": "juragan_beku", "start_percent": 85.77, "width_percent": 0.749}, {"sql": "select * from `tbl_order_cancel` where `tbl_order_cancel`.`deleted_at` is null order by `created_at` desc, `id` asc limit 10 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 272}, {"index": 15, "namespace": null, "name": "\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 101}, {"index": 16, "namespace": null, "name": "\\app\\Service\\Generator\\OrderCancelHistoryService.php", "line": 92}, {"index": 17, "namespace": null, "name": "\\app\\Repository\\Generator\\OrderCancelHistoryRepository.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Controllers\\CoreController.php", "line": 100}], "duration": 0.00196, "duration_str": "1.96ms", "stmt_id": "\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php:272", "connection": "juragan_beku", "start_percent": 86.518, "width_percent": 0.834}, {"sql": "select * from `tbl_order` where `tbl_order`.`id` in ('7af1ff01-076e-4bcd-913d-c625e9ac0124', '89dba837-ef91-4d1e-ac3c-2b75b853ce85', '8a5613a7-719c-40d8-9553-999c1cf3e4f2', '939f4d07-bfc0-4785-84a4-702503673018', '93ce78de-fb31-48f3-ae00-91b8725e2b13', '9d3f7ae0-6317-4c4a-80af-d2f4216d6f07', 'c10b8cdd-31e1-4684-a6b3-bfdaa480ea33', 'f145c92e-7498-4f8e-a313-3a0f2ae5d5e5', 'f84ba984-a4fc-4eb1-8d88-b48d23a6946e', 'ff4c5a7f-47d1-409d-b333-15fc9d984698') and `tbl_order`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["7af1ff01-076e-4bcd-913d-c625e9ac0124", "89dba837-ef91-4d1e-ac3c-2b75b853ce85", "8a5613a7-719c-40d8-9553-999c1cf3e4f2", "939f4d07-bfc0-4785-84a4-702503673018", "93ce78de-fb31-48f3-ae00-91b8725e2b13", "9d3f7ae0-6317-4c4a-80af-d2f4216d6f07", "c10b8cdd-31e1-4684-a6b3-bfdaa480ea33", "f145c92e-7498-4f8e-a313-3a0f2ae5d5e5", "f84ba984-a4fc-4eb1-8d88-b48d23a6946e", "ff4c5a7f-47d1-409d-b333-15fc9d984698"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 272}, {"index": 20, "namespace": null, "name": "\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 101}, {"index": 21, "namespace": null, "name": "\\app\\Service\\Generator\\OrderCancelHistoryService.php", "line": 92}, {"index": 22, "namespace": null, "name": "\\app\\Repository\\Generator\\OrderCancelHistoryRepository.php", "line": 36}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Controllers\\CoreController.php", "line": 100}], "duration": 0.00175, "duration_str": "1.75ms", "stmt_id": "\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php:272", "connection": "juragan_beku", "start_percent": 87.352, "width_percent": 0.744}, {"sql": "select * from `conf_users` where `conf_users`.`id` in (0, 0) and `conf_users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 272}, {"index": 20, "namespace": null, "name": "\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 101}, {"index": 21, "namespace": null, "name": "\\app\\Service\\Generator\\OrderCancelHistoryService.php", "line": 92}, {"index": 22, "namespace": null, "name": "\\app\\Repository\\Generator\\OrderCancelHistoryRepository.php", "line": 36}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Controllers\\CoreController.php", "line": 100}], "duration": 0.02689, "duration_str": "26.89ms", "stmt_id": "\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php:272", "connection": "juragan_beku", "start_percent": 88.096, "width_percent": 11.436}, {"sql": "select * from `conf_group` where `conf_group`.`id` in ('', '0c8233e1-2175-452a-ba1a-8a14b9c3b00f', '17611e84-3f05-429f-81d0-b6e3a4fdb658', '3b2cce7c-c8e3-4d6d-a289-4809e985549e', '4aa39254-ce2c-4b67-9cee-d4ecbc6d6aef', '63f5b38f-11ee-4439-b86f-5eefcaaf8cab', '8888b855-0e42-46a6-aa79-a0e9c8aed957', '8bf7370b-e2ae-4304-8851-a07d0b2c27a5', '940952c5-19b3-4bb7-8088-91ec74251295', '9b27fced-93e8-4541-aaa5-3e536ca189d2', 'a126c5e2-3cec-496f-be95-50e5b863c4d1', 'fca7d997-63dc-4474-9b50-b6dce3e30208', 'ff9759b5-2dc5-410b-b86c-f8146c1f66d8') and `conf_group`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["", "0c8233e1-2175-452a-ba1a-8a14b9c3b00f", "17611e84-3f05-429f-81d0-b6e3a4fdb658", "3b2cce7c-c8e3-4d6d-a289-4809e985549e", "4aa39254-ce2c-4b67-9cee-d4ecbc6d6aef", "63f5b38f-11ee-4439-b86f-5eefcaaf8cab", "8888b855-0e42-46a6-aa79-a0e9c8aed957", "8bf7370b-e2ae-4304-8851-a07d0b2c27a5", "940952c5-19b3-4bb7-8088-91ec74251295", "9b27fced-93e8-4541-aaa5-3e536ca189d2", "a126c5e2-3cec-496f-be95-50e5b863c4d1", "fca7d997-63dc-4474-9b50-b6dce3e30208", "ff9759b5-2dc5-410b-b86c-f8146c1f66d8"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 272}, {"index": 25, "namespace": null, "name": "\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 101}, {"index": 26, "namespace": null, "name": "\\app\\Service\\Generator\\OrderCancelHistoryService.php", "line": 92}, {"index": 27, "namespace": null, "name": "\\app\\Repository\\Generator\\OrderCancelHistoryRepository.php", "line": 36}, {"index": 28, "namespace": null, "name": "\\app\\Http\\Controllers\\CoreController.php", "line": 100}], "duration": 0.0011, "duration_str": "1.1ms", "stmt_id": "\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php:272", "connection": "juragan_beku", "start_percent": 99.532, "width_percent": 0.468}]}, "models": {"data": {"App\\Models\\Generator\\Order": 6, "App\\Models\\Generator\\OrderCancelHistory": 25, "App\\Models\\User": 1167, "App\\Models\\Setting": 4, "App\\Models\\Group": 2619, "App\\Models\\Menu": 313}, "count": 4134}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QoZh1I8mypHP2aMUh0DEtCk2CpnpWBdxW5ZtIuuB", "url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/administrator/ordercancelhistories\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "c065dc93-6b2f-4bf5-b746-6bb48713d4bf"}, "request": {"path_info": "/administrator/ordercancelhistories/datatable.json", "status_code": "<pre class=sf-dump id=sf-dump-604339947 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-604339947\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2066175988 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>draw</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>columns</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"11 characters\">DT_RowIndex</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"16 characters\">order.order_code</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"16 characters\">order.order_code</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"13 characters\">user.fullname</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">user.fullname</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"6 characters\">action</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">action</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>column</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>dir</span>\" => \"<span class=sf-dump-str title=\"3 characters\">asc</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>start</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>length</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>no_order</span>\" => \"<span class=sf-dump-str title=\"5 characters\">20185</span>\"\n  \"<span class=sf-dump-key>_</span>\" => \"<span class=sf-dump-str title=\"13 characters\">1752049795640</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2066175988\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1596398226 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>draw</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>columns</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"11 characters\">DT_RowIndex</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"16 characters\">order.order_code</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"16 characters\">order.order_code</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"13 characters\">user.fullname</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">user.fullname</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"6 characters\">action</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">action</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>column</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>dir</span>\" => \"<span class=sf-dump-str title=\"3 characters\">asc</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>start</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>length</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>no_order</span>\" => \"<span class=sf-dump-str title=\"5 characters\">20185</span>\"\n  \"<span class=sf-dump-key>_</span>\" => \"<span class=sf-dump-str title=\"13 characters\">1752049795640</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1596398226\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1964818156 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">http://127.0.0.1:8000/administrator/ordercancelhistories</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">en-US,en;q=0.9,id;q=0.8,zh-CN;q=0.7,zh;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"808 characters\">_ga=GA1.1.587546091.1749434964; _ga_GBZ3SGGX85=GS2.1.s1751877726$o26$g0$t1751877726$j60$l0$h0; XSRF-TOKEN=eyJpdiI6Ild1NENFZTBrTDdGcXBzN2VxZHRJY2c9PSIsInZhbHVlIjoiMURXVncyTCthK2dOZTlWSzlUL04yM01kd0xwNU16MFZ0RDlSa2RuOEdYY3orM1BRby85aVhiZHl3cnlwcGtWWC8yMGRiME84cjVXbFpKOGd6dkJQdytYVFpObUxmcXFCU05UY1R2WnMzUDhndFgwTXBUYzRHRWtNTGh3UWN0L3EiLCJtYWMiOiI4MGNiMDM4ZTk3NGQwZWI0ZWVkMTgwZjc0MDQwNzI4Y2Q3ODJkZWYwM2E5MTBjM2E0YWI2ZDkyNTM3MGUzMDE4IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImdtTm5qV2VPRk92b1J6OGh4c3d2ZWc9PSIsInZhbHVlIjoiWG5xTGswV0s2TjJIRUlocXZ4bWtPd05vcVRKWlhFTjhHNXpTd0gvdEd4aHl1eG5wSnhEbVBrajdCWElZWWMwbW1malhWVUc2SSsrVmtlZHhwNGNaNzgrc2lROStWcFRTbVBHNnIwQXFra2M4bkFmY0crSnQzWTNKZE1Vdm9uWGkiLCJtYWMiOiJkY2ViNDY0OWNmYTMxZDY5MDEwMmM0NjRiZDljNWVjN2JkOTIxZmVmNDU2NjVmNDA4ZDQyODhhODg0ZDFlYjBhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1964818156\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1925814211 data-indent-pad=\"  \"><span class=sf-dump-note>array:30</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">D:\\Topan\\cms-juraganbeku\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">54923</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"30 characters\">PHP/8.3.6 (Development Server)</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"1340 characters\">/administrator/ordercancelhistories/datatable.json?draw=1&amp;columns%5B0%5D%5Bdata%5D=DT_RowIndex&amp;columns%5B0%5D%5Bname%5D=id&amp;columns%5B0%5D%5Bsearchable%5D=true&amp;columns%5B0%5D%5Borderable%5D=true&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order.order_code&amp;columns%5B1%5D%5Bname%5D=order.order_code&amp;columns%5B1%5D%5Bsearchable%5D=false&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=user.fullname&amp;columns%5B2%5D%5Bname%5D=user.fullname&amp;columns%5B2%5D%5Bsearchable%5D=false&amp;columns%5B2%5D%5Borderable%5D=true&amp;columns%5B2%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B2%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B3%5D%5Bdata%5D=created_at&amp;columns%5B3%5D%5Bname%5D=created_at&amp;columns%5B3%5D%5Bsearchable%5D=false&amp;columns%5B3%5D%5Borderable%5D=true&amp;columns%5B3%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B3%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B4%5D%5Bdata%5D=action&amp;columns%5B4%5D%5Bname%5D=action&amp;columns%5B4%5D%5Bsearchable%5D=false&amp;columns%5B4%5D%5Borderable%5D=false&amp;columns%5B4%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B4%5D%5Bsearch%5D%5Bregex%5D=false&amp;order%5B0%5D%5Bcolumn%5D=0&amp;order%5B0%5D%5Bdir%5D=asc&amp;start=0&amp;length=10&amp;search%5Bvalue%5D=&amp;search%5Bregex%5D=false&amp;no_order=20185&amp;_=1752049795640</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"50 characters\">/administrator/ordercancelhistories/datatable.json</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"35 characters\">D:\\Topan\\cms-juraganbeku\\server.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"50 characters\">/administrator/ordercancelhistories/datatable.json</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"1289 characters\">draw=1&amp;columns%5B0%5D%5Bdata%5D=DT_RowIndex&amp;columns%5B0%5D%5Bname%5D=id&amp;columns%5B0%5D%5Bsearchable%5D=true&amp;columns%5B0%5D%5Borderable%5D=true&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order.order_code&amp;columns%5B1%5D%5Bname%5D=order.order_code&amp;columns%5B1%5D%5Bsearchable%5D=false&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=user.fullname&amp;columns%5B2%5D%5Bname%5D=user.fullname&amp;columns%5B2%5D%5Bsearchable%5D=false&amp;columns%5B2%5D%5Borderable%5D=true&amp;columns%5B2%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B2%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B3%5D%5Bdata%5D=created_at&amp;columns%5B3%5D%5Bname%5D=created_at&amp;columns%5B3%5D%5Bsearchable%5D=false&amp;columns%5B3%5D%5Borderable%5D=true&amp;columns%5B3%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B3%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B4%5D%5Bdata%5D=action&amp;columns%5B4%5D%5Bname%5D=action&amp;columns%5B4%5D%5Bsearchable%5D=false&amp;columns%5B4%5D%5Borderable%5D=false&amp;columns%5B4%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B4%5D%5Bsearch%5D%5Bregex%5D=false&amp;order%5B0%5D%5Bcolumn%5D=0&amp;order%5B0%5D%5Bdir%5D=asc&amp;start=0&amp;length=10&amp;search%5Bvalue%5D=&amp;search%5Bregex%5D=false&amp;no_order=20185&amp;_=1752049795640</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_REQUESTED_WITH</span>\" => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"56 characters\">http://127.0.0.1:8000/administrator/ordercancelhistories</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"44 characters\">en-US,en;q=0.9,id;q=0.8,zh-CN;q=0.7,zh;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"808 characters\">_ga=GA1.1.587546091.1749434964; _ga_GBZ3SGGX85=GS2.1.s1751877726$o26$g0$t1751877726$j60$l0$h0; XSRF-TOKEN=eyJpdiI6Ild1NENFZTBrTDdGcXBzN2VxZHRJY2c9PSIsInZhbHVlIjoiMURXVncyTCthK2dOZTlWSzlUL04yM01kd0xwNU16MFZ0RDlSa2RuOEdYY3orM1BRby85aVhiZHl3cnlwcGtWWC8yMGRiME84cjVXbFpKOGd6dkJQdytYVFpObUxmcXFCU05UY1R2WnMzUDhndFgwTXBUYzRHRWtNTGh3UWN0L3EiLCJtYWMiOiI4MGNiMDM4ZTk3NGQwZWI0ZWVkMTgwZjc0MDQwNzI4Y2Q3ODJkZWYwM2E5MTBjM2E0YWI2ZDkyNTM3MGUzMDE4IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImdtTm5qV2VPRk92b1J6OGh4c3d2ZWc9PSIsInZhbHVlIjoiWG5xTGswV0s2TjJIRUlocXZ4bWtPd05vcVRKWlhFTjhHNXpTd0gvdEd4aHl1eG5wSnhEbVBrajdCWElZWWMwbW1malhWVUc2SSsrVmtlZHhwNGNaNzgrc2lROStWcFRTbVBHNnIwQXFra2M4bkFmY0crSnQzWTNKZE1Vdm9uWGkiLCJtYWMiOiJkY2ViNDY0OWNmYTMxZDY5MDEwMmM0NjRiZDljNWVjN2JkOTIxZmVmNDU2NjVmNDA4ZDQyODhhODg0ZDFlYjBhIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1752049822.8407</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1752049822</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1925814211\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-186046935 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_GBZ3SGGX85</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QoZh1I8mypHP2aMUh0DEtCk2CpnpWBdxW5ZtIuuB</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OXEmGaq68dByCoDl0SJP8YEGquktzNtL3aXCzLzt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-186046935\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1905370764 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 09 Jul 2025 08:30:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ik1FOHRYT1JCbzFZVmo2VU5wZVRBTEE9PSIsInZhbHVlIjoiTFNMQnF0YXlYbEJ1MitMeHZVL242Q05jV1p2MTBMRThnaUEvWDZPaGNKNTJUclVlSFlXaVY3YU5Hckp3bWNvU2ZSNjJLS0hBZGhtZDZqSnZyNFlmblZUYlFZdHpYN1JhSzlrQkNVOWwyZk9EUDdQK1FXN2hVRDR2ZS9XZS9xRmoiLCJtYWMiOiJhZmI1MDAyYzRlMGNmNzRmMTU2MTk5MTI0MjFhNGM5OWFiZGMwZWRkMzI2YWY5NmY1NWJhZGZlOWM4MjJmMjA5IiwidGFnIjoiIn0%3D; expires=Wed, 09-Jul-2025 10:30:24 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6Ilg2N2V3dUVhaXRFcW5iT0lxNU9uREE9PSIsInZhbHVlIjoidDJkcGVyeG1PMzI1Tm9wWTQvQVp4L0U4YWFrbWo2eVgrUWdkajM3OExzMk4zajAwemJDM2dPZ3d5ODlBTUtuYkZHRmdsYlNIKzlvODd4aEVEZ1YwVlQwNWhjTnV4b2I2MlZCbzZiVWVqWlVvUE9pVzMxY3lkeWRhVVFnRDVVdkgiLCJtYWMiOiJkYzg2NmE1NzIwMjg2OWZlZTU2MWI1YTY5YzI1MmFkOTRlMDM2ZWE2MWI4ZTQ2OWExYjNlM2JhNGFiYjhjZjRiIiwidGFnIjoiIn0%3D; expires=Wed, 09-Jul-2025 10:30:24 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ik1FOHRYT1JCbzFZVmo2VU5wZVRBTEE9PSIsInZhbHVlIjoiTFNMQnF0YXlYbEJ1MitMeHZVL242Q05jV1p2MTBMRThnaUEvWDZPaGNKNTJUclVlSFlXaVY3YU5Hckp3bWNvU2ZSNjJLS0hBZGhtZDZqSnZyNFlmblZUYlFZdHpYN1JhSzlrQkNVOWwyZk9EUDdQK1FXN2hVRDR2ZS9XZS9xRmoiLCJtYWMiOiJhZmI1MDAyYzRlMGNmNzRmMTU2MTk5MTI0MjFhNGM5OWFiZGMwZWRkMzI2YWY5NmY1NWJhZGZlOWM4MjJmMjA5IiwidGFnIjoiIn0%3D; expires=Wed, 09-Jul-2025 10:30:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6Ilg2N2V3dUVhaXRFcW5iT0lxNU9uREE9PSIsInZhbHVlIjoidDJkcGVyeG1PMzI1Tm9wWTQvQVp4L0U4YWFrbWo2eVgrUWdkajM3OExzMk4zajAwemJDM2dPZ3d5ODlBTUtuYkZHRmdsYlNIKzlvODd4aEVEZ1YwVlQwNWhjTnV4b2I2MlZCbzZiVWVqWlVvUE9pVzMxY3lkeWRhVVFnRDVVdkgiLCJtYWMiOiJkYzg2NmE1NzIwMjg2OWZlZTU2MWI1YTY5YzI1MmFkOTRlMDM2ZWE2MWI4ZTQ2OWExYjNlM2JhNGFiYjhjZjRiIiwidGFnIjoiIn0%3D; expires=Wed, 09-Jul-2025 10:30:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1905370764\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-579115222 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QoZh1I8mypHP2aMUh0DEtCk2CpnpWBdxW5ZtIuuB</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"56 characters\">http://127.0.0.1:8000/administrator/ordercancelhistories</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"36 characters\">c065dc93-6b2f-4bf5-b746-6bb48713d4bf</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-579115222\", {\"maxDepth\":0})</script>\n"}}