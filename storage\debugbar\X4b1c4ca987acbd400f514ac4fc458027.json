{"__meta": {"id": "X4b1c4ca987acbd400f514ac4fc458027", "datetime": "2025-07-09 15:02:40", "utime": 1752048160.856968, "method": "POST", "uri": "/login", "ip": "127.0.0.1"}, "php": {"version": "8.3.6", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[15:02:37] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1752048157.545167, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1752048156.840059, "end": 1752048160.857002, "duration": 4.016942977905273, "duration_str": "4.02s", "measures": [{"label": "Booting", "start": 1752048156.840059, "relative_start": 0, "end": 1752048157.493806, "relative_end": 1752048157.493806, "duration": 0.6537468433380127, "duration_str": "654ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1752048157.493826, "relative_start": 0.6537668704986572, "end": 1752048160.857007, "relative_end": 5.0067901611328125e-06, "duration": 3.3631811141967773, "duration_str": "3.36s", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 25051256, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST login", "middleware": "web", "controller": "App\\Http\\Controllers\\Auth\\LoginController@login", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=D:\\Topan\\cms-juraganbeku\\app\\Http\\Controllers\\Auth\\LoginController.php&line=109\">\\app\\Http\\Controllers\\Auth\\LoginController.php:109-155</a>"}, "queries": {"nb_statements": 10, "nb_failed_statements": 0, "accumulated_duration": 0.12722, "accumulated_duration_str": "127ms", "statements": [{"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'juragan_beku' and table_name = 'conf_setting'", "type": "query", "params": [], "bindings": ["juragan_beku", "conf_setting"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php", "line": 33}, {"index": 12, "namespace": null, "name": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php", "line": 16}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00789, "duration_str": "7.89ms", "stmt_id": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php:33", "connection": "juragan_beku", "start_percent": 0, "width_percent": 6.202}, {"sql": "select * from `conf_setting` where `conf_setting`.`deleted_at` is null and (`parameter` = 'super_password') limit 1", "type": "query", "params": [], "bindings": ["super_password"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repository\\CoreRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\Auth\\LoginController.php", "line": 60}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00242, "duration_str": "2.42ms", "stmt_id": "\\app\\Repository\\CoreRepository.php:34", "connection": "juragan_beku", "start_percent": 6.202, "width_percent": 1.902}, {"sql": "select * from `conf_setting` where `conf_setting`.`deleted_at` is null and (`parameter` = 'logo') limit 1", "type": "query", "params": [], "bindings": ["logo"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repository\\CoreRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\Auth\\LoginController.php", "line": 61}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00101, "duration_str": "1.01ms", "stmt_id": "\\app\\Repository\\CoreRepository.php:34", "connection": "juragan_beku", "start_percent": 8.104, "width_percent": 0.794}, {"sql": "select * from `conf_setting` where `conf_setting`.`deleted_at` is null and (`parameter` = 'app_name') limit 1", "type": "query", "params": [], "bindings": ["app_name"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repository\\CoreRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\Auth\\LoginController.php", "line": 62}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00087, "duration_str": "870μs", "stmt_id": "\\app\\Repository\\CoreRepository.php:34", "connection": "juragan_beku", "start_percent": 8.898, "width_percent": 0.684}, {"sql": "select * from `conf_setting` where `conf_setting`.`deleted_at` is null and `parameter` in ('logo', 'app_name', 'app_name_short', 'footer', 'logo_icon')", "type": "query", "params": [], "bindings": ["logo", "app_name", "app_name_short", "footer", "logo_icon"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Repository\\SettingRepository.php", "line": 41}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Auth\\LoginController.php", "line": 63}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00075, "duration_str": "750μs", "stmt_id": "\\app\\Repository\\SettingRepository.php:41", "connection": "juragan_beku", "start_percent": 9.582, "width_percent": 0.59}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'juragan_beku' and table_name = 'conf_users'", "type": "query", "params": [], "bindings": ["juragan_beku", "conf_users"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php", "line": 33}, {"index": 12, "namespace": null, "name": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php", "line": 16}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 185}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 172}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 117}], "duration": 0.01093, "duration_str": "10.93ms", "stmt_id": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php:33", "connection": "juragan_beku", "start_percent": 10.171, "width_percent": 8.591}, {"sql": "select * from `conf_users` where `username` = 'spradmin' and `conf_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["sprad<PERSON>"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 133}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 377}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php", "line": 86}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Controllers\\Auth\\LoginController.php", "line": 137}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.01781, "duration_str": "17.81ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:133", "connection": "juragan_beku", "start_percent": 18.763, "width_percent": 13.999}, {"sql": "insert into `audits` (`auditable_id`, `auditable_type`, `event`, `url`, `ip_address`, `user_agent`, `created_at`, `updated_at`, `user_id`) values ('c065dc93-6b2f-4bf5-b746-6bb48713d4bf', 'Logged In', 'Logged In', 'http://127.0.0.1:8000/login', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-09 15:02:39', '2025-07-09 15:02:39', 'c065dc93-6b2f-4bf5-b746-6bb48713d4bf')", "type": "query", "params": [], "bindings": ["c065dc93-6b2f-4bf5-b746-6bb48713d4bf", "Logged In", "Logged In", "http://127.0.0.1:8000/login", "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "2025-07-09 15:02:39", "2025-07-09 15:02:39", "c065dc93-6b2f-4bf5-b746-6bb48713d4bf"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\Auth\\LoginController.php", "line": 94}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php", "line": 114}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Controllers\\Auth\\LoginController.php", "line": 138}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.*****************, "duration_str": "79.21ms", "stmt_id": "\\app\\Http\\Controllers\\Auth\\LoginController.php:94", "connection": "juragan_beku", "start_percent": 32.762, "width_percent": 62.262}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'juragan_beku' and table_name = 'conf_group'", "type": "query", "params": [], "bindings": ["juragan_beku", "conf_group"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php", "line": 33}, {"index": 12, "namespace": null, "name": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php", "line": 16}, {"index": 20, "namespace": null, "name": "\\app\\Models\\User.php", "line": 81}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Controllers\\Auth\\LoginController.php", "line": 97}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php", "line": 114}], "duration": 0.00387, "duration_str": "3.87ms", "stmt_id": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php:33", "connection": "juragan_beku", "start_percent": 95.024, "width_percent": 3.042}, {"sql": "select * from `conf_group` where `conf_group`.`id` = 'ff9759b5-2dc5-410b-b86c-f8146c1f66d8' and `conf_group`.`id` is not null and `conf_group`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["ff9759b5-2dc5-410b-b86c-f8146c1f66d8"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\Auth\\LoginController.php", "line": 97}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php", "line": 114}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\Auth\\LoginController.php", "line": 138}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.00246, "duration_str": "2.46ms", "stmt_id": "\\app\\Http\\Controllers\\Auth\\LoginController.php:97", "connection": "juragan_beku", "start_percent": 98.066, "width_percent": 1.934}]}, "models": {"data": {"App\\Models\\Group": 1, "App\\Models\\User": 1, "App\\Models\\Setting": 6}, "count": 8}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QoZh1I8mypHP2aMUh0DEtCk2CpnpWBdxW5ZtIuuB", "url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "c065dc93-6b2f-4bf5-b746-6bb48713d4bf", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-482969380 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-482969380\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-67400270 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">uqU8XcYdjZEChvw0H37yWr6HFtCneckJnENtrf9H</span>\"\n  \"<span class=sf-dump-key>username</span>\" => \"<span class=sf-dump-str title=\"8 characters\">spradmin</span>\"\n  \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"8 characters\">k5j67k2i</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-67400270\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1778325423 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">83</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">en-US,en;q=0.9,id;q=0.8,zh-CN;q=0.7,zh;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"808 characters\">_ga=GA1.1.587546091.1749434964; _ga_GBZ3SGGX85=GS2.1.s1751877726$o26$g0$t1751877726$j60$l0$h0; XSRF-TOKEN=eyJpdiI6IkEvYkM4eFBWUzZTblh4S3IxR2ZOTUE9PSIsInZhbHVlIjoiUG1vZzgyU3dicUJuUnYvR1hWR1gzWGw2bnZYZ3QvVnBZQU14TDFzREVvQmVqdk5TZ3l3bGNxL252RXdlWlByZjMzcGFURzNLVG5pWUFua08wd0lVc2x3V1NKRlgrdENLaG00VW9pdy9uNldDRmowZE0zZEFsSmw4eTRvd20wazkiLCJtYWMiOiIyZWIyNDliOTFlOTVjY2E5ZWViZDUzODMwMGJjMjE4YjE3YzJiZmU2NDc5MGVjMzBlY2M4NTUyZDAwYzFiYjlmIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InUxSTlYSnNYU3hQYVdXSXlWMEFmTnc9PSIsInZhbHVlIjoiTjRERHgwQTNETEJnQjN3MjViWS91RmlnZVlSTUI1K29GeVQzMldlUC9aaXJEVUZyWmM1UzNMQVBsWThDVmFOVTVaU3lvWlhZajZLdGRZUTZhdEhySXBUb2h2RDVyMWtidFlxZzJVN3NJTDRNdndqd0FwSE9TT2grbHBlemVDY28iLCJtYWMiOiI1MzM5ZDEyNzcwYTk3YTljNmU4MDk4NmZhY2M3NTY2YWVjNGVhMTk2ZGFlYWUyYmU4ODI4ZTY5MWYzMTQ3MzIwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1778325423\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-883991090 data-indent-pad=\"  \"><span class=sf-dump-note>array:37</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">D:\\Topan\\cms-juraganbeku\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">53126</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"30 characters\">PHP/8.3.6 (Development Server)</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"6 characters\">/login</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"41 characters\">D:\\Topan\\cms-juraganbeku\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"6 characters\">/login</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"16 characters\">/index.php/login</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"2 characters\">83</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"2 characters\">83</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"44 characters\">en-US,en;q=0.9,id;q=0.8,zh-CN;q=0.7,zh;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"808 characters\">_ga=GA1.1.587546091.1749434964; _ga_GBZ3SGGX85=GS2.1.s1751877726$o26$g0$t1751877726$j60$l0$h0; XSRF-TOKEN=eyJpdiI6IkEvYkM4eFBWUzZTblh4S3IxR2ZOTUE9PSIsInZhbHVlIjoiUG1vZzgyU3dicUJuUnYvR1hWR1gzWGw2bnZYZ3QvVnBZQU14TDFzREVvQmVqdk5TZ3l3bGNxL252RXdlWlByZjMzcGFURzNLVG5pWUFua08wd0lVc2x3V1NKRlgrdENLaG00VW9pdy9uNldDRmowZE0zZEFsSmw4eTRvd20wazkiLCJtYWMiOiIyZWIyNDliOTFlOTVjY2E5ZWViZDUzODMwMGJjMjE4YjE3YzJiZmU2NDc5MGVjMzBlY2M4NTUyZDAwYzFiYjlmIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InUxSTlYSnNYU3hQYVdXSXlWMEFmTnc9PSIsInZhbHVlIjoiTjRERHgwQTNETEJnQjN3MjViWS91RmlnZVlSTUI1K29GeVQzMldlUC9aaXJEVUZyWmM1UzNMQVBsWThDVmFOVTVaU3lvWlhZajZLdGRZUTZhdEhySXBUb2h2RDVyMWtidFlxZzJVN3NJTDRNdndqd0FwSE9TT2grbHBlemVDY28iLCJtYWMiOiI1MzM5ZDEyNzcwYTk3YTljNmU4MDk4NmZhY2M3NTY2YWVjNGVhMTk2ZGFlYWUyYmU4ODI4ZTY5MWYzMTQ3MzIwIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1752048156.8401</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1752048156</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-883991090\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-771290856 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_GBZ3SGGX85</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">uqU8XcYdjZEChvw0H37yWr6HFtCneckJnENtrf9H</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">126wG4hebAiaQyBxiFLVTw3M3rvMyMRahdhWTtrw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-771290856\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-501796211 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 09 Jul 2025 08:02:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/administrator</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjVidmtMN3FuZWowc2FNYkRYcTZ4WFE9PSIsInZhbHVlIjoiaTdVRld6aFFxU0lvWVZlbElQTkYrUXJCME5ucThhSHNaQkw3bjdWalFjeUdNaVY5VTRmWlJScGQxWXVrVjJwQThHWHVuczFvQXhORWF6Y3NIRUQwV2plb3A2L0NqNXR1Y0R1RlEvTWc4TE1CUU1ZRWpWNVQ5TjQ3UG9sNWlrQ0QiLCJtYWMiOiI3N2JlMzZlNjZkZjkwMDAwYmM2MGQ2Njg2YjRiOTE5ZWIwMDNhODU0ZGZiNjA3N2ZlNTNmM2VlZTZmNWY2Mjk2IiwidGFnIjoiIn0%3D; expires=Wed, 09-Jul-2025 10:02:40 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6Im5uYVpWaXd4bkFVU09DMFhxSitEYkE9PSIsInZhbHVlIjoiUk93K2M1VHpKTzVCTncwZ3lCU2pUQkU0U2t0MTJTMlQ4SlZ5ZGRVNVJURXlpNW5WdE5OUHdMUmNNaEdzdE1uNUs3NXV2VHVsSHBFRHFNUktJSWJFU1MwZkNwMTY3a1U1WjNVRmxncGRBUWF2L05jUU5vZkJWOVdEVzdaR08yemsiLCJtYWMiOiIzYzY4ZGFlNzY0MDE5YTcwYzIyNmUxNGQ4ZTRiMjk5OTZhMDZmZTZmN2Q1MjkxMjkxMDBjYWFmMTZhNGJlYjY2IiwidGFnIjoiIn0%3D; expires=Wed, 09-Jul-2025 10:02:40 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjVidmtMN3FuZWowc2FNYkRYcTZ4WFE9PSIsInZhbHVlIjoiaTdVRld6aFFxU0lvWVZlbElQTkYrUXJCME5ucThhSHNaQkw3bjdWalFjeUdNaVY5VTRmWlJScGQxWXVrVjJwQThHWHVuczFvQXhORWF6Y3NIRUQwV2plb3A2L0NqNXR1Y0R1RlEvTWc4TE1CUU1ZRWpWNVQ5TjQ3UG9sNWlrQ0QiLCJtYWMiOiI3N2JlMzZlNjZkZjkwMDAwYmM2MGQ2Njg2YjRiOTE5ZWIwMDNhODU0ZGZiNjA3N2ZlNTNmM2VlZTZmNWY2Mjk2IiwidGFnIjoiIn0%3D; expires=Wed, 09-Jul-2025 10:02:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6Im5uYVpWaXd4bkFVU09DMFhxSitEYkE9PSIsInZhbHVlIjoiUk93K2M1VHpKTzVCTncwZ3lCU2pUQkU0U2t0MTJTMlQ4SlZ5ZGRVNVJURXlpNW5WdE5OUHdMUmNNaEdzdE1uNUs3NXV2VHVsSHBFRHFNUktJSWJFU1MwZkNwMTY3a1U1WjNVRmxncGRBUWF2L05jUU5vZkJWOVdEVzdaR08yemsiLCJtYWMiOiIzYzY4ZGFlNzY0MDE5YTcwYzIyNmUxNGQ4ZTRiMjk5OTZhMDZmZTZmN2Q1MjkxMjkxMDBjYWFmMTZhNGJlYjY2IiwidGFnIjoiIn0%3D; expires=Wed, 09-Jul-2025 10:02:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-501796211\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1417190673 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QoZh1I8mypHP2aMUh0DEtCk2CpnpWBdxW5ZtIuuB</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"36 characters\">c065dc93-6b2f-4bf5-b746-6bb48713d4bf</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1417190673\", {\"maxDepth\":0})</script>\n"}}