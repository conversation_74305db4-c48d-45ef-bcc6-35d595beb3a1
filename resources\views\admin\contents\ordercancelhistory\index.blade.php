@extends('admin.layouts.main')
@section('title', 'OrderCancelHistory')

@section('stylesheet')

@endsection

@section('breadcumbs')
@include('admin.templates.breadcrumbs')
@endsection

@section('content')
<div class="row">
    <div class="col-sm-12">
        <div class="card">
            <div class="card-header">
                <h5>OrderCancelHistory Table</h5>

            </div>
            <div class="card-body">
                @php
                $current_path = \Request::route()->getName();
                getPagesAccess($current_path);
                @endphp
                <form action="" id="formFilter">

                    <div class="col-md-6 pl-2 mt-2">
                        <form class="form form-horizontal" id="formFilterReport">
                            <div class="row">

                                <div class="col-12">
                                    <div class="form-group row">
                                        <div class="col-sm-3 col-form-label">
                                            <label for="province_id">Order Code</label>
                                        </div>
                                        <div class="col-sm-9">
                                            <input type="text" class="form-control" id="no_order" name="no_order">
                                        </div>
                                    </div>
                                </div>



                                <div class="col-12">
                                    <div class="form-group row">

                                        <div class="col-sm-12">

                                            <button type="button" class="btn btn-rounded btn-primary text-bold submit-filter" style="float: right !important;">
                                                Filter
                                            </button>

                                        </div>
                                    </div>
                                </div>

                            </div>
                        </form>


                    </div>

                </form>
                <div class="card-datatable table-responsive">
                    <table id="contentTable" class="display table nowrap table-striped table-hover" style="width:100%">
                        <thead>
                            <tr class="table100-head">
                                <th width="3%" class="text-center">No</th>
                                <th class="">Order Code</th>
                                <th class="">Alasan</th>
                                <th class="">Cancel by</th>
                                <th class="">Tanggal Cancel</th>
                                <!-- <th class="text-center">Action</th> -->
                            </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Modal -->
@include('admin.contents.ordercancelhistory._modal')
@endsection

@section('script')

<script type="text/javascript">
    var url = {
        detail: "{{route('dashboard_ordercancelhistories_detail')}}",
        delete: "{{route('dashboard_ordercancelhistories_delete')}}",
        submit: "{{route('dashboard_ordercancelhistories_post')}}",
        table: "{{route('dashboard_ordercancelhistories_table')}}"
    };
    var table;


    $(document).ready(function() {
        var CSRF_TOKEN = "{{@csrf_token()}}";
        loadDataTable();
        $(document).on('click', '.submit-filter', function(e) {
            $('#formFilter').submit();
        });

        $('#formFilter').validate({ // initialize the plugin
            rules: {
                no_order: {
                    required: true,
                },
            },
            submitHandler: function(form) {
                let noOrder = $('#no_order').val();

                console.log(noOrder);
                $('#contentTable').dataTable().fnDestroy();
                //
                loadDataTable(noOrder);
                // let data = $('#formFilter').serialize();


            }
        });

        $(document).on('click', '.view', function(e) {
            let id = $(this).data('id');
            e.preventDefault();
            formDisable();
            modalShow('myModal', 'View Data');
            $.get(url.detail, {
                id: id
            }, function(result) {

                let response = result.data;

            });

        });

        $(document).on('click', '.update', function(e) {
            let id = $(this).data('id');
            e.preventDefault();
            formEnable();
            modalShow('myModal', 'Update Data');

            $.get(url.detail, {
                id: id
            }, function(result) {
                let response = result.data;
                $('#id').val(response.id)

            });

        });
        $(document).on('click', '.delete', function(e) {
            e.preventDefault();
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': CSRF_TOKEN
                }
            });
            Swal.fire({
                title: `Are you sure delete ${$(this).data('name')}?`,
                text: "You won't be able to revert this!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, delete it!',
            }).then((result) => {
                if (result.value) {
                    $.ajax({
                            url: url.delete,
                            method: 'GET',
                            data: {
                                id: $(this).data('id'),
                            },
                        })
                        .then((result) => {
                            swalStatus(result, "myModal")
                        }).then(() => {
                            tableReload(table)
                        });
                }
            });
        });


        $('#formModal').validate({ // initialize the plugin
            rules: {

            },
            submitHandler: function(form) {
                let data = $('#formModal').serialize();

                $.post(url.submit, data, function(result) {
                    swalStatus(result, "myModal", '', table)
                });
            }
        });

    });

    function loadDataTable(noOrder) {
        // console.log(noOrder);
        table = $('#contentTable').DataTable({
            processing: true,
            serverSide: true,
            searching: false, // Disable search feature
            ajax: {
                type: 'GET',
                url: url.table,
                data: {
                    'no_order': noOrder,
                }
            },
            columns: [{
                    data: 'DT_RowIndex',
                    name: 'id',
                    title: '#',
                    width: '2%'
                },
                {
                    data: 'order.order_code',
                    name: 'order.order_code',
                    defaultContent: '-',
                    'searchable': false
                },
                {
                    data: 'reason',
                    name: 'reason',
                    defaultContent: '-',
                    'searchable': false
                },
                {
                    data: 'user.fullname',
                    name: 'user.fullname',
                    'searchable': false
                },
                {
                    data: 'created_at',
                    name: 'created_at',
                    defaultContent: '-',
                    'searchable': false,
                    "render": function(data) {
                        return new moment(data).format("DD-MM-YYYY")
                    }
                },
                // {
                //     data: 'action',
                //     name: 'action',
                //     orderable: false,
                //     searchable: false,
                //     className: 'text-center',
                //     width: '15%'
                // },
            ]
        });
    }
</script>


@endsection