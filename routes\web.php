<?php
use App\Http\Controllers\Generator\OrderCancelHistoryController;
use App\Http\Controllers\Generator\ProductStockHistoryController;
use App\Http\Controllers\Generator\FakturHistoryController;

use App\Http\Controllers\Generator\DataLeadResellerController;

use App\Http\Controllers\Generator\InsertVariantController;

use App\Http\Controllers\Generator\MoveHubReceiveController;

use App\Http\Controllers\Generator\MoveHubCartRequestController;

use App\Http\Controllers\Generator\MoveHubRequestItemController;
use App\Http\Controllers\Generator\MoveHubRequestController;

use App\Http\Controllers\Generator\Inbound\WarehouseStockController;

use App\Http\Controllers\Generator\Inbound\ProductStockController;

use App\Http\Controllers\Generator\Inbound\ProductReceiveController;

use App\Http\Controllers\Generator\CartRequestController;

use App\Http\Controllers\Generator\Inbound\ProductRequestItemController;

use App\Http\Controllers\Generator\Inbound\ProductRequestController;

use App\Http\Controllers\Generator\BroadcastCampignController;

use App\Http\Controllers\Generator\OrderItemController;

use App\Http\Controllers\Generator\ReportStoreMonitoringClientController;

use App\Http\Controllers\Generator\RequestPaymentSettingController;

use App\Http\Controllers\Generator\PaymentSettingController;

use App\Http\Controllers\Generator\ReportOrderKonsinyasiController;

use App\Http\Controllers\Generator\OrderInvKonsinyasiController;
use App\Http\Controllers\Generator\OrderProductKonsinyasiController;

use App\Http\Controllers\Generator\KonsinyiasiUserController;
use App\Http\Controllers\Generator\KonsinyiasiProductController;

use App\Http\Controllers\Generator\MultipleAssignOrderController;

use App\Http\Controllers\Generator\MultipleAssignStoreMonitoringController;
use App\Http\Controllers\Generator\FakturConfirmController;

use App\Http\Controllers\Generator\ProductTypeController;
use App\Http\Controllers\Generator\FreezerCapacityController;

use App\Http\Controllers\Generator\TerminApprovalController;
use App\Http\Controllers\Generator\RepaymentHistoryController;

use App\Http\Controllers\Generator\CronAutoAssignHubController;
use App\Http\Controllers\Generator\CronAutoServeOrderController;
use App\Http\Controllers\Generator\ReportStoreMonitoringController;
use App\Http\Controllers\Generator\NotificationController;

use App\Http\Controllers\Generator\CancelOrderController;
use App\Http\Controllers\Generator\ResellerSyncBulkController;
use App\Http\Controllers\Generator\ResellerSyncController;
use App\Http\Controllers\Generator\StoreMonitoringAssignVisitController;
use App\Http\Controllers\Generator\StoreMonitoringAssignController;
use App\Http\Controllers\Generator\MonitoringQuestionDetailController;
use App\Http\Controllers\Generator\MonitoringQuestionController;
use App\Http\Controllers\Generator\StoreProblemController;
use App\Http\Controllers\Generator\StorePointController;
use App\Http\Controllers\Generator\StoreFreezerBrandController;
use App\Http\Controllers\Generator\StoreOwnerhipController;
use App\Http\Controllers\Generator\StoreTypeController;
use App\Http\Controllers\Generator\StoreLocationController;
use App\Http\Controllers\Generator\ReportAgentVaController;
use App\Http\Controllers\Generator\CronHistoryVaAgentController;
use App\Http\Controllers\Generator\ReportAgentVisitController;
use App\Http\Controllers\Generator\OrderCodReleaseController;
use App\Http\Controllers\Generator\OrderCODTransferController;
use App\Http\Controllers\Generator\OrderTerminAssignController;
use App\Http\Controllers\Generator\OrderTerminController;
use App\Http\Controllers\Generator\ProspectVisitHistoryController;
use App\Http\Controllers\Generator\ResellerVisitHistoryController;
use App\Http\Controllers\Generator\DashboardAgentController;

use App\Http\Controllers\Generator\ReqAgentController;
use App\Http\Controllers\Generator\AreaAgentController;
use App\Http\Controllers\Generator\PegawaiController;
use App\Http\Controllers\Generator\DashboardOrderingController;
use App\Http\Controllers\Generator\DashboardAnalyticController;
use App\Http\Controllers\Generator\ReportPaymentDetailController;
use App\Http\Controllers\Generator\ReportDeliveryController;
use App\Http\Controllers\Generator\ReportHubTransaksiController;
use App\Http\Controllers\Generator\ReportOrderController;
use App\Http\Controllers\Generator\ReportOrderManualController;
use App\Http\Controllers\Generator\AgentDepositController;
use App\Http\Controllers\Generator\TopanBankController;
use App\Http\Controllers\Generator\MstBankController;
use App\Http\Controllers\Generator\IntegrationUsageController;
use App\Http\Controllers\Generator\IntegrationController;
use App\Http\Controllers\Generator\AgentPickupController;
use App\Http\Controllers\Generator\AreaProductController;
use App\Http\Controllers\Generator\TypeProductController;
use App\Http\Controllers\Generator\BankController;
use App\Http\Controllers\Generator\HubBankController;
use App\Http\Controllers\Generator\HubTransactionHistoryController;

use App\Http\Controllers\Generator\DataResellerController;
use App\Http\Controllers\Generator\DataProspectController;
use App\Http\Controllers\Generator\CanvasingController;
use App\Http\Controllers\Generator\FacilityController;
use App\Http\Controllers\Generator\BusinesTypeController;
use App\Http\Controllers\Generator\HubTransactionController;
use App\Http\Controllers\Generator\AgentController;
use App\Http\Controllers\Generator\AgentTypeController;
use App\Http\Controllers\Generator\contentDetailController;
use App\Http\Controllers\Generator\ContentHeaderController;
use App\Http\Controllers\Generator\PaymentMethodController;
use App\Http\Controllers\Generator\OrderController;
use App\Http\Controllers\Generator\MembershipController;
use App\Http\Controllers\Generator\VoucherController;
use App\Http\Controllers\Generator\ShippingCostController;
use App\Http\Controllers\Generator\PrincipalController;
use App\Http\Controllers\Generator\SequenceCodeController;
use App\Http\Controllers\Generator\ResellerController;
use App\Http\Controllers\Generator\LogicController;
use App\Http\Controllers\Generator\ReqUsersController;
use App\Http\Controllers\Generator\ReqAreaCoveredController;
use App\Http\Controllers\Generator\SectionBannerController;
use App\Http\Controllers\Generator\SectionController;
use App\Http\Controllers\Generator\TypeController;
use App\Http\Controllers\Generator\SegmentController;
use App\Http\Controllers\Generator\ProductController;
use App\Http\Controllers\Generator\VariantController;
use App\Http\Controllers\Generator\HubController;
use App\Http\Controllers\Generator\AreaController;
use App\Http\Controllers\Generator\BrandController;
use App\Http\Controllers\Generator\CategoryController;


use App\Http\Controllers\DashboardController;
use App\Http\Controllers\Generator\Akumaju\AkumajuBrandController;
use App\Http\Controllers\Generator\Akumaju\AkumajuCategoryController;
use App\Http\Controllers\Generator\Akumaju\AkumajuPrincipalController;
use App\Http\Controllers\Generator\VaAgentController;
use App\Http\Controllers\GroupConttroller;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\Generator\HistoryPaymentController;
use App\Http\Controllers\Generator\HistoryPayment2Controller;
use App\Http\Controllers\Generator\HistoryPaymentHubController;
use App\Http\Controllers\Generator\StoreMonitoringClientController;
use App\Http\Controllers\HubPaymentController;
use App\Http\Controllers\Location\DistrictController;
use App\Http\Controllers\Location\ProvinceController;
use App\Http\Controllers\Location\RegencyController;
use App\Http\Controllers\Location\VillagesController;
use App\Http\Controllers\MaintenanceController;
use App\Http\Controllers\MenuController;
use App\Http\Controllers\Monitoring\HubProductController;
use App\Http\Controllers\OcrController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\SettingController;
use App\Http\Controllers\UserController;
use Illuminate\Support\Facades\Route;


/**
 * <AUTHOR> Priyanto<<EMAIL>>
 */

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/
Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('/maintenance', [MaintenanceController::class, 'index'])->name('maintenance');
Route::get('/comingsoon', [MaintenanceController::class, 'comingsoon'])->name('maintenance');

Route::group(['prefix' => 'cronautoassignhub'], function () {
    Route::get('/', [CronAutoAssignHubController::class, 'index'])->name('dashboard_cronautoassignhub');
    Route::get('/run', [CronAutoAssignHubController::class, 'run'])->name('dashboard_cronautoassignhub_init');
});
Route::group(['prefix' => 'cronautoserveorder'], function () {
    Route::get('/', [CronAutoServeOrderController::class, 'index'])->name('dashboard_cronautoserveorder');
    Route::get('/run', [CronAutoServeOrderController::class, 'run'])->name('dashboard_cronautoserveorder_init');
});
Auth::routes();

Route::group(['prefix' => 'administrator', 'middleware' => ['auth', 'roles']], function () {
    Route::get('/', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('/delete_file', [DashboardController::class, 'deleteFileContent'])->name('file_delete');


    Route::group(['prefix'=>'ordercancelhistories'], function () {
        Route::get('/', [OrderCancelHistoryController::class, 'index'])->name('dashboard_ordercancelhistories');
        Route::get('/get', [OrderCancelHistoryController::class, 'get'])->name('dashboard_ordercancelhistories_detail');
        Route::get('/delete', [OrderCancelHistoryController::class, 'destroy'])->name('dashboard_ordercancelhistories_delete');
        Route::post('/', [OrderCancelHistoryController::class, 'store'])->name('dashboard_ordercancelhistories_post');
        Route::get('/datatable.json', [OrderCancelHistoryController::class ,'__datatable'])->name('dashboard_ordercancelhistories_table');
    });

    Route::group(['prefix'=>'productstockhistories'], function () {
        Route::get('/', [ProductStockHistoryController::class, 'index'])->name('dashboard_productstockhistories');
        Route::get('/get', [ProductStockHistoryController::class, 'get'])->name('dashboard_productstockhistories_detail');
        Route::get('/delete', [ProductStockHistoryController::class, 'destroy'])->name('dashboard_productstockhistories_delete');
        Route::post('/', [ProductStockHistoryController::class, 'store'])->name('dashboard_productstockhistories_post');
        Route::get('/datatable.json', [ProductStockHistoryController::class ,'__datatable'])->name('dashboard_productstockhistories_table');
    });

    Route::group(['prefix'=>'fakturhistories'], function () {
        Route::get('/', [FakturHistoryController::class, 'index'])->name('dashboard_fakturhistories');
        Route::get('/get', [FakturHistoryController::class, 'get'])->name('dashboard_fakturhistories_detail');
        Route::get('/delete', [FakturHistoryController::class, 'destroy'])->name('dashboard_fakturhistories_delete');
        Route::post('/', [FakturHistoryController::class, 'store'])->name('dashboard_fakturhistories_post');
        Route::get('/datatable.json', [FakturHistoryController::class ,'__datatable'])->name('dashboard_fakturhistories_table');
    });

    Route::group(['prefix' => 'dataleadresellers'], function () {
        Route::get('/', [DataLeadResellerController::class, 'index'])->name('dashboard_dataleadresellers');
        Route::get('/get', [DataLeadResellerController::class, 'get'])->name('dashboard_dataleadresellers_detail');
        Route::get('/delete', [DataLeadResellerController::class, 'destroy'])->name('dashboard_dataleadresellers_delete');
        Route::post('/', [DataLeadResellerController::class, 'store'])->name('dashboard_dataleadresellers_post');
        Route::get('/datatable.json', [DataLeadResellerController::class, '__datatable'])->name('dashboard_dataleadresellers_table');
    });

    Route::group(['prefix' => 'insertvariants'], function () {
        Route::get('/', [InsertVariantController::class, 'index'])->name('dashboard_insertvariants');
        Route::get('/get', [InsertVariantController::class, 'get'])->name('dashboard_insertvariants_detail');
        Route::get('/delete', [InsertVariantController::class, 'destroy'])->name('dashboard_insertvariants_delete');
        Route::post('/', [InsertVariantController::class, 'store'])->name('dashboard_insertvariants_post');
        Route::get('/datatable.json', [InsertVariantController::class, '__datatable'])->name('dashboard_insertvariants_table');
    });

    Route::group(['prefix' => 'movehubreceives'], function () {
        Route::get('/', [MoveHubReceiveController::class, 'index'])->name('dashboard_movehubreceives');
        Route::get('/get', [MoveHubReceiveController::class, 'get'])->name('dashboard_movehubreceives_detail');
        Route::get('/list', [MoveHubReceiveController::class, 'detailUserProduct'])->name('dashboard_movehubreceives_detail_product');
        Route::get('/item', [MoveHubReceiveController::class, 'getRecieve'])->name('dashboard_movehubreceives_item');
        Route::get('/delete', [MoveHubReceiveController::class, 'destroy'])->name('dashboard_movehubreceives_delete');
        Route::post('/', [MoveHubReceiveController::class, 'store'])->name('dashboard_movehubreceives_post');
        Route::get('/datatable.json', [MoveHubReceiveController::class, '__datatable'])->name('dashboard_movehubreceives_table');
        Route::get('/download-report', [MoveHubReceiveController::class, 'downloadReport'])->name('dashboard_movehubreceives_download');
    });

    Route::group(['prefix' => 'movehubcartrequests'], function () {
        Route::get('/', [MoveHubCartRequestController::class, 'index'])->name('dashboard_movehubcartrequests');
        Route::get('/get', [MoveHubCartRequestController::class, 'get'])->name('dashboard_movehubcartrequests_detail');
        Route::get('/delete', [MoveHubCartRequestController::class, 'destroy'])->name('dashboard_movehubcartrequests_delete');
        Route::post('/', [MoveHubCartRequestController::class, 'store'])->name('dashboard_movehubcartrequests_post');
        Route::get('/datatable.json', [MoveHubCartRequestController::class, '__datatable'])->name('dashboard_movehubcartrequests_table');
    });

    Route::group(['prefix' => 'movehubrequestitems'], function () {
        Route::get('/', [MoveHubRequestItemController::class, 'index'])->name('dashboard_movehubrequestitems');
        Route::get('/get', [MoveHubRequestItemController::class, 'get'])->name('dashboard_movehubrequestitems_detail');
        Route::get('/delete', [MoveHubRequestItemController::class, 'destroy'])->name('dashboard_movehubrequestitems_delete');
        Route::post('/', [MoveHubRequestItemController::class, 'store'])->name('dashboard_movehubrequestitems_post');
        Route::get('/datatable.json', [MoveHubRequestItemController::class, '__datatable'])->name('dashboard_movehubrequestitems_table');
    });

    Route::group(['prefix' => 'movehubrequests'], function () {
        Route::get('/', [MoveHubRequestController::class, 'index'])->name('dashboard_movehubrequests');
        Route::get('/get', [MoveHubRequestController::class, 'get'])->name('dashboard_movehubrequests_detail');
        Route::get('/list', [MoveHubRequestController::class, 'detailUserProduct'])->name('dashboard_movehubrequests_detail_product');
        Route::get('/delete', [MoveHubRequestController::class, 'destroy'])->name('dashboard_movehubrequests_delete');
        Route::post('/', [MoveHubRequestController::class, 'store'])->name('dashboard_movehubrequests_post');
        Route::get('/add', [MoveHubRequestController::class, 'move'])->name('dashboard_pindah_hub');
        Route::get('/edit', [MoveHubRequestController::class, 'edit'])->name('dashboard_movehubrequests_edit');
        Route::get('/reject', [MoveHubRequestController::class, 'reject'])->name('dashboard_movehubrequests_reject');
        Route::get('/approve', [MoveHubRequestController::class, 'approve'])->name('dashboard_movehubrequests_approve');
        Route::get('/reject', [MoveHubRequestController::class, 'reject'])->name('dashboard_movehubrequests_reject');
        Route::get('/add-to-cart', [MoveHubRequestController::class, 'addToCart'])->name('add.to.cart');
        // Route::patch('/update-cart', [ProductRequestController::class, 'updateCart'])->name('update.cart');
        Route::delete('/remove-from-cart', [MoveHubRequestController::class, 'removeCart'])->name('remove.from.cart');
        Route::get('/datatable.json', [MoveHubRequestController::class, '__datatable'])->name('dashboard_movehubrequests_table');
        Route::get('/datatable-move-hub.json', [MoveHubRequestController::class, '__datatable_stock'])->name('dashboard_movehub_item_table');
        Route::get('/datatable_cart.json', [MoveHubRequestController::class, '__datatable_cart'])->name('dashboard_movehubrequests_table_cart');
        Route::get('/datatable_edit.json', [MoveHubRequestController::class, '__datatable_edit'])->name('dashboard_movehubrequests_table_edit');
        Route::post('/submit-request', [MoveHubRequestController::class, 'submitRequest'])->name('dashboard_submitmovehubcart_post');
        Route::post('/submit-edit', [ProductRequestController::class, 'updateRequest'])->name('dashboard_submitedit_post');
        Route::post('/change-request-stock-hub', [MoveHubRequestController::class, 'changeReqStockBulk'])->name('dashboard_req_change_stock_bulk');
        Route::get('/datatable-product.json', [MoveHubRequestController::class, 'detailUserProduct'])->name('dashboard_movehubrequests_table_product');
        Route::get('/download-report', [MoveHubRequestController::class, 'downloadReport'])->name('dashboard_movehubrequests_download');
    });

    Route::group(['prefix' => 'warehousestocks'], function () {
        Route::get('/', [WarehouseStockController::class, 'index'])->name('dashboard_warehousestocks');
        Route::get('/get', [WarehouseStockController::class, 'get'])->name('dashboard_warehousestocks_detail');
        Route::get('/get-id', [WarehouseStockController::class, 'getById'])->name('dashboard_warehousestocks_byid');
        Route::get('/get-stock', [WarehouseStockController::class, 'getStockId'])->name('dashboard_warehousestocks_get_product');
        Route::get('/get-variant-value', [WarehouseStockController::class, 'getCartonValue'])->name('dashboard_warehousestocks_get_carton');
        Route::get('/delete', [WarehouseStockController::class, 'destroy'])->name('dashboard_warehousestocks_delete');
        Route::post('/', [WarehouseStockController::class, 'store'])->name('dashboard_warehousestocks_post');
        Route::post('/convert', [WarehouseStockController::class, 'convert'])->name('dashboard_warehousestocks_convert_post');
        Route::post('/convert-pack', [WarehouseStockController::class, 'convertPack'])->name('dashboard_warehousestocks_convert_pack_post');
        Route::get('/list', [WarehouseStockController::class, 'getStockId'])->name('dashboard_warehousestocks_detail_product');
        // Route::get('/edit', [WarehouseStockController::class, 'edit'])->name('dashboard_warehousestocks_edit');
        Route::get('/list-stock', [WarehouseStockController::class, 'edit'])->name('dashboard_warehousestocks_list');
        // Route::get('/move-stock', [WarehouseStockController::class, 'move'])->name('dashboard_pindah_hub');
        Route::get('/datatable-list.json', [WarehouseStockController::class, '__datatable_list'])->name('dashboard_productstock_table_list');
        Route::get('/datatable.json', [WarehouseStockController::class, '__datatable'])->name('dashboard_warehousestocks_table');
        Route::get('/download-report-stock-in-hub', [WarehouseStockController::class, 'downloadReport'])->name('dashboard_warehouse_product_report');
        // Route::get('/datatable-move-hub.json', [WarehouseStockController::class, '__datatable_stock'])->name('dashboard_movehub_table');
    });

    Route::group(['prefix' => 'productstocks'], function () {
        Route::get('/', [ProductStockController::class, 'index'])->name('dashboard_productstocks');
        Route::get('/get', [ProductStockController::class, 'get'])->name('dashboard_productstocks_detail');
        Route::get('/delete', [ProductStockController::class, 'destroy'])->name('dashboard_productstocks_delete');
        Route::post('/', [ProductStockController::class, 'store'])->name('dashboard_productstocks_post');
        Route::get('/datatable.json', [ProductStockController::class, '__datatable'])->name('dashboard_productstocks_table');
        Route::get('/download-excel', [ProductStockController::class, 'downloadExcel'])->name('dashboard_productstocks_download');
    });

    Route::group(['prefix' => 'productreceives'], function () {
        Route::get('/', [ProductReceiveController::class, 'index'])->name('dashboard_productreceives');
        Route::get('/get', [ProductReceiveController::class, 'get'])->name('dashboard_productreceives_detail');
        Route::get('/item', [ProductReceiveController::class, 'getRecieve'])->name('dashboard_productreceives_item');
        Route::get('/delete', [ProductReceiveController::class, 'destroy'])->name('dashboard_productreceives_delete');
        Route::get('/close', [ProductReceiveController::class, 'close'])->name('dashboard_productreceives_close');
        Route::post('/', [ProductReceiveController::class, 'store'])->name('dashboard_productreceives_post');
        Route::get('/datatable.json', [ProductReceiveController::class, '__datatable'])->name('dashboard_productreceives_table');
        Route::get('/recieve', [ProductReceiveController::class, 'recieve'])->name('dashboard_productreceives_recieve');
        Route::get('/download-excel', [ProductReceiveController::class, 'downloadExcel'])->name('dashboard_productreceives_download');
    });

    Route::group(['prefix' => 'cartrequests'], function () {
        Route::get('/', [CartRequestController::class, 'index'])->name('dashboard_cartrequests');
        Route::get('/get', [CartRequestController::class, 'get'])->name('dashboard_cartrequests_detail');
        Route::get('/delete', [CartRequestController::class, 'destroy'])->name('dashboard_cartrequests_delete');
        Route::post('/', [CartRequestController::class, 'store'])->name('dashboard_cartrequests_post');
        Route::get('/datatable.json', [CartRequestController::class, '__datatable'])->name('dashboard_cartrequests_table');
    });

    Route::group(['prefix' => 'productrequestitems'], function () {
        Route::get('/', [ProductRequestItemController::class, 'index'])->name('dashboard_productrequestitems');
        Route::get('/get', [ProductRequestItemController::class, 'get'])->name('dashboard_productrequestitems_detail');
        Route::get('/delete', [ProductRequestItemController::class, 'destroy'])->name('dashboard_productrequestitems_delete');
        Route::post('/', [ProductRequestItemController::class, 'store'])->name('dashboard_productrequestitems_post');
        Route::get('/datatable.json', [ProductRequestItemController::class, '__datatable'])->name('dashboard_productrequestitems_table');
    });

    Route::group(['prefix' => 'productrequests'], function () {
        Route::get('/', [ProductRequestController::class, 'index'])->name('dashboard_productrequests');
        Route::get('/get', [ProductRequestController::class, 'get'])->name('dashboard_productrequests_detail');
        Route::get('/list', [ProductRequestController::class, 'detailUserProduct'])->name('dashboard_productrequests_detail_product');
        Route::get('/delete', [ProductRequestController::class, 'destroy'])->name('dashboard_productrequests_delete');
        Route::get('/delete-item', [ProductRequestController::class, 'destroyItem'])->name('dashboard_productrequestitems_delete');
        Route::post('/', [ProductRequestController::class, 'store'])->name('dashboard_productrequests_post');
        Route::get('/add', [ProductRequestController::class, 'add'])->name('dashboard_productrequests_add');
        Route::get('/edit', [ProductRequestController::class, 'edit'])->name('dashboard_productrequests_edit');
        Route::get('/reject', [ProductRequestController::class, 'reject'])->name('dashboard_productrequests_reject');
        Route::get('/approve', [ProductRequestController::class, 'approve'])->name('dashboard_productrequests_approve');
        // Route::get('/cart', [ProductRequestController::class, 'cart'])->name('dashboard_cart');
        Route::get('/add-to-cart', [ProductRequestController::class, 'addToCart'])->name('add.to.cart');
        Route::patch('/update-cart', [ProductRequestController::class, 'updateCart'])->name('update.cart');
        Route::delete('/remove-from-cart', [ProductRequestController::class, 'removeCart'])->name('remove.from.cart');
        Route::get('/datatable.json', [ProductRequestController::class, '__datatable'])->name('dashboard_productrequestslist_table');
        Route::get('/datatable_requests.json', [ProductRequestController::class, '__datatable_request'])->name('dashboard_requestproduct_table');
        Route::get('/datatable_cart.json', [ProductRequestController::class, '__datatable_cart'])->name('dashboard_requestproduct_table_cart');
        Route::get('/datatable_edit.json', [ProductRequestController::class, '__datatable_edit'])->name('dashboard_requestproduct_table_edit');
        Route::post('/submit-request', [ProductRequestController::class, 'submitRequest'])->name('dashboard_submitcart_post');
        Route::post('/submit-edit', [ProductRequestController::class, 'updateRequest'])->name('dashboard_submitedit_post');
        Route::post('/cart/update', [ProductRequestController::class, 'update'])->name('cart.update');
        Route::post('/add-product-request', [ProductRequestController::class, 'storeEdit'])->name('dashboard_productrequests_edit_add_post');
        Route::post('/change-request-stock-bulk', [ProductRequestController::class, 'changeReqStockBulk'])->name('dashboard_req_product_change_req_stock_bulk');
        // Route::post('/upload/preinvoice/{id}', [ProductRequestController::class, 'upload'])->name('upload.preinvoice');
        Route::get('/download/preinvoice/{file}', [ProductRequestController::class, 'download'])->name('download.preinvoice');
        Route::post('/upload/preinvoice/ajax', [ProductRequestController::class, 'uploadAjax'])->name('upload.preinvoice.ajax');
        Route::get('/download-excel', [ProductRequestController::class, 'downloadExcel'])->name('dashboard_productrequests_download');
    });

    Route::group(['prefix' => 'broadcastcampigns'], function () {
        Route::get('/', [BroadcastCampignController::class, 'index'])->name('dashboard_broadcastcampigns');
        Route::get('/get', [BroadcastCampignController::class, 'get'])->name('dashboard_broadcastcampigns_detail');
        Route::get('/list', [BroadcastCampignController::class, 'detailBroadcastNumber'])->name('dashboard_broadcastcampigns_detail_number');
        Route::get('/delete', [BroadcastCampignController::class, 'destroy'])->name('dashboard_broadcastcampigns_delete');
        Route::post('/', [BroadcastCampignController::class, 'store'])->name('dashboard_broadcastcampigns_post');
        Route::get('/datatable.json', [BroadcastCampignController::class, '__datatable'])->name('dashboard_broadcastcampigns_table');
        Route::get('/list-numbers', [BroadcastCampignController::class, 'getListNumbers'])->name('dashboard_numbers_list');
        Route::get('/datatable-list.json', [BroadcastCampignController::class, '__datatable_list'])->name('dashboard_broadcastnumbers_table_list');
        Route::get('/get-numbers', [BroadcastCampignController::class, 'getDetailNumbers'])->name('dashboard_broadcastnumbers_detail');
        Route::post('/update', [KonsinyiasiProductController::class, 'update'])->name('dashboard_konsinyiasiproducts_product_variant_post');
        Route::get('/delete-numbers', [KonsinyiasiProductController::class, 'destroyVariant'])->name('dashboard_konsinyiasiproducts_delete_variant');
        // Route::get('/datatable-number.json', [BroadcastCampignController::class, 'detailVariantProduct'])->name('dashboard_konsinyiasiproducts_table_variant');
        Route::get('/search', [BroadcastCampignController::class, 'search'])->name('dashboard_phone_search');
    });

    Route::group(['prefix' => 'reportstoremonitoringclients'], function () {
        Route::get('/', [ReportStoreMonitoringClientController::class, 'index'])->name('dashboard_reportstoremonitoringclients');
        Route::get('/get', [ReportStoreMonitoringClientController::class, 'get'])->name('dashboard_reportstoremonitoringclients_detail');
        Route::get('/delete', [ReportStoreMonitoringClientController::class, 'destroy'])->name('dashboard_reportstoremonitoringclients_delete');
        Route::post('/', [ReportStoreMonitoringClientController::class, 'store'])->name('dashboard_reportstoremonitoringclients_post');
        Route::get('/datatable.json', [ReportStoreMonitoringClientController::class, '__datatable'])->name('dashboard_reportstoremonitoringclients_table');

        Route::get('/download-excell', [ReportStoreMonitoringClientController::class, 'downloadExcell'])->name('dashboard_reportstoremonitoringclients_report_excel');
        Route::get('/download-pdf', [ReportStoreMonitoringClientController::class, 'downloadPDF'])->name('dashboard_reportstoremonitoringclients_report_pdf');
    });


    Route::group(['prefix' => 'storemonitoringclients'], function () {
        Route::get('/', [StoreMonitoringClientController::class, 'index'])->name('dashboard_storemonitoringclients');
        Route::get('/get', [StoreMonitoringClientController::class, 'get'])->name('dashboard_storemonitoringclients_detail');
        Route::get('/delete', [StoreMonitoringClientController::class, 'destroy'])->name('dashboard_storemonitoringclients_delete');
        Route::post('/', [StoreMonitoringClientController::class, 'store'])->name('dashboard_storemonitoringclients_post');
        Route::get('/datatable.json', [StoreMonitoringClientController::class, '__datatable'])->name('dashboard_storemonitoringclients_table');

        Route::get('/download-excell', [StoreMonitoringClientController::class, 'downloadExcell'])->name('dashboard_storemonitoringclients_report_excel');
        Route::get('/download-pdf', [StoreMonitoringClientController::class, 'downloadPDF'])->name('dashboard_storemonitoringclients_report_pdf');
    });

    Route::group(['prefix' => 'requestpaymentsettings'], function () {
        Route::get('/', [RequestPaymentSettingController::class, 'index'])->name('dashboard_requestpaymentsettings');
        Route::get('/get', [RequestPaymentSettingController::class, 'get'])->name('dashboard_requestpaymentsettings_detail');
        Route::get('/delete', [RequestPaymentSettingController::class, 'destroy'])->name('dashboard_requestpaymentsettings_delete');
        Route::post('/', [RequestPaymentSettingController::class, 'store'])->name('dashboard_requestpaymentsettings_post');
        Route::get('/datatable.json', [RequestPaymentSettingController::class, '__datatable'])->name('dashboard_requestpaymentsettings_table');
    });

    Route::group(['prefix' => 'paymentsettings'], function () {
        Route::get('/', [PaymentSettingController::class, 'index'])->name('dashboard_paymentsettings');
        Route::get('/get', [PaymentSettingController::class, 'get'])->name('dashboard_paymentsettings_detail');
        Route::get('/delete', [PaymentSettingController::class, 'destroy'])->name('dashboard_paymentsettings_delete');
        Route::post('/', [PaymentSettingController::class, 'store'])->name('dashboard_paymentsettings_post');
        Route::get('/datatable.json', [PaymentSettingController::class, '__datatable'])->name('dashboard_paymentsettings_table');
    });

    Route::group(['prefix' => 'reportorderkonsinyasis'], function () {
        Route::get('/', [ReportOrderKonsinyasiController::class, 'index'])->name('dashboard_reportorderkonsinyasis');
        Route::get('/get', [ReportOrderKonsinyasiController::class, 'get'])->name('dashboard_reportorderkonsinyasis_detail');
        Route::get('/delete', [ReportOrderKonsinyasiController::class, 'destroy'])->name('dashboard_reportorderkonsinyasis_delete');
        Route::post('/', [ReportOrderKonsinyasiController::class, 'store'])->name('dashboard_reportorderkonsinyasis_post');
        Route::get('/datatable.json', [ReportOrderKonsinyasiController::class, '__datatable'])->name('dashboard_reportorderkonsinyasis_table');
        Route::get('/download-report-order-konsinyasi', [ReportOrderKonsinyasiController::class, 'downloadReportOrder'])->name('dashboard_reportorderkonsinyasi_download');
    });

    Route::group(['prefix' => 'orderinvkonsinyasis'], function () {
        Route::get('/', [OrderInvKonsinyasiController::class, 'index'])->name('dashboard_orderinvkonsinyasis');
        Route::get('/get', [OrderInvKonsinyasiController::class, 'get'])->name('dashboard_orderinvkonsinyasis_detail');
        Route::get('/delete', [OrderInvKonsinyasiController::class, 'destroy'])->name('dashboard_orderinvkonsinyasis_delete');
        Route::post('/', [OrderInvKonsinyasiController::class, 'store'])->name('dashboard_orderinvkonsinyasis_post');
        Route::get('/datatable.json', [OrderInvKonsinyasiController::class, '__datatable'])->name('dashboard_orderinvkonsinyasis_table');
        Route::get('/detail', [OrderInvKonsinyasiController::class, 'process'])->name('dashboard_orderinvkonsinyasis_process');
        Route::get('/print-invoice-konsinyasi', [OrderInvKonsinyasiController::class, 'print_invoice'])->name('dashboard_konsinyasi_print_invoice_pdf');
        Route::post('/confirmOrder', [OrderInvKonsinyasiController::class, 'submitConfirmOrder'])->name('dashboard_submit_confirm_order');
    });

    Route::group(['prefix' => 'orderproductkonsinyasis'], function () {
        Route::get('/', [OrderProductKonsinyasiController::class, 'index'])->name('dashboard_orderproductkonsinyasis');
        Route::get('/get', [OrderProductKonsinyasiController::class, 'get'])->name('dashboard_orderproductkonsinyasis_detail');
        Route::get('/delete', [OrderProductKonsinyasiController::class, 'destroy'])->name('dashboard_orderproductkonsinyasis_delete');
        Route::post('/', [OrderProductKonsinyasiController::class, 'store'])->name('dashboard_orderproductkonsinyasis_post');
        Route::get('/datatable.json', [OrderProductKonsinyasiController::class, '__datatable'])->name('dashboard_orderproductkonsinyasis_table');
        Route::post('/submit-inv', [OrderProductKonsinyasiController::class, 'submitInv'])->name('dashboard_submitinv_post');
        Route::get('/get-code', [OrderProductKonsinyasiController::class, 'generateOrderCode'])->name('dashboard_order_generate_code');
    });

    Route::group(['prefix' => 'konsinyiasiusers'], function () {
        Route::get('/', [KonsinyiasiUserController::class, 'index'])->name('dashboard_konsinyiasiusers');
        Route::get('/get', [KonsinyiasiUserController::class, 'get'])->name('dashboard_konsinyiasiusers_detail');
        Route::get('/list', [KonsinyiasiUserController::class, 'detailUserProduct'])->name('dashboard_konsinyiasiusers_detail_product');
        Route::get('/delete', [KonsinyiasiUserController::class, 'destroy'])->name('dashboard_konsinyiasiusers_delete');
        Route::post('/', [KonsinyiasiUserController::class, 'store'])->name('dashboard_konsinyiasiusers_post');
        Route::post('/add-product', [KonsinyiasiUserController::class, 'addProduct'])->name('dashboard_konsinyiasiusers_post_product');
        Route::get('/datatable.json', [KonsinyiasiUserController::class, '__datatable'])->name('dashboard_konsinyiasiusers_table');
        Route::get('/datatable-product.json', [KonsinyiasiUserController::class, 'detailUserProduct'])->name('dashboard_konsinyiasiusers_table_product');
    });

    Route::group(['prefix' => 'konsinyiasiproducts'], function () {
        Route::get('/', [KonsinyiasiProductController::class, 'index'])->name('dashboard_konsinyiasiproducts');
        Route::get('/get', [KonsinyiasiProductController::class, 'get'])->name('dashboard_konsinyiasiproducts_detail');
        Route::get('/list', [KonsinyiasiProductController::class, 'detailVariantProduct'])->name('dashboard_konsinyiasiproducts_detail_variant');
        Route::get('/delete', [KonsinyiasiProductController::class, 'destroy'])->name('dashboard_konsinyiasiproducts_delete');
        Route::post('/', [KonsinyiasiProductController::class, 'store'])->name('dashboard_konsinyiasiproducts_post');
        Route::post('/add-variant', [KonsinyiasiProductController::class, 'addVariant'])->name('dashboard_konsinyiasiproducts_post_variant');
        Route::get('/datatable.json', [KonsinyiasiProductController::class, '__datatable'])->name('dashboard_konsinyiasiproducts_table');
        Route::get('/datatable-variant.json', [KonsinyiasiProductController::class, 'detailVariantProduct'])->name('dashboard_konsinyiasiproducts_table_variant');
        Route::get('/edit', [KonsinyiasiProductController::class, 'edit'])->name('dashboard_konsinyiasiproducts_edit');
        Route::get('/list-variant', [KonsinyiasiProductController::class, 'getListVariant'])->name('dashboard_konsinyiasiproducts_list');
        Route::get('/datatable-list.json', [KonsinyiasiProductController::class, '__datatable_list'])->name('dashboard_konsinyiasiproducts_table_list');
        Route::get('/get-product-variant', [KonsinyiasiProductController::class, 'getProductVariant'])->name('dashboard_konsinyiasiproducts_detail_product_variant');
        Route::post('/update', [KonsinyiasiProductController::class, 'update'])->name('dashboard_konsinyiasiproducts_product_variant_post');
        Route::get('/delete-variant', [KonsinyiasiProductController::class, 'destroyVariant'])->name('dashboard_konsinyiasiproducts_delete_variant');
    });

    Route::group(['prefix' => 'multipleassignorders'], function () {
        Route::get('/', [MultipleAssignOrderController::class, 'index'])->name('dashboard_multipleassignorders');
        Route::get('/get', [MultipleAssignOrderController::class, 'get'])->name('dashboard_multipleassignorders_detail');
        Route::get('/delete', [MultipleAssignOrderController::class, 'destroy'])->name('dashboard_multipleassignorders_delete');
        Route::post('/', [MultipleAssignOrderController::class, 'store'])->name('dashboard_multipleassignorders_post');
        Route::get('/datatable.json', [MultipleAssignOrderController::class, '__datatable'])->name('dashboard_multipleassignorders_table');
        Route::post('/submit-assign', [MultipleAssignOrderController::class, 'submitAssign'])->name('dashboard_submitassignorder_post');
    });

    Route::group(['prefix' => 'multipleassignstoremonitorings'], function () {
        Route::get('/', [MultipleAssignStoreMonitoringController::class, 'index'])->name('dashboard_multipleassignstoremonitorings');
        Route::get('/get', [MultipleAssignStoreMonitoringController::class, 'get'])->name('dashboard_multipleassignstoremonitorings_detail');
        Route::get('/delete', [MultipleAssignStoreMonitoringController::class, 'destroy'])->name('dashboard_multipleassignstoremonitorings_delete');
        Route::post('/', [MultipleAssignStoreMonitoringController::class, 'store'])->name('dashboard_multipleassignstoremonitorings_post');
        Route::get('/datatable.json', [MultipleAssignStoreMonitoringController::class, '__datatable'])->name('dashboard_multipleassignstoremonitorings_table');
        Route::post('/submit-assign', [MultipleAssignStoreMonitoringController::class, 'submitAssign'])->name('dashboard_submitassign_post');
    });

    Route::group(['prefix' => 'fakturconfirms'], function () {
        Route::get('/', [FakturConfirmController::class, 'index'])->name('dashboard_fakturconfirms');
        Route::get('/get', [FakturConfirmController::class, 'get'])->name('dashboard_fakturconfirms_detail');
        Route::get('/delete', [FakturConfirmController::class, 'destroy'])->name('dashboard_fakturconfirms_delete');
        Route::post('/', [FakturConfirmController::class, 'store'])->name('dashboard_fakturconfirms_post');
        Route::get('/datatable.json', [FakturConfirmController::class, '__datatable'])->name('dashboard_fakturconfirms_table');
    });

    Route::group(['prefix' => 'producttypes'], function () {
        Route::get('/', [ProductTypeController::class, 'index'])->name('dashboard_producttypes');
        Route::get('/get', [ProductTypeController::class, 'get'])->name('dashboard_producttypes_detail');
        Route::get('/delete', [ProductTypeController::class, 'destroy'])->name('dashboard_producttypes_delete');
        Route::post('/', [ProductTypeController::class, 'store'])->name('dashboard_producttypes_post');
        Route::get('/datatable.json', [ProductTypeController::class, '__datatable'])->name('dashboard_producttypes_table');
    });

    Route::group(['prefix' => 'freezercapacities'], function () {
        Route::get('/', [FreezerCapacityController::class, 'index'])->name('dashboard_freezercapacities');
        Route::get('/get', [FreezerCapacityController::class, 'get'])->name('dashboard_freezercapacities_detail');
        Route::get('/delete', [FreezerCapacityController::class, 'destroy'])->name('dashboard_freezercapacities_delete');
        Route::post('/', [FreezerCapacityController::class, 'store'])->name('dashboard_freezercapacities_post');
        Route::get('/datatable.json', [FreezerCapacityController::class, '__datatable'])->name('dashboard_freezercapacities_table');
    });

    Route::group(['prefix' => 'terminapprovals'], function () {
        Route::get('/', [TerminApprovalController::class, 'index'])->name('dashboard_terminapprovals');
        Route::get('/get', [TerminApprovalController::class, 'get'])->name('dashboard_terminapprovals_detail');
        Route::get('/delete', [TerminApprovalController::class, 'destroy'])->name('dashboard_terminapprovals_delete');
        Route::post('/', [TerminApprovalController::class, 'store'])->name('dashboard_terminapprovals_post');
        Route::get('/datatable.json', [TerminApprovalController::class, '__datatable'])->name('dashboard_terminapprovals_table');
    });

    Route::group(['prefix' => 'repaymenthistories'], function () {
        Route::get('/', [RepaymentHistoryController::class, 'index'])->name('dashboard_repaymenthistories');
        Route::get('/get', [RepaymentHistoryController::class, 'get'])->name('dashboard_repaymenthistories_detail');
        Route::get('/delete', [RepaymentHistoryController::class, 'destroy'])->name('dashboard_repaymenthistories_delete');
        Route::post('/', [RepaymentHistoryController::class, 'store'])->name('dashboard_repaymenthistories_post');
        Route::get('/datatable.json', [RepaymentHistoryController::class, '__datatable'])->name('dashboard_repaymenthistories_table');
    });

    Route::group(['prefix' => 'reportstoremonitorings'], function () {
        Route::get('/', [ReportStoreMonitoringController::class, 'index'])->name('dashboard_reportstoremonitorings');
        Route::get('/get', [ReportStoreMonitoringController::class, 'get'])->name('dashboard_reportstoremonitorings_detail');
        Route::get('/delete', [ReportStoreMonitoringController::class, 'destroy'])->name('dashboard_reportstoremonitorings_delete');
        Route::post('/', [ReportStoreMonitoringController::class, 'store'])->name('dashboard_reportstoremonitorings_post');
        Route::get('/datatable.json', [ReportStoreMonitoringController::class, '__datatable'])->name('dashboard_reportstoremonitorings_table');


        Route::get('/download-excell', [ReportStoreMonitoringController::class, 'downloadExcell'])->name('dashboard_reportstoremonitorings_report_excell');
        Route::get('/download-pdf', [ReportStoreMonitoringController::class, 'downloadPDF'])->name('dashboard_reportstoremonitorings_report_pdf');
    });

    Route::group(['prefix' => 'notifications'], function () {
        Route::get('/', [NotificationController::class, 'index'])->name('dashboard_notifications');
        Route::get('/get', [NotificationController::class, 'get'])->name('dashboard_notifications_detail');
        Route::get('/delete', [NotificationController::class, 'destroy'])->name('dashboard_notifications_delete');
        Route::post('/', [NotificationController::class, 'store'])->name('dashboard_notifications_post');
        Route::get('/datatable.json', [NotificationController::class, '__datatable'])->name('dashboard_notifications_table');
    });

    Route::group(['prefix' => 'cancelorders'], function () {
        Route::get('/', [CancelOrderController::class, 'index'])->name('dashboard_cancelorders');
        Route::get('/get', [CancelOrderController::class, 'get'])->name('dashboard_cancelorders_detail');
        Route::get('/delete', [CancelOrderController::class, 'destroy'])->name('dashboard_cancelorders_delete');
        Route::post('/', [CancelOrderController::class, 'store'])->name('dashboard_cancelorders_post');
        Route::get('/datatable.json', [CancelOrderController::class, '__datatable'])->name('dashboard_cancelorders_table');
    });

    Route::group(['prefix' => 'resellersyncbulks'], function () {
        Route::get('/', [ResellerSyncBulkController::class, 'index'])->name('dashboard_resellersyncbulks');
        Route::get('/get', [ResellerSyncBulkController::class, 'get'])->name('dashboard_resellersyncbulks_detail');
        Route::get('/delete', [ResellerSyncBulkController::class, 'destroy'])->name('dashboard_resellersyncbulks_delete');
        Route::post('/', [ResellerSyncBulkController::class, 'store'])->name('dashboard_resellersyncbulks_post');
        Route::get('/datatable.json', [ResellerSyncBulkController::class, '__datatable'])->name('dashboard_resellersyncbulks_table');

        Route::post('/import', [ResellerSyncBulkController::class, 'import'])->name('dashboard_resellersyncbulks_import');
    });

    Route::group(['prefix' => 'resellersyncs'], function () {
        Route::get('/', [ResellerSyncController::class, 'index'])->name('dashboard_resellersyncs');
        Route::get('/get', [ResellerSyncController::class, 'get'])->name('dashboard_resellersyncs_detail');
        Route::get('/delete', [ResellerSyncController::class, 'destroy'])->name('dashboard_resellersyncs_delete');
        Route::post('/', [ResellerSyncController::class, 'store'])->name('dashboard_resellersyncs_post');
        Route::post('/sync', [ResellerSyncController::class, 'sync'])->name('dashboard_resellersyncs_syncpost');
        Route::get('/search-lecode', [ResellerSyncController::class, 'searchLeCode'])->name('dashboard_resellersyncs_search_le_code');
        Route::get('/datatable.json', [ResellerSyncController::class, '__datatable'])->name('dashboard_resellersyncs_table');
    });

    //    Route::group(['prefix'=>'storemonitoringassignvisits'], function () {
    //        Route::get('/', [StoreMonitoringAssignVisitController::class, 'index'])->name('dashboard_storemonitoringassignvisits');
    //        Route::get('/get', [StoreMonitoringAssignVisitController::class, 'get'])->name('dashboard_storemonitoringassignvisits_detail');
    //        Route::get('/delete', [StoreMonitoringAssignVisitController::class, 'destroy'])->name('dashboard_storemonitoringassignvisits_delete');
    //        Route::post('/', [StoreMonitoringAssignVisitController::class, 'store'])->name('dashboard_storemonitoringassignvisits_post');
    //        Route::get('/datatable.json', [StoreMonitoringAssignVisitController::class ,'__datatable'])->name('dashboard_storemonitoringassignvisits_table');
    //
    //
    //    });

    Route::group(['prefix' => 'storemonitoringassigns'], function () {
        Route::get('/', [StoreMonitoringAssignController::class, 'index'])->name('dashboard_storemonitoringassigns');
        Route::get('/get', [StoreMonitoringAssignController::class, 'get'])->name('dashboard_storemonitoringassigns_detail');
        Route::get('/delete', [StoreMonitoringAssignController::class, 'destroy'])->name('dashboard_storemonitoringassigns_delete');
        Route::post('/', [StoreMonitoringAssignController::class, 'store'])->name('dashboard_storemonitoringassigns_post');
        Route::get('/datatable.json', [StoreMonitoringAssignController::class, '__datatable'])->name('dashboard_storemonitoringassigns_table');

        Route::get('/history-assign', [StoreMonitoringAssignController::class, 'historyAssign'])->name('dashboard_storemonitoringassigns_history');
        Route::post('/assign', [StoreMonitoringAssignController::class, 'storeAssign'])->name('dashboard_storemonitoringassigns_post_assign_bulk');


        Route::get('/answer', [StoreMonitoringAssignController::class, 'getAnswer'])->name('dashboard_storemonitoringassigns_get_answer');
        Route::post('/answer', [StoreMonitoringAssignController::class, 'postAnswer'])->name('dashboard_storemonitoringassigns_post_answer');


        Route::get('/get-product-in', [StoreMonitoringAssignController::class, 'getProductIn'])->name('dashboard_storemonitoringassigns_get_product_in');
    });

    Route::group(['prefix' => 'monitoringquestiondetails'], function () {
        Route::get('/', [MonitoringQuestionDetailController::class, 'index'])->name('dashboard_monitoringquestiondetails');
        Route::get('/get-by-parent', [MonitoringQuestionDetailController::class, 'getByParent'])->name('dashboard_monitoringquestiondetails_detail_by_parent');
        Route::get('/get', [MonitoringQuestionDetailController::class, 'get'])->name('dashboard_monitoringquestiondetails_detail');
        Route::get('/delete', [MonitoringQuestionDetailController::class, 'destroy'])->name('dashboard_monitoringquestiondetails_delete');
        Route::post('/', [MonitoringQuestionDetailController::class, 'store'])->name('dashboard_monitoringquestiondetails_post');
        Route::get('/datatable.json', [MonitoringQuestionDetailController::class, '__datatable'])->name('dashboard_monitoringquestiondetails_table');

        Route::get('/get-value-by-filter', [MonitoringQuestionDetailController::class, 'getValueByFilter'])->name('dashboard_monitoringquestiondetails_get_value_by_filter');
    });

    Route::group(['prefix' => 'monitoringquestions'], function () {
        Route::get('/', [MonitoringQuestionController::class, 'index'])->name('dashboard_monitoringquestions');
        Route::get('/get', [MonitoringQuestionController::class, 'get'])->name('dashboard_monitoringquestions_detail');
        Route::get('/delete', [MonitoringQuestionController::class, 'destroy'])->name('dashboard_monitoringquestions_delete');
        Route::post('/', [MonitoringQuestionController::class, 'store'])->name('dashboard_monitoringquestions_post');
        Route::get('/datatable.json', [MonitoringQuestionController::class, '__datatable'])->name('dashboard_monitoringquestions_table');

        //        Route::get('/get-detail-question/{id}', [MonitoringQuestionController::class, 'getDetailQuestion'])->name('dashboard_monitoringquestions_child_question');

    });

    Route::group(['prefix' => 'storeproblems'], function () {
        Route::get('/', [StoreProblemController::class, 'index'])->name('dashboard_storeproblems');
        Route::get('/get', [StoreProblemController::class, 'get'])->name('dashboard_storeproblems_detail');
        Route::get('/delete', [StoreProblemController::class, 'destroy'])->name('dashboard_storeproblems_delete');
        Route::post('/', [StoreProblemController::class, 'store'])->name('dashboard_storeproblems_post');
        Route::get('/datatable.json', [StoreProblemController::class, '__datatable'])->name('dashboard_storeproblems_table');
    });

    Route::group(['prefix' => 'storepoints'], function () {
        Route::get('/', [StorePointController::class, 'index'])->name('dashboard_storepoints');
        Route::get('/get', [StorePointController::class, 'get'])->name('dashboard_storepoints_detail');
        Route::get('/delete', [StorePointController::class, 'destroy'])->name('dashboard_storepoints_delete');
        Route::post('/', [StorePointController::class, 'store'])->name('dashboard_storepoints_post');
        Route::get('/datatable.json', [StorePointController::class, '__datatable'])->name('dashboard_storepoints_table');
    });

    Route::group(['prefix' => 'storefreezerbrands'], function () {
        Route::get('/', [StoreFreezerBrandController::class, 'index'])->name('dashboard_storefreezerbrands');
        Route::get('/get', [StoreFreezerBrandController::class, 'get'])->name('dashboard_storefreezerbrands_detail');
        Route::get('/delete', [StoreFreezerBrandController::class, 'destroy'])->name('dashboard_storefreezerbrands_delete');
        Route::post('/', [StoreFreezerBrandController::class, 'store'])->name('dashboard_storefreezerbrands_post');
        Route::get('/datatable.json', [StoreFreezerBrandController::class, '__datatable'])->name('dashboard_storefreezerbrands_table');
    });

    Route::group(['prefix' => 'storeownerhips'], function () {
        Route::get('/', [StoreOwnerhipController::class, 'index'])->name('dashboard_storeownerhips');
        Route::get('/get', [StoreOwnerhipController::class, 'get'])->name('dashboard_storeownerhips_detail');
        Route::get('/delete', [StoreOwnerhipController::class, 'destroy'])->name('dashboard_storeownerhips_delete');
        Route::post('/', [StoreOwnerhipController::class, 'store'])->name('dashboard_storeownerhips_post');
        Route::get('/datatable.json', [StoreOwnerhipController::class, '__datatable'])->name('dashboard_storeownerhips_table');
    });

    Route::group(['prefix' => 'storetypes'], function () {
        Route::get('/', [StoreTypeController::class, 'index'])->name('dashboard_storetypes');
        Route::get('/get', [StoreTypeController::class, 'get'])->name('dashboard_storetypes_detail');
        Route::get('/delete', [StoreTypeController::class, 'destroy'])->name('dashboard_storetypes_delete');
        Route::post('/', [StoreTypeController::class, 'store'])->name('dashboard_storetypes_post');
        Route::get('/datatable.json', [StoreTypeController::class, '__datatable'])->name('dashboard_storetypes_table');
    });

    Route::group(['prefix' => 'storelocations'], function () {
        Route::get('/', [StoreLocationController::class, 'index'])->name('dashboard_storelocations');
        Route::get('/get', [StoreLocationController::class, 'get'])->name('dashboard_storelocations_detail');
        Route::get('/delete', [StoreLocationController::class, 'destroy'])->name('dashboard_storelocations_delete');
        Route::post('/', [StoreLocationController::class, 'store'])->name('dashboard_storelocations_post');
        Route::get('/datatable.json', [StoreLocationController::class, '__datatable'])->name('dashboard_storelocations_table');
    });

    Route::group(['prefix' => 'reportagentvas'], function () {
        Route::get('/', [ReportAgentVaController::class, 'index'])->name('dashboard_reportagentvas');
        Route::get('/get', [ReportAgentVaController::class, 'get'])->name('dashboard_reportagentvas_detail');
        Route::get('/delete', [ReportAgentVaController::class, 'destroy'])->name('dashboard_reportagentvas_delete');
        Route::post('/', [ReportAgentVaController::class, 'store'])->name('dashboard_reportagentvas_post');
        Route::get('/datatable.json', [ReportAgentVaController::class, '__datatable'])->name('dashboard_reportagentvas_table');

        Route::get('/download-excel', [ReportAgentVaController::class, 'downloadExcel'])->name('dashboard_reportagentvas_download_excel');
    });

    Route::group(['prefix' => 'cronhistoryvaagents'], function () {
        Route::get('/', [CronHistoryVaAgentController::class, 'index'])->name('dashboard_cronhistoryvaagents');
        Route::get('/init', [CronHistoryVaAgentController::class, 'init'])->name('dashboard_cronhistoryvaagents_init');
        //        Route::get('/get', [CronHistoryVaAgentController::class, 'get'])->name('dashboard_cronhistoryvaagents_detail');
        //        Route::get('/delete', [CronHistoryVaAgentController::class, 'destroy'])->name('dashboard_cronhistoryvaagents_delete');
        //        Route::post('/', [CronHistoryVaAgentController::class, 'store'])->name('dashboard_cronhistoryvaagents_post');
        //        Route::get('/datatable.json', [CronHistoryVaAgentController::class ,'__datatable'])->name('dashboard_cronhistoryvaagents_table');
    });

    Route::group(['prefix' => 'reportagentvisits'], function () {
        Route::get('/', [ReportAgentVisitController::class, 'index'])->name('dashboard_reportagentvisits');
        Route::get('/get', [ReportAgentVisitController::class, 'get'])->name('dashboard_reportagentvisits_detail');
        Route::get('/delete', [ReportAgentVisitController::class, 'destroy'])->name('dashboard_reportagentvisits_delete');
        Route::post('/', [ReportAgentVisitController::class, 'store'])->name('dashboard_reportagentvisits_post');
        Route::get('/datatable.json', [ReportAgentVisitController::class, '__datatable'])->name('dashboard_reportagentvisits_table');

        Route::get('/download-excel', [ReportAgentVisitController::class, 'downloadExcel'])->name('dashboard_reportagentvisits_download_excel');
    });

    //    Route::group(['prefix'=>'ordercodreleases'], function () {
    //        Route::get('/', [OrderCodReleaseController::class, 'index'])->name('dashboard_ordercodreleases');
    //        Route::get('/get', [OrderCodReleaseController::class, 'get'])->name('dashboard_ordercodreleases_detail');
    //        Route::get('/delete', [OrderCodReleaseController::class, 'destroy'])->name('dashboard_ordercodreleases_delete');
    //        Route::post('/', [OrderCodReleaseController::class, 'store'])->name('dashboard_ordercodreleases_post');
    //        Route::get('/datatable.json', [OrderCodReleaseController::class ,'__datatable'])->name('dashboard_ordercodreleases_table');
    //    });

    Route::group(['prefix' => 'releasecodtransfers'], function () {
        Route::get('/', [OrderCODTransferController::class, 'index'])->name('dashboard_ordercodtransfers');
        Route::get('/get', [OrderCODTransferController::class, 'get'])->name('dashboard_ordercodtransfers_detail');
        Route::get('/delete', [OrderCODTransferController::class, 'destroy'])->name('dashboard_ordercodtransfers_delete');
        Route::post('/', [OrderCODTransferController::class, 'store'])->name('dashboard_ordercodtransfers_post');
        Route::post('/release', [OrderCODTransferController::class, 'releaseStore'])->name('dashboard_ordercodtransfers_release_post');
        Route::post('/upload', [OrderCODTransferController::class, 'uploadStore'])->name('dashboard_ordercodtransfers_upload_post');
        Route::get('/release', [OrderCODTransferController::class, 'releaseGet'])->name('dashboard_ordercodtransfers_release_get');
        Route::get('/datatable.json', [OrderCODTransferController::class, '__datatable'])->name('dashboard_ordercodtransfers_table');

        Route::get('/delivery-detail', [OrderCODTransferController::class, 'getDeliveryDetail'])->name('dashboard_ordercodtransfers_delivery_detail');
        Route::get('/download-excel', [OrderCODTransferController::class, 'downloadExcel'])->name('dashboard_ordercod_download_excel');
    });

    Route::group(['prefix' => 'orderterminassigns'], function () {
        Route::get('/', [OrderTerminAssignController::class, 'index'])->name('dashboard_orderterminassigns');
        Route::get('/get', [OrderTerminAssignController::class, 'get'])->name('dashboard_orderterminassigns_detail');
        Route::get('/delete', [OrderTerminAssignController::class, 'destroy'])->name('dashboard_orderterminassigns_delete');
        Route::post('/', [OrderTerminAssignController::class, 'store'])->name('dashboard_orderterminassigns_post');
        Route::get('/datatable.json', [OrderTerminAssignController::class, '__datatable'])->name('dashboard_orderterminassigns_table');

        Route::get('/get-agent-assign-list', [OrderTerminAssignController::class, 'getAgentAssignList'])->name('dashboard_orderterminassigns_get_agent_assign_list');
        Route::post('/assign', [OrderTerminAssignController::class, 'storeAssign'])->name('dashboard_orderterminassigns_post_assign');
        Route::get('/history-assign', [OrderTerminAssignController::class, 'historyAssign'])->name('dashboard_orderterminassigns_detail_history_assign');

        //        $agent = $this->hubService->getAllAgentInAgentArea($order->hub_id);

    });

    Route::group(['prefix' => 'ordertermins'], function () {
        Route::get('/', [OrderTerminController::class, 'index'])->name('dashboard_ordertermins');
        Route::get('/get', [OrderTerminController::class, 'get'])->name('dashboard_ordertermins_detail');
        Route::get('/delete', [OrderTerminController::class, 'destroy'])->name('dashboard_ordertermins_delete');
        Route::post('/', [OrderTerminController::class, 'store'])->name('dashboard_ordertermins_post');
        Route::post('/release', [OrderTerminController::class, 'releaseStore'])->name('dashboard_ordertermins_release_post');
        Route::post('/upload', [OrderTerminController::class, 'uploadStore'])->name('dashboard_ordertermintransfers_upload_post');
        Route::post('/upload-faktur', [OrderTerminController::class, 'uploadStoreFaktur'])->name('dashboard_ordertermintransfers_upload_faktur_post');
        Route::get('/release', [OrderTerminController::class, 'releaseGet'])->name('dashboard_ordertermins_release_get');
        Route::get('/datatable.json', [OrderTerminController::class, '__datatable'])->name('dashboard_ordertermins_table');

        Route::get('/history-assign', [OrderTerminAssignController::class, 'historyAssign'])->name('dashboard_ordertermin_detail_history_assign');
        Route::get('/download-excel', [OrderTerminController::class, 'downloadExcel'])->name('dashboard_ordertermins_download_excel');
        Route::get('/get-termin-day', [OrderTerminController::class, 'getTerminDay'])->name('get_termin_day');
        Route::get('/get-invoice-by-reseller', [OrderTerminController::class, 'getInvoiceByReseller'])->name('get_invoice_by_reseller');
    });

    Route::group(['prefix' => 'prospectvisithistories'], function () {
        Route::get('/', [ProspectVisitHistoryController::class, 'index'])->name('dashboard_prospectvisithistories');
        Route::get('/get', [ProspectVisitHistoryController::class, 'get'])->name('dashboard_prospectvisithistories_detail');
        Route::get('/delete', [ProspectVisitHistoryController::class, 'destroy'])->name('dashboard_prospectvisithistories_delete');
        Route::post('/', [ProspectVisitHistoryController::class, 'store'])->name('dashboard_prospectvisithistories_post');
        Route::get('/datatable.json', [ProspectVisitHistoryController::class, '__datatable'])->name('dashboard_prospectvisithistories_table');
        Route::get('/datatable-visit.json', [ProspectVisitHistoryController::class, '__datatableHistoryVisit'])->name('dashboard_historyvisitprospect_table');
    });

    Route::group(['prefix' => 'resellervisithistories'], function () {
        Route::get('/', [ResellerVisitHistoryController::class, 'index'])->name('dashboard_resellervisithistories');
        Route::get('/get', [ResellerVisitHistoryController::class, 'get'])->name('dashboard_resellervisithistories_detail');
        Route::get('/delete', [ResellerVisitHistoryController::class, 'destroy'])->name('dashboard_resellervisithistories_delete');
        Route::post('/', [ResellerVisitHistoryController::class, 'store'])->name('dashboard_resellervisithistories_post');
        Route::get('/datatable.json', [ResellerVisitHistoryController::class, '__datatable'])->name('dashboard_resellervisithistories_table');
        Route::get('/datatable-visit.json', [ResellerVisitHistoryController::class, '__datatableHistoryVisit'])->name('dashboard_historyvisit_table');
    });


    Route::get('/ocr-test', [OcrController::class, 'home'])->name('ocr');
    Route::post('/ocr-test', [OcrController::class, 'readImage'])->name('ocr_post');


    Route::group(['prefix' => 'areaagents'], function () {
        Route::get('/', [AreaAgentController::class, 'index'])->name('dashboard_areaagents');
        Route::get('/get', [AreaAgentController::class, 'get'])->name('dashboard_areaagents_detail');
        Route::get('/delete', [AreaAgentController::class, 'destroy'])->name('dashboard_areaagents_delete');
        Route::post('/', [AreaAgentController::class, 'store'])->name('dashboard_areaagents_post');
        Route::get('/datatable.json', [AreaAgentController::class, '__datatable'])->name('dashboard_areaagents_table');

        Route::get('/init-area-agent', [AreaAgentController::class, 'initArea'])->name('dashboard_areaagents_init_area');
        Route::get('/areaAgent.json', [AreaAgentController::class, '__AreaAgent'])->name('dashboard_areaagents_area_agent_table');
        Route::get('/change-agent-area', [AreaAgentController::class, 'changeAgentArea'])->name('dashboard_areaagents_area_agent_change_area');
    });

    Route::group(['prefix' => 'pegawais'], function () {
        Route::get('/', [PegawaiController::class, 'index'])->name('dashboard_pegawais');
        Route::get('/get', [PegawaiController::class, 'get'])->name('dashboard_pegawais_detail');
        Route::get('/delete', [PegawaiController::class, 'destroy'])->name('dashboard_pegawais_delete');
        Route::post('/', [PegawaiController::class, 'store'])->name('dashboard_pegawais_post');
        Route::get('/datatable.json', [PegawaiController::class, '__datatable'])->name('dashboard_pegawais_table');
    });

    Route::group(['prefix' => 'dashboardorderings'], function () {
        Route::get('/', [DashboardOrderingController::class, 'index'])->name('dashboard_dashboardorderings');
        Route::get('/get', [DashboardOrderingController::class, 'get'])->name('dashboard_dashboardorderings_detail');
        Route::get('/delete', [DashboardOrderingController::class, 'destroy'])->name('dashboard_dashboardorderings_delete');
        Route::post('/', [DashboardOrderingController::class, 'store'])->name('dashboard_dashboardorderings_post');
        Route::get('/datatable.json', [DashboardOrderingController::class, '__datatable'])->name('dashboard_dashboardorderings_table');


        Route::get('/getCardData', [DashboardOrderingController::class, 'getCardData'])->name('dashboard_dashboardordering_get_card_data');
        Route::get('/getChartData', [DashboardOrderingController::class, 'getChartData'])->name('dashboard_dashboardordering_get_chart_data');
    });

    Route::group(['prefix' => 'dashboardanalytics'], function () {
        Route::get('/', [DashboardAnalyticController::class, 'index'])->name('dashboard_dashboardanalytics');
        Route::get('/get', [DashboardAnalyticController::class, 'get'])->name('dashboard_dashboardanalytics_detail');
        Route::get('/delete', [DashboardAnalyticController::class, 'destroy'])->name('dashboard_dashboardanalytics_delete');
        Route::post('/', [DashboardAnalyticController::class, 'store'])->name('dashboard_dashboardanalytics_post');
        Route::get('/datatable.json', [DashboardAnalyticController::class, '__datatable'])->name('dashboard_dashboardanalytics_table');


        Route::get('/getCardData', [DashboardAnalyticController::class, 'getCardData'])->name('dashboard_dashboardanalytics_get_card_data');
        Route::get('/getChartData', [DashboardAnalyticController::class, 'getChartData'])->name('dashboard_dashboardanalytics_get_chart_data');
        Route::get('/getMapData', [DashboardAnalyticController::class, 'getMapData'])->name('dashboard_dashboardanalytics_get_map_data');
    });

    Route::group(['prefix' => 'dashboardagents'], function () {
        Route::get('/', [DashboardAgentController::class, 'index'])->name('dashboard_dashboardagents');
        Route::get('/get', [DashboardAgentController::class, 'get'])->name('dashboard_dashboardagents_detail');
        Route::get('/delete', [DashboardAgentController::class, 'destroy'])->name('dashboard_dashboardagents_delete');
        Route::post('/', [DashboardAgentController::class, 'store'])->name('dashboard_dashboardagents_post');
        Route::get('/datatable.json', [DashboardAgentController::class, '__datatable'])->name('dashboard_dashboardagents_table');
    });

    Route::group(['prefix' => 'agentdeposits'], function () {
        Route::get('/', [AgentDepositController::class, 'index'])->name('dashboard_agentdeposits');
        Route::get('/get', [AgentDepositController::class, 'get'])->name('dashboard_agentdeposits_detail');
        Route::get('/delete', [AgentDepositController::class, 'destroy'])->name('dashboard_agentdeposits_delete');
        Route::post('/', [AgentDepositController::class, 'store'])->name('dashboard_agentdeposits_post');
        Route::get('/datatable.json', [AgentDepositController::class, '__datatable'])->name('dashboard_agentdeposits_table');
    });

    Route::group(['prefix' => 'topanbanks'], function () {
        Route::get('/', [TopanBankController::class, 'index'])->name('dashboard_topanbanks');
        Route::get('/get', [TopanBankController::class, 'get'])->name('dashboard_topanbanks_detail');
        Route::get('/delete', [TopanBankController::class, 'destroy'])->name('dashboard_topanbanks_delete');
        Route::post('/', [TopanBankController::class, 'store'])->name('dashboard_topanbanks_post');
        Route::get('/datatable.json', [TopanBankController::class, '__datatable'])->name('dashboard_topanbanks_table');
    });

    Route::group(['prefix' => 'mstbanks'], function () {
        Route::get('/', [MstBankController::class, 'index'])->name('dashboard_mstbanks');
        Route::get('/get', [MstBankController::class, 'get'])->name('dashboard_mstbanks_detail');
        Route::get('/delete', [MstBankController::class, 'destroy'])->name('dashboard_mstbanks_delete');
        Route::post('/', [MstBankController::class, 'store'])->name('dashboard_mstbanks_post');
        Route::get('/datatable.json', [MstBankController::class, '__datatable'])->name('dashboard_mstbanks_table');
    });

    Route::group(['prefix' => 'integrationusages'], function () {
        Route::get('/', [IntegrationUsageController::class, 'index'])->name('dashboard_integrationusages');
        Route::get('/get', [IntegrationUsageController::class, 'get'])->name('dashboard_integrationusages_detail');
        Route::get('/delete', [IntegrationUsageController::class, 'destroy'])->name('dashboard_integrationusages_delete');
        Route::post('/', [IntegrationUsageController::class, 'store'])->name('dashboard_integrationusages_post');

        Route::get('/download-report-usage', [IntegrationUsageController::class, 'downloadReportUsage'])->name('dashboard_integrationusages_download_report_usage');
        Route::get('/datatable.json', [IntegrationUsageController::class, '__datatable'])->name('dashboard_integrationusages_table');

        //        Route::get('/filter-report', [IntegrationUsageController::class, 'filterReport'])->name('dashboard_integrationusages_filter_report');

    });

    Route::group(['prefix' => 'reportorder'], function () {
        Route::get('/', [ReportOrderController::class, 'index'])->name('dashboard_reportorder');
        Route::get('/get', [ReportOrderController::class, 'get'])->name('dashboard_reportorder_detail');
        Route::get('/delete', [ReportOrderController::class, 'destroy'])->name('dashboard_reportorder_delete');
        Route::post('/', [ReportOrderController::class, 'store'])->name('dashboard_reportorder_post');

        Route::get('/download-report-order', [ReportOrderController::class, 'downloadReportOrder'])->name('dashboard_reportorder_download_report_order');
        Route::get('/datatable.json', [ReportOrderController::class, '__datatable'])->name('dashboard_reportorder_table');
    });

    Route::group(['prefix' => 'reportordermanual'], function () {
        Route::get('/', [ReportOrderManualController::class, 'index'])->name('dashboard_reportordermanual');
        Route::get('/get', [ReportOrderManualController::class, 'get'])->name('dashboard_reportordermanual_detail');
        Route::get('/delete', [ReportOrderManualController::class, 'destroy'])->name('dashboard_reportordermanual_delete');
        Route::post('/', [ReportOrderManualController::class, 'store'])->name('dashboard_reportordermanual_post');

        Route::get('/download-report-order', [ReportOrderManualController::class, 'downloadReportOrder'])->name('dashboard_reportordermanual_download_report_order');
        Route::get('/datatable.json', [ReportOrderManualController::class, '__datatable'])->name('dashboard_reportordermanual_table');
    });

    Route::group(['prefix' => 'reporthubtransaksi'], function () {
        Route::get('/', [ReportHubTransaksiController::class, 'index'])->name('dashboard_reporthubtransaksi');
        Route::get('/get', [ReportHubTransaksiController::class, 'get'])->name('dashboard_reporthubtransaksi_detail');
        Route::get('/delete', [ReportHubTransaksiController::class, 'destroy'])->name('dashboard_reporthubtransaksi_delete');
        Route::post('/', [ReportHubTransaksiController::class, 'store'])->name('dashboard_reporthubtransaksi_post');

        Route::get('/download-report-reporthubtransaksi', [ReportHubTransaksiController::class, 'downloadReportHubTransaksi'])->name('dashboard_reporthubtransaksi_download_report_hubtransaksi');
        Route::get('/datatable.json', [ReportHubTransaksiController::class, '__datatable'])->name('dashboard_reporthubtransaksi_table');
    });

    Route::group(['prefix' => 'reportdelivery'], function () {
        Route::get('/', [ReportDeliveryController::class, 'index'])->name('dashboard_reportdelivery');
        Route::get('/get', [ReportDeliveryController::class, 'get'])->name('dashboard_reportdelivery_detail');
        Route::get('/delete', [ReportDeliveryController::class, 'destroy'])->name('dashboard_reportdelivery_delete');
        Route::post('/', [ReportDeliveryController::class, 'store'])->name('dashboard_reportdelivery_post');

        Route::get('/download-report-reportdelivery', [ReportDeliveryController::class, 'downloadReportdelivery'])->name('dashboard_reportdelivery_download_report_delivery');
        Route::get('/datatable.json', [ReportDeliveryController::class, '__datatable'])->name('dashboard_reportdelivery_table');
    });

    Route::group(['prefix' => 'reportpaymentdetail'], function () {
        Route::get('/', [ReportPaymentDetailController::class, 'index'])->name('dashboard_reportpaymentdetail');
        Route::get('/get', [ReportPaymentDetailController::class, 'get'])->name('dashboard_reportpaymentdetail_detail');
        Route::get('/delete', [ReportPaymentDetailController::class, 'destroy'])->name('dashboard_reportpaymentdetail_delete');
        Route::post('/', [ReportPaymentDetailController::class, 'store'])->name('dashboard_reportpaymentdetail_post');

        Route::get('/download-report-reportpaymentdetail', [ReportPaymentDetailController::class, 'downloadreportpaymentdetail'])->name('dashboard_reportpaymentdetail_download_report_paymentdetail');
        Route::get('/datatable.json', [ReportPaymentDetailController::class, '__datatable'])->name('dashboard_reportpaymentdetail_table');
    });

    Route::group(['prefix' => 'integrations'], function () {
        Route::get('/', [IntegrationController::class, 'index'])->name('dashboard_integrations');
        Route::get('/get', [IntegrationController::class, 'get'])->name('dashboard_integrations_detail');
        Route::get('/delete', [IntegrationController::class, 'destroy'])->name('dashboard_integrations_delete');
        Route::post('/', [IntegrationController::class, 'store'])->name('dashboard_integrations_post');
        Route::get('/datatable.json', [IntegrationController::class, '__datatable'])->name('dashboard_integrations_table');


        Route::post('/import', [IntegrationController::class, 'submitImport'])->name('dashboard_integrations_submit_import');
    });

    Route::group(['prefix' => 'agentpickups'], function () {
        Route::get('/', [AgentPickupController::class, 'index'])->name('dashboard_agentpickups');
        Route::get('/get', [AgentPickupController::class, 'get'])->name('dashboard_agentpickups_detail');
        Route::get('/delete', [AgentPickupController::class, 'destroy'])->name('dashboard_agentpickups_delete');
        Route::post('/', [AgentPickupController::class, 'store'])->name('dashboard_agentpickups_post');
        Route::get('/datatable.json', [AgentPickupController::class, '__datatable'])->name('dashboard_agentpickups_table');

        Route::get('/detail', [AgentPickupController::class, 'process'])->name('dashboard_agentpickups_process');
        Route::post('/confirm-delivery', [AgentPickupController::class, 'confirmDelivery'])->name('dashboard_agentdelivery_confirm');
    });

    Route::group(['prefix' => 'areaproducts'], function () {
        Route::get('/', [AreaProductController::class, 'index'])->name('dashboard_areaproducts');
        Route::get('/get', [AreaProductController::class, 'get'])->name('dashboard_areaproducts_detail');
        Route::get('/delete', [AreaProductController::class, 'destroy'])->name('dashboard_areaproducts_delete');
        //        Route::post('/', [AreaProductController::class, 'store'])->name('dashboard_areaproducts_post');
        Route::post('/price', [AreaProductController::class, 'storePrice'])->name('dashboard_areaproducts_post_price');
        Route::get('/datatable.json', [AreaProductController::class, '__datatable'])->name('dashboard_areaproducts_table');
        Route::get('/datatable-product.json', [AreaProductController::class, '__datatableProduct'])->name('dashboard_areaproducts_table_product');
        Route::get('/datatable-brand.json', [AreaProductController::class, '__datatableBrand'])->name('dashboard_areaproducts_table_brand');
        Route::get('/datatable-variant.json', [AreaProductController::class, '__datatableVariant'])->name('dashboard_areaproducts_table_variant');

        Route::get('/get-price', [AreaProductController::class, 'getPrice'])->name('dashboard_areaproducts_get_price');


        Route::get('/datatable-product_area.json', [AreaProductController::class, '__datatable_product_area'])->name('dashboard_areaproducts_product_in_area_table');
        Route::get('/changeProductArea', [AreaProductController::class, 'storeProductArea'])->name('dashboard_areaproducts_change_product_area');
        Route::get('/changeProductBrandArea', [AreaProductController::class, 'storeProductBrandArea'])->name('dashboard_areaproducts_change_product_brand_area');
        Route::get('/changeProductVariantArea', [AreaProductController::class, 'storeProductVariantArea'])->name('dashboard_areaproducts_change_product_variant_area');

        Route::get('/init-area-with-all-product', [AreaProductController::class, 'initAreaWithAllProduct'])->name('dashboard_areaproducts_init_area');
        Route::get('/init-product-in-all-area', [AreaProductController::class, 'initProductInAllArea'])->name('dashboard_areaproducts_init_product_in_all_area');

        Route::post('/changeProductAreaBulk', [AreaProductController::class, 'storeBulkProductArea'])->name('dashboard_areaproducts_change_bulk_product_area');
        Route::post('/changeVariantAreaBulk', [AreaProductController::class, 'storeBulkProductVariantArea'])->name('dashboard_areaproducts_change_bulk_product_variant_area');
        Route::post('/changeBrandAreaBulk', [AreaProductController::class, 'storeBulkProductBrandArea'])->name('dashboard_areaproducts_change_bulk_product_brand_area');
        Route::get('/get-variant', [AreaProductController::class, 'getVariant'])->name('dashboard_areaproducts_get_variant');


        Route::get('/get-exist-brand', [AreaProductController::class, 'getExistInAreaBrand'])->name('dashboard_areaproducts_get_exist_brand_in_area');
        Route::get('/download', [AreaProductController::class, 'export'])->name('dashboard_areaproducts_export');
    });

    Route::group(['prefix' => 'hubbanks'], function () {
        Route::get('/', [HubBankController::class, 'index'])->name('dashboard_hubbanks');
        Route::get('/get', [HubBankController::class, 'get'])->name('dashboard_hubbanks_detail');
        Route::get('/delete', [HubBankController::class, 'destroy'])->name('dashboard_hubbanks_delete');
        Route::post('/', [HubBankController::class, 'store'])->name('dashboard_hubbanks_post');
        Route::get('/datatable.json', [HubBankController::class, '__datatable'])->name('dashboard_hubbanks_table');
    });

    Route::group(['prefix' => 'hubtransactionhistories'], function () {
        Route::get('/', [HubTransactionHistoryController::class, 'index'])->name('dashboard_hubtransactionhistories');
        Route::get('/get', [HubTransactionHistoryController::class, 'get'])->name('dashboard_hubtransactionhistories_detail');
        Route::get('/delete', [HubTransactionHistoryController::class, 'destroy'])->name('dashboard_hubtransactionhistories_delete');
        Route::post('/', [HubTransactionHistoryController::class, 'store'])->name('dashboard_hubtransactionhistories_post');
        Route::get('/datatable.json', [HubTransactionHistoryController::class, '__datatable'])->name('dashboard_hubtransactionhistories_table');
        Route::get('/vacc-datatable.json', [HubTransactionHistoryController::class, '__datatable_hubtransaction'])->name('dashboard_user_vacc_table');
    });

    Route::group(['prefix' => 'historypayment'], function () {
        Route::get('/', [HistoryPaymentController::class, 'index'])->name('dashboard_historypayment');
        Route::get('/get', [HistoryPaymentController::class, 'get'])->name('dashboard_historypayment_detail');
        Route::get('/datatable.json', [HistoryPaymentController::class, '__datatable'])->name('dashboard_historypayment_table');
        Route::post('/cash-out', [HistoryPaymentController::class, 'cashout'])->name('dashboard_history_cashout');
    });

    Route::group(['prefix' => 'historypayment2'], function () {
        Route::get('/', [HistoryPayment2Controller::class, 'index'])->name('dashboard_historypayment2');
        Route::get('/get', [HistoryPayment2Controller::class, 'get'])->name('dashboard_historypayment2_detail');
        Route::get('/datatable.json', [HistoryPayment2Controller::class, '__datatable'])->name('dashboard_historypayment2_table');
        Route::post('/cash-out', [HistoryPayment2Controller::class, 'cashout'])->name('dashboard_history_cashout2');
    });

    Route::group(['prefix' => 'historypaymentradensaleh'], function () {
        Route::get('/', [HistoryPaymentController::class, 'indexradensaleh'])->name('dashboard_historypaymentradensaleh');
        Route::get('/get', [HistoryPaymentController::class, 'get'])->name('dashboard_historypaymentradensaleh_detail');
        Route::get('/datatable.json', [HistoryPaymentController::class, '__datatableradensaleh'])->name('dashboard_historypaymentradensaleh_table');
    });

    Route::group(['prefix' => 'historypaymentpuriimperium'], function () {
        Route::get('/', [HistoryPaymentController::class, 'indexpuriimperium'])->name('dashboard_historypaymentpuriimperium');
        Route::get('/get', [HistoryPaymentController::class, 'get'])->name('dashboard_historypaymentpuriimperium_detail');
        Route::get('/datatable.json', [HistoryPaymentController::class, '__datatablepuriimperium'])->name('dashboard_historypaymentpuriimperium_table');
    });

    Route::group(['prefix' => 'historypaymenttangerang'], function () {
        Route::get('/', [HistoryPaymentController::class, 'indextangerang'])->name('dashboard_historypaymenttangerang');
        Route::get('/get', [HistoryPaymentController::class, 'get'])->name('dashboard_historypaymenttangerang_detail');
        Route::get('/datatable.json', [HistoryPaymentController::class, '__datatabletangerang'])->name('dashboard_historypaymenttangerang_table');
    });

    Route::group(['prefix' => 'historypaymentbekasi'], function () {
        Route::get('/', [HistoryPaymentController::class, 'indexbekasi'])->name('dashboard_historypaymentbekasi');
        Route::get('/get', [HistoryPaymentController::class, 'get'])->name('dashboard_historypaymentbekasi_detail');
        Route::get('/datatable.json', [HistoryPaymentController::class, '__datatablebekasi'])->name('dashboard_historypaymentbekasi_table');
    });

    Route::group(['prefix' => 'historypaymentsurabaya'], function () {
        Route::get('/', [HistoryPaymentController::class, 'indexsurabaya'])->name('dashboard_historypaymentsurabaya');
        Route::get('/get', [HistoryPaymentController::class, 'get'])->name('dashboard_historypaymentsurabaya_detail');
        Route::get('/datatable.json', [HistoryPaymentController::class, '__datatablesurabaya'])->name('dashboard_historypaymentsurabaya_table');
    });

    Route::group(['prefix' => 'historypaymentcibinong'], function () {
        Route::get('/', [HistoryPaymentController::class, 'indexcibinong'])->name('dashboard_historypaymentcibinong');
        Route::get('/get', [HistoryPaymentController::class, 'get'])->name('dashboard_historypaymentcibinong_detail');
        Route::get('/datatable.json', [HistoryPaymentController::class, '__datatablecibinong'])->name('dashboard_historypaymentcibinong_table');
    });

    Route::group(['prefix' => 'historypaymenthub'], function () {
        Route::get('/', [HistoryPaymentHubController::class, 'index'])->name('dashboard_historypaymenthub');
        Route::get('/get', [HistoryPaymentHubController::class, 'get'])->name('dashboard_historypaymenthub_detail');
        Route::get('/datatable.json', [HistoryPaymentHubController::class, '__datatable'])->name('dashboard_historypaymenthub_table');
    });

    Route::group(['prefix' => 'hubpayment'], function () {
        Route::get('/', [HubPaymentController::class, 'index'])->name('dashboard_hubpayment');
        Route::get('/get', [HubPaymentController::class, 'get'])->name('dashboard_hubpayment_detail');
        Route::get('/datatable.json', [HubPaymentController::class, '__datatable'])->name('dashboard_hubpayment_table');
        Route::post('/cash-out', [HubPaymentController::class, 'cashout'])->name('dashboard_cashout');
    });

    Route::group(['prefix' => 'vaagentsaldo'], function () {
        Route::get('/', [VaAgentController::class, 'index'])->name('dashboard_vaagent');
        Route::get('/get', [VaAgentController::class, 'get'])->name('dashboard_vaagent_detail');
        Route::post('/', [VaAgentController::class, 'store'])->name('dashboard_vaagent_post');
        Route::get('/datatable.json', [VaAgentController::class, '__datatable'])->name('dashboard_vaagent_table');
        Route::post('/va-status', [VaAgentController::class, 'updateStatusVA'])->name('dashboard_vaagent_status');
    });

    Route::group(['prefix' => 'datareseller'], function () {
        Route::get('/', [DataResellerController::class, 'index'])->name('dashboard_datareseller');
        Route::get('/get', [DataResellerController::class, '__datatableReseller'])->name('dashboard_datareseller_detail');
        Route::post('/', [DataResellerController::class, 'store'])->name('dashboard_datareseller_post');
        Route::get('/datatable.json', [DataResellerController::class, '__datatable'])->name('dashboard_dataagent_table');
        Route::post('/datatable-agent.json', [DataResellerController::class, 'getAgent'])->name('agent_data');
        Route::get('/datatable-reseller.json', [DataResellerController::class, '__datatableReseller'])->name('dashboard_datareseller_table');

        // Route::post('/va-status', [DataResellerController::class, 'updateStatusVA'])->name('dashboard_datareseller_status');
    });

    Route::group(['prefix' => 'dataprospect'], function () {
        Route::get('/', [DataProspectController::class, 'index'])->name('dashboard_dataprospect');
        Route::get('/get', [DataProspectController::class, '__datatableReseller'])->name('dashboard_dataprospect_detail');
        Route::get('/import', [DataProspectController::class, 'import'])->name('dashboard_prospect_import');
        Route::post('/', [DataProspectController::class, 'store'])->name('dashboard_dataprospect_post');
        Route::post('/import', [DataProspectController::class, 'submitImport'])->name('dashboard_dataprospect_import_submit');
        Route::get('/reset', [DataProspectController::class, 'resetCookies'])->name('dashboard_dataprospect_import_reset_cookies');
        Route::get('/datatable.json', [DataProspectController::class, '__datatable'])->name('dashboard_dataagent_table');
        Route::post('/datatable-agent.json', [DataProspectController::class, 'getAgent'])->name('agent_data');
        Route::get('/datatable-reseller.json', [DataProspectController::class, '__datatableReseller'])->name('dashboard_dataprospect_table');
    });

    // Route::group(['prefix'=>'hubpayment'], function () {
    //     Route::get('/', [HubPaymentController::class, 'index'])->name('dashboard_hubpayment');
    //     Route::get('/get', [HubPaymentController::class, 'get'])->name('dashboard_hubtransactionhistories_detail');
    //     Route::get('/delete', [HubPaymentController::class, 'destroy'])->name('dashboard_hubtransactionhistories_delete');
    //     Route::post('/', [HubPaymentController::class, 'store'])->name('dashboard_hubtransactionhistories_post');
    //     Route::get('/datatable.json', [HubPaymentController::class ,'__datatable'])->name('dashboard_hubtransactionhistories_table');
    //     Route::get('/vacc-datatable.json', [HubPaymentController::class, '__datatable_hubtransaction'])->name('dashboard_user_vacc_table');
    // });

    Route::group(['prefix' => 'canvasings'], function () {
        Route::get('/', [CanvasingController::class, 'index'])->name('dashboard_canvasing');
        Route::get('/import', [CanvasingController::class, 'import'])->name('dashboard_canvasing_import');
        Route::get('/get', [CanvasingController::class, 'get'])->name('dashboard_canvasings_detail');
        Route::get('/delete', [CanvasingController::class, 'destroy'])->name('dashboard_canvasings_delete');
        Route::post('/', [CanvasingController::class, 'store'])->name('dashboard_canvasings_post');
        Route::get('/datatable.json', [CanvasingController::class, '__datatable'])->name('dashboard_canvasings_table');

        Route::get('/import', [CanvasingController::class, 'import'])->name('dashboard_canvasing_import');
        Route::post('/import', [CanvasingController::class, 'submitImport'])->name('dashboard_canvasing_import_submit');
        Route::get('/reset', [CanvasingController::class, 'resetCookies'])->name('dashboard_canvasing_import_reset_cookies');
    });

    Route::group(['prefix' => 'facilities'], function () {
        Route::get('/', [FacilityController::class, 'index'])->name('dashboard_facilities');
        Route::get('/get', [FacilityController::class, 'get'])->name('dashboard_facilities_detail');
        Route::get('/delete', [FacilityController::class, 'destroy'])->name('dashboard_facilities_delete');
        Route::post('/', [FacilityController::class, 'store'])->name('dashboard_facilities_post');
        Route::get('/datatable.json', [FacilityController::class, '__datatable'])->name('dashboard_facilities_table');
    });

    Route::group(['prefix' => 'businestypes'], function () {
        Route::get('/', [BusinesTypeController::class, 'index'])->name('dashboard_businestypes');
        Route::get('/get', [BusinesTypeController::class, 'get'])->name('dashboard_businestypes_detail');
        Route::get('/delete', [BusinesTypeController::class, 'destroy'])->name('dashboard_businestypes_delete');
        Route::post('/', [BusinesTypeController::class, 'store'])->name('dashboard_businestypes_post');
        Route::get('/datatable.json', [BusinesTypeController::class, '__datatable'])->name('dashboard_businestypes_table');
    });

    Route::group(['prefix' => 'hubtransactions'], function () {
        Route::get('/', [HubTransactionController::class, 'index'])->name('dashboard_hubtransactions');
        Route::get('/get', [HubTransactionController::class, 'get'])->name('dashboard_hubtransactions_detail');
        Route::get('/delete', [HubTransactionController::class, 'destroy'])->name('dashboard_hubtransactions_delete');
        Route::post('/', [HubTransactionController::class, 'store'])->name('dashboard_hubtransactions_post');
        Route::get('/datatable.json', [HubTransactionController::class, '__datatable'])->name('dashboard_hubtransactions_table');

        Route::get('/detail', [HubTransactionController::class, 'process'])->name('dashboard_hubtransaction_process');
        Route::post('/processTransaction', [HubTransactionController::class, 'submitPackagingTransaction'])->name('dashboard_submit_process_packaging_hubtransaction');
        Route::post('/processTransactionCross', [HubTransactionController::class, 'submitPackagingTransactionCross'])->name('dashboard_submit_process_packaging_hubtransaction_cross');
        Route::post('/confirmStockTransaction', [HubTransactionController::class, 'submitConfirmStockTransaction'])->name('dashboard_submit_confirm_stock_hubtransaction');
    });

    Route::group(['prefix' => 'agents'], function () {
        Route::get('/', [AgentController::class, 'index'])->name('dashboard_agents');
        Route::get('/get', [AgentController::class, 'get'])->name('dashboard_agents_detail');
        Route::get('/delete', [AgentController::class, 'destroy'])->name('dashboard_agents_delete');
        Route::post('/', [AgentController::class, 'store'])->name('dashboard_agents_post');
        Route::get('/datatable.json', [AgentController::class, '__datatable'])->name('dashboard_agents_table');
        Route::get('/get-pin', [AgentController::class, 'getResetPin'])->name('dashboard_agent_reset_pin_get');
        Route::post('/update-pin', [AgentController::class, 'resetPin'])->name('dashboard_agent_reset_pin');
        Route::post('/changeStatusBulk', [AgentController::class, 'storeBulkStatus'])->name('dashboard_agent_change_status');

        Route::get('/get-agent-by-area', [AgentController::class, 'getAgentByArea'])->name('dashboard_agents_get_agent_by_area');
    });

    Route::group(['prefix' => 'agenttypes'], function () {
        Route::get('/', [AgentTypeController::class, 'index'])->name('dashboard_agenttypes');
        Route::get('/get', [AgentTypeController::class, 'get'])->name('dashboard_agenttypes_detail');
        Route::get('/delete', [AgentTypeController::class, 'destroy'])->name('dashboard_agenttypes_delete');
        Route::post('/', [AgentTypeController::class, 'store'])->name('dashboard_agenttypes_post');
        Route::get('/datatable.json', [AgentTypeController::class, '__datatable'])->name('dashboard_agenttypes_table');
    });

    Route::group(['prefix' => 'contentdetails'], function () {
        Route::get('/', [contentDetailController::class, 'index'])->name('dashboard_contentdetails');
        Route::get('/get', [contentDetailController::class, 'get'])->name('dashboard_contentdetails_detail');
        Route::get('/delete', [contentDetailController::class, 'destroy'])->name('dashboard_contentdetails_delete');
        Route::post('/', [contentDetailController::class, 'store'])->name('dashboard_contentdetails_post');
        Route::get('/datatable.json', [contentDetailController::class, '__datatable'])->name('dashboard_contentdetails_table');
    });

    Route::group(['prefix' => 'contentheaders'], function () {
        Route::get('/', [ContentHeaderController::class, 'index'])->name('dashboard_contentheaders');
        Route::get('/get', [ContentHeaderController::class, 'get'])->name('dashboard_contentheaders_detail');
        Route::get('/delete', [ContentHeaderController::class, 'destroy'])->name('dashboard_contentheaders_delete');
        Route::post('/', [ContentHeaderController::class, 'store'])->name('dashboard_contentheaders_post');
        Route::get('/datatable.json', [ContentHeaderController::class, '__datatable'])->name('dashboard_contentheaders_table');
    });

    Route::group(['prefix' => 'paymentmethods'], function () {
        Route::get('/', [PaymentMethodController::class, 'index'])->name('dashboard_paymentmethods');
        Route::get('/get', [PaymentMethodController::class, 'get'])->name('dashboard_paymentmethods_detail');
        Route::get('/delete', [PaymentMethodController::class, 'destroy'])->name('dashboard_paymentmethods_delete');
        Route::post('/', [PaymentMethodController::class, 'store'])->name('dashboard_paymentmethods_post');
        Route::get('/datatable.json', [PaymentMethodController::class, '__datatable'])->name('dashboard_paymentmethods_table');

        Route::get('/init-all-reseller', [PaymentMethodController::class, 'initAllReseller'])->name('dashboard_paymentmethods_init_all_reseller');
    });

    Route::group(['prefix' => 'orders'], function () {
        Route::get('/', [OrderController::class, 'index'])->name('dashboard_orders');
        Route::get('/get', [OrderController::class, 'get'])->name('dashboard_orders_detail');
        Route::get('/delete', [OrderController::class, 'destroy'])->name('dashboard_orders_delete');
        Route::post('/', [OrderController::class, 'store'])->name('dashboard_orders_post');
        Route::get('/datatable.json', [OrderController::class, '__datatable'])->name('dashboard_orders_table');

        Route::get('/detail', [OrderController::class, 'process'])->name('dashboard_orders_process');
        // Route::get('/edit', [OrderController::class, 'edit'])->name('dashboard_orders_edit');
        // Route::get('/datatable-product.json', [OrderController::class, '__datatableProduct'])->name('dashboard_edit_table_product');
        Route::post('/processOrder', [OrderController::class, 'submitProcessOrder'])->name('dashboard_submit_process_order');
        Route::post('/cancelOrder', [OrderController::class, 'submitCancelOrder'])->name('dashboard_submit_cancel_order');
        Route::get('/returOrder', [OrderController::class, 'getDetailRetur'])->name('dashboard_retur_order');
        Route::get('/potonganOrder', [OrderController::class, 'getDetailPotongan'])->name('dashboard_potongan_order');
        Route::get('/fakturOrder', [OrderController::class, 'getDetailFaktur'])->name('dashboard_faktur_order');
        Route::post('/returSubmit', [OrderController::class, 'submitReturOrder'])->name('dashboard_submit_retur_order');
        Route::post('/potonganSubmit', [OrderController::class, 'submitPotonganOrder'])->name('dashboard_submit_potongan_order');
        Route::post('/fakturSubmit', [OrderController::class, 'submitFakturOrder'])->name('dashboard_submit_faktur_order');
        Route::post('/photos', [OrderController::class, 'uploadFaktur'])->name('dashboard_upload_faktur');
        Route::get('/orders/{orderId}/photos', [OrderController::class, 'getPhotos'])->name('dashboard_get_faktur');
        // Route::delete('/photos/{photo}', [OrderController::class, 'destroy_faktur']);
        Route::get('/delete-photo', [OrderController::class, 'destroyFaktur'])->name('dashboard_faktur_delete');


        Route::post('/setAgent', [OrderController::class, 'setAgentDeliveryProcess'])->name('dashboard_orders_set_agent_delivery');
        Route::post('/setAgentCross', [OrderController::class, 'setAgentDeliveryCross'])->name('dashboard_orders_set_agent_cross_delivery');

        Route::get('/print-invoice', [OrderController::class, 'print_invoice'])->name('dashboard_orders_print_invoice_pdf');
        Route::get('/print-efaktur', [OrderController::class, 'print_efaktur'])->name('dashboard_orders_print_efaktur_pdf');
        Route::get('/print-letter', [OrderController::class, 'print_letter'])->name('dashboard_orders_print_letter_pdf');



        /*HANDLE CROSS ORDER HERE*/
        //get stock in hub based on order item
        Route::get('/resetCookieCrossOrder', [OrderController::class, 'resetCookieCrossOrder'])->name('dashboard_orders_cross_reset_cookie');
        Route::get('/getAllCookie', [OrderController::class, 'getAllCookie'])->name('dashboard_orders_cross_get_all_cookie');
        Route::get('/getStock', [OrderController::class, 'getStockInHubByOrderItem'])->name('dashboard_orders_get_stock_in_hub_by_order_item');
        Route::post('/submitCrossNotFinal', [OrderController::class, 'submitCrossNotFinal'])->name('dashboard_orders_submit_cross_not_final');
        Route::get('/submitCrossFinal', [OrderController::class, 'submitCrossFinal'])->name('dashboard_orders_submit_cross_final');

        //        Route::post('/submitCrossNotFinal', OrderController::class, 'submitCrossNotFinal')->name('dashboard_orders_submit_cross_not_final');

    });

    Route::group(['prefix' => 'orderitems'], function () {
        Route::get('/', [OrderItemController::class, 'index'])->name('dashboard_orderitems');
        Route::get('/get', [OrderItemController::class, 'get'])->name('dashboard_orderitems_detail');
        Route::get('/delete', [OrderItemController::class, 'destroy'])->name('dashboard_orderitems_delete');
        Route::post('/', [OrderItemController::class, 'store'])->name('dashboard_orderitems_post');
        Route::post('/update', [OrderItemController::class, 'submitUpdateOrder'])->name('dashboard_orderitems_post_update');
        Route::get('/datatable.json', [OrderItemController::class, '__datatable'])->name('dashboard_orderitems_table');
        Route::get('/get-products-item', [OrderItemController::class, 'getProducts'])->name('dashboard_get_products_items');
    });

    Route::group(['prefix' => 'cron'], function () {
        Route::get('/reset-membership', [ResellerController::class, 'resetMembership'])->name('dashboard_resellers_reset_membership');
    });

    Route::group(['prefix' => 'memberships'], function () {
        Route::get('/', [MembershipController::class, 'index'])->name('dashboard_memberships');
        Route::get('/get', [MembershipController::class, 'get'])->name('dashboard_memberships_detail');
        Route::get('/delete', [MembershipController::class, 'destroy'])->name('dashboard_memberships_delete');
        Route::post('/', [MembershipController::class, 'store'])->name('dashboard_memberships_post');
        Route::get('/datatable.json', [MembershipController::class, '__datatable'])->name('dashboard_memberships_table');
    });

    Route::group(['prefix' => 'vouchers'], function () {
        Route::get('/', [VoucherController::class, 'index'])->name('dashboard_vouchers');
        Route::get('/get', [VoucherController::class, 'get'])->name('dashboard_vouchers_detail');
        Route::get('/delete', [VoucherController::class, 'destroy'])->name('dashboard_vouchers_delete');
        Route::post('/', [VoucherController::class, 'store'])->name('dashboard_vouchers_post');
        Route::get('/datatable.json', [VoucherController::class, '__datatable'])->name('dashboard_vouchers_table');

        Route::get('/add', [VoucherController::class, 'add'])->name('dashboard_vouchers_add');
        Route::get('/edit', [VoucherController::class, 'edit'])->name('dashboard_vouchers_edit');
    });

    Route::group(['prefix' => 'shippingcosts'], function () {
        Route::get('/', [ShippingCostController::class, 'index'])->name('dashboard_shippingcosts');
        Route::get('/get', [ShippingCostController::class, 'get'])->name('dashboard_shippingcosts_detail');
        Route::get('/delete', [ShippingCostController::class, 'destroy'])->name('dashboard_shippingcosts_delete');
        Route::post('/', [ShippingCostController::class, 'store'])->name('dashboard_shippingcosts_post');
        Route::get('/datatable.json', [ShippingCostController::class, '__datatable'])->name('dashboard_shippingcosts_table');
    });

    Route::group(['prefix' => 'principals'], function () {
        Route::get('/', [PrincipalController::class, 'index'])->name('dashboard_principals');
        Route::get('/get', [PrincipalController::class, 'get'])->name('dashboard_principals_detail');
        Route::get('/delete', [PrincipalController::class, 'destroy'])->name('dashboard_principals_delete');
        Route::post('/', [PrincipalController::class, 'store'])->name('dashboard_principals_post');
        Route::get('/datatable.json', [PrincipalController::class, '__datatable'])->name('dashboard_principals_table');
    });

    Route::group(['prefix' => 'akumaju_principals'], function () {
        Route::get('/', [AkumajuPrincipalController::class, 'index'])->name('dashboard_akumaju_principals');
        Route::get('/get', [AkumajuPrincipalController::class, 'get'])->name('dashboard_akumaju_principals_detail');
        Route::get('/delete', [AkumajuPrincipalController::class, 'destroy'])->name('dashboard_akumaju_principals_delete');
        Route::post('/', [AkumajuPrincipalController::class, 'store'])->name('dashboard_akumaju_principals_post');
        Route::get('/datatable.json', [AkumajuPrincipalController::class, '__datatable'])->name('dashboard_akumaju_principals_table');
    });

    Route::group(['prefix' => 'sequencecodes'], function () {
        Route::get('/', [SequenceCodeController::class, 'index'])->name('dashboard_sequencecodes');
        Route::get('/get', [SequenceCodeController::class, 'get'])->name('dashboard_sequencecodes_detail');
        Route::get('/delete', [SequenceCodeController::class, 'destroy'])->name('dashboard_sequencecodes_delete');
        Route::post('/', [SequenceCodeController::class, 'store'])->name('dashboard_sequencecodes_post');
        Route::get('/datatable.json', [SequenceCodeController::class, '__datatable'])->name('dashboard_sequencecodes_table');
    });

    Route::group(['prefix' => 'user-list'], function () {


        Route::group(['prefix' => 'resellers'], function () {
            Route::get('/', [ResellerController::class, 'index'])->name('dashboard_resellers');
            Route::get('/get', [ResellerController::class, 'get'])->name('dashboard_resellers_detail');
            Route::get('/delete', [ResellerController::class, 'destroy'])->name('dashboard_resellers_delete');
            Route::get('/bypass', [ResellerController::class, 'storeRequestBypass'])->name('dashboard_resellers_bypass');
            Route::post('/', [ResellerController::class, 'store'])->name('dashboard_resellers_post');


            Route::get('/datatable.json', [ResellerController::class, '__datatable'])->name('dashboard_resellers_table');
            Route::get('/address-datatable.json', [ResellerController::class, '__datatable_address'])->name('dashboard_resellers_address_table');
            Route::get('/delete-address', [ResellerController::class, 'destroyAddress'])->name('dashboard_resellers_address_delete');
            Route::post('/update-address', [ResellerController::class, 'storeAddress'])->name('dashboard_resellers_address_update');
            Route::get('/get-address', [ResellerController::class, 'getAddress'])->name('dashboard_resellers_address_get');

            Route::get('/init-temp', [ResellerController::class, 'intTemp'])->name('dashboard_resellers_init_temp');

            Route::get('/get-pin', [ResellerController::class, 'getResetPin'])->name('dashboard_resellers_reset_pin_get');
            Route::post('/update-pin', [ResellerController::class, 'resetPin'])->name('dashboard_resellers_reset_pin');

            Route::get('/resellerPayment.json', [ResellerController::class, '__resellerPayment'])->name('dashboard_resellers_payment_table');
            Route::get('/resller-change-payment', [ResellerController::class, 'changePayment'])->name('dashboard_resellers_change_payment');
            Route::post('/post-termin-request', [ResellerController::class, 'storeRequest'])->name('dashboard_resellers_termin_request');
            Route::post('/post-termin', [ResellerController::class, 'storeTermin'])->name('dashboard_resellers_termin_post');
            Route::post('/post-block', [ResellerController::class, 'storeBlock'])->name('dashboard_resellers_block_order_post');
            Route::get('/get-termin', [ResellerController::class, 'getTermin'])->name('dashboard_resellers_termin_get');
            Route::get('/get-bypass', [ResellerController::class, 'getTermin'])->name('dashboard_resellers_bypass_get');
            Route::post('/changeStatusBulk', [ResellerController::class, 'storeBulkStatus'])->name('dashboard_reseller_change_status');
        });
    });


    Route::group(['prefix' => 'logics'], function () {
        Route::get('/', [LogicController::class, 'index'])->name('dashboard_logics');
        Route::get('/get', [LogicController::class, 'get'])->name('dashboard_logics_detail');
        Route::get('/delete', [LogicController::class, 'destroy'])->name('dashboard_logics_delete');
        Route::post('/', [LogicController::class, 'store'])->name('dashboard_logics_post');
        Route::get('/datatable.json', [LogicController::class, '__datatable'])->name('dashboard_logics_table');
    });
    Route::group(['prefix' => 'sectionbanners'], function () {
        Route::get('/', [SectionBannerController::class, 'index'])->name('dashboard_sectionbanners');
        Route::get('/get', [SectionBannerController::class, 'get'])->name('dashboard_sectionbanners_detail');
        Route::get('/delete', [SectionBannerController::class, 'destroy'])->name('dashboard_sectionbanners_delete');
        Route::post('/', [SectionBannerController::class, 'store'])->name('dashboard_sectionbanners_post');
        Route::get('/datatable.json', [SectionBannerController::class, '__datatable'])->name('dashboard_sectionbanners_table');
    });

    Route::group(['prefix' => 'sections'], function () {
        Route::get('/', [SectionController::class, 'index'])->name('dashboard_sections');
        Route::get('/get', [SectionController::class, 'get'])->name('dashboard_sections_detail');
        Route::get('/delete', [SectionController::class, 'destroy'])->name('dashboard_sections_delete');
        Route::post('/', [SectionController::class, 'store'])->name('dashboard_sections_post');
        Route::get('/datatable.json', [SectionController::class, '__datatable'])->name('dashboard_sections_table');

        Route::post('/section-product', [SectionController::class, 'storeSectionProduct'])->name('dashboard_sections_product_post');
        Route::get('/delete-section-product', [SectionController::class, 'destroySectionProduct'])->name('dashboard_sections_product_delete');
        Route::get('/sectionProduct.json', [SectionController::class, '__datatableSectionProduct'])->name('dashboard_sections_product_table');
    });


    Route::group(['prefix' => 'request'], function () {

        Route::group(['prefix' => 'register-reseller'], function () {
            Route::get('/', [ReqUsersController::class, 'index'])->name('dashboard_request_user_register');
            Route::get('/get', [ReqUsersController::class, 'get'])->name('dashboard_request_user_register_detail');
            Route::get('/delete', [ReqUsersController::class, 'destroy'])->name('dashboard_request_user_register_delete');
            Route::post('/reject', [ReqUsersController::class, 'reject'])->name('dashboard_request_user_register_reject');
            Route::post('/', [ReqUsersController::class, 'store'])->name('dashboard_request_user_register_post');
            Route::get('/datatable.json', [ReqUsersController::class, '__datatable'])->name('dashboard_request_user_register_table');

            Route::get('/get-code', [ReqUsersController::class, 'generateResellerCode'])->name('dashboard_request_user_register_generate_reseller_code');
            Route::get('/save-temp', [ReqUsersController::class, 'generateResellerCode'])->name('dashboard_request_user_register_generate_reseller_code');
        });

        Route::group(['prefix' => 'register-agent'], function () {
            Route::get('/', [ReqAgentController::class, 'index'])->name('dashboard_reqagents');
            Route::get('/get', [ReqAgentController::class, 'get'])->name('dashboard_reqagents_detail');
            Route::get('/delete', [ReqAgentController::class, 'destroy'])->name('dashboard_reqagents_delete');
            Route::post('/', [ReqAgentController::class, 'store'])->name('dashboard_reqagents_post');

            Route::get('/datatable.json', [ReqAgentController::class, '__datatable'])->name('dashboard_reqagents_table');
        });

        Route::group(['prefix' => 'coverage_area'], function () {
            Route::get('/', [ReqAreaCoveredController::class, 'index'])->name('dashboard_request_coverage_area');
            Route::get('/get', [ReqAreaCoveredController::class, 'get'])->name('dashboard_request_coverage_area_detail');
            Route::get('/delete', [ReqAreaCoveredController::class, 'destroy'])->name('dashboard_request_coverage_area_delete');
            Route::post('/', [ReqAreaCoveredController::class, 'store'])->name('dashboard_request_coverage_area_post');
            Route::get('/datatable.json', [ReqAreaCoveredController::class, '__datatable'])->name('dashboard_request_coverage_area_table');
        });
    });

    Route::group(['prefix' => 'types'], function () {
        Route::get('/', [TypeController::class, 'index'])->name('dashboard_types');
        Route::get('/get', [TypeController::class, 'get'])->name('dashboard_types_detail');
        Route::get('/delete', [TypeController::class, 'destroy'])->name('dashboard_types_delete');
        Route::post('/', [TypeController::class, 'store'])->name('dashboard_types_post');
        Route::post('/store-type-product', [TypeController::class, 'storeTypeProduct'])->name('dashboard_types_product_post');
        Route::get('/delete-type-product', [TypeController::class, 'destroyTypeProduct'])->name('dashboard_types_product_delete');


        Route::get('/datatable.json', [TypeController::class, '__datatable'])->name('dashboard_types_table');
        Route::get('/typeProduct.json', [TypeController::class, '__type_product'])->name('dashboard_types_product_table');
    });


    Route::group(['prefix' => 'typeproducts'], function () {
        Route::get('/', [TypeProductController::class, 'index'])->name('dashboard_typeproducts');
        Route::get('/get', [TypeProductController::class, 'get'])->name('dashboard_typeproducts_detail');
        Route::get('/delete', [TypeProductController::class, 'destroy'])->name('dashboard_typeproducts_delete');
        //        Route::post('/', [TypeProductController::class, 'store'])->name('dashboard_typeproducts_post');
        Route::post('/price', [TypeProductController::class, 'storePrice'])->name('dashboard_typeproducts_post_price');
        Route::get('/datatable.json', [TypeProductController::class, '__datatable'])->name('dashboard_typeproducts_table');
        Route::get('/datatable-product.json', [TypeProductController::class, '__datatableProduct'])->name('dashboard_typeproducts_table_product');
        // Route::get('/datatable-brand.json', [TypeProductController::class ,'__datatableBrand'])->name('dashboard_typeproducts_table_brand');
        // Route::get('/datatable-variant.json', [TypeProductController::class ,'__datatableVariant'])->name('dashboard_typeproducts_table_variant');

        Route::get('/datatable-product_area.json', [TypeProductController::class, '__datatable_product_area'])->name('dashboard_typeproducts_product_in_area_table');
        Route::get('/changeProductType', [TypeProductController::class, 'storeProductType'])->name('dashboard_typeproducts_change_product_type');
        Route::get('/changeProductBrandArea', [TypeProductController::class, 'storeProductBrandArea'])->name('dashboard_typeproducts_change_product_brand_area');
        Route::get('/changeProductVariantArea', [TypeProductController::class, 'storeProductVariantArea'])->name('dashboard_typeproducts_change_product_variant_area');

        Route::get('/init-area-with-all-product', [TypeProductController::class, 'initAreaWithAllProduct'])->name('dashboard_typeproducts_init_area');
        Route::get('/init-product-in-all-area', [TypeProductController::class, 'initProductInAllArea'])->name('dashboard_typeproducts_init_product_in_all_area');

        Route::post('/changeProductAreaBulk', [TypeProductController::class, 'storeBulkProductType'])->name('dashboard_typeproducts_change_bulk_product_type');
        Route::post('/changeVariantAreaBulk', [TypeProductController::class, 'storeBulkProductVariantArea'])->name('dashboard_typeproducts_change_bulk_product_variant_area');
        Route::get('/get-variant', [TypeProductController::class, 'getVariant'])->name('dashboard_typeproducts_get_variant');
    });

    Route::group(['prefix' => 'distribution'], function () {

        Route::group(['prefix' => 'product_area'], function () {});

        Route::group(['prefix' => 'product-hub'], function () {
            Route::get('/', [HubProductController::class, 'index'])->name('dashboard_hub_product');
            Route::get('/get', [HubProductController::class, 'get'])->name('dashboard_hub_product_get');

            Route::post('/', [HubProductController::class, 'storeHubProduct'])->name('dashboard_hub_product_post');
            Route::post('/change-hub-stock', [HubProductController::class, 'changeHubStock'])->name('dashboard_hub_product_change_hub_stock');
            Route::post('/change-hub-stock-bulk', [HubProductController::class, 'changeHubStockBulk'])->name('dashboard_hub_product_change_hub_stock_bulk');


            Route::get('/datatable.json', [HubProductController::class, '__datatable'])->name('dashboard_hub_product_table');
            Route::get('/datatable_product_in_hub.json', [HubProductController::class, '__datatable_product_in_hub'])->name('dashboard_product_in_hub_table');
            Route::get('/datatable_product_in_hub_with_product_id.json', [HubProductController::class, '__datatable_product_in_hub_with_product_id'])->name('dashboard_product_in_hub_with_product_id_table');

            Route::get('/datatable_product_in_hub_with_product_id.json', [HubProductController::class, '__datatable_product_in_hub_order'])->name('dashboard_product_in_hub_order');
            Route::get('/download-report-stock-in-hub', [HubProductController::class, 'downloadReport'])->name('dashboard_product_in_hub_table_report');
        });

        Route::group(['prefix' => 'product_segment'], function () {});

        Route::group(['prefix' => 'product_brand'], function () {});

        Route::group(['prefix' => 'product_category'], function () {});
    });


    Route::group(['prefix' => 'segments'], function () {
        Route::get('/', [SegmentController::class, 'index'])->name('dashboard_segments');
        Route::get('/get', [SegmentController::class, 'get'])->name('dashboard_segments_detail');
        Route::get('/delete', [SegmentController::class, 'destroy'])->name('dashboard_segments_delete');
        Route::post('/', [SegmentController::class, 'store'])->name('dashboard_segments_post');
        Route::get('/datatable.json', [SegmentController::class, '__datatable'])->name('dashboard_segments_table');

        Route::get('/get_parent', [SegmentController::class, 'getParent'])->name('dashboard_segments_get_parent');

        Route::get('/segmentPayment.json', [SegmentController::class, '__segmentPayment'])->name('dashboard_segments_payment_segment');
        Route::get('/changePaymentSegment', [SegmentController::class, 'changePaymentSegment'])->name('dashboard_segments_change_payment');
        Route::get('/segmentPrice', [SegmentController::class, 'getSegmentPrice'])->name('dashboard_segments_price');

        Route::get('/init-variant-segment-area', [SegmentController::class, 'initVariantSegmentArea'])->name('dashboard_segments_init_variant_segment_area');
    });

    Route::group(['prefix' => 'products'], function () {
        Route::get('/', [ProductController::class, 'index'])->name('dashboard_products');
        Route::get('/get', [ProductController::class, 'get'])->name('dashboard_products_detail');
        Route::get('/get-price', [ProductController::class, 'getPrice'])->name('dashboard_products_get_price');
        Route::get('/get-product', [ProductController::class, 'getProduct'])->name('dashboard_products_get_product');
        Route::get('/delete', [ProductController::class, 'destroy'])->name('dashboard_products_delete');
        Route::post('/', [ProductController::class, 'store'])->name('dashboard_products_post');

        Route::get('/search', [ProductController::class, 'search'])->name('dashboard_products_search');
        Route::get('/datatable.json', [ProductController::class, '__datatable'])->name('dashboard_products_table');


        Route::get('/add', [ProductController::class, 'add'])->name('dashboard_products_add');
        Route::get('/edit', [ProductController::class, 'edit'])->name('dashboard_products_edit');


        Route::get('/import', [ProductController::class, 'import'])->name('dashboard_products_import');
        Route::post('/import', [ProductController::class, 'submitImport'])->name('dashboard_products_import_submit');
        Route::get('/reset', [ProductController::class, 'resetCookies'])->name('dashboard_products_import_reset_cookies');

        Route::get('/get-price-by', [ProductController::class, 'getPriceBy'])->name('dashboard_products_get_price_by');
        Route::get('/get-by', [ProductController::class, 'getBy'])->name('dashboard_products_get_by');
        Route::get('/get-in', [ProductController::class, 'getIn'])->name('dashboard_products_get_in');


        Route::get('/test', [ProductController::class, 'setCookie'])->name('dashboard_products_import_set');
        Route::get('/init-base-price', [ProductController::class, 'initBasePrice'])->name('dashboard_products_set_base_price');
        Route::get('/init-price', [ProductController::class, 'initPrice'])->name('dashboard_products_set_price');
        Route::get('/init-stock', [ProductController::class, 'initStock'])->name('dashboard_products_set_stock');

        Route::get('/export-excell', [ProductController::class, 'exportExcell'])->name('dashboard_products_export_excell');


        Route::get('/search-product', [ProductController::class, 'searchProduct'])->name('dashboard_products_search');

        //        Route::get('/edit/{id}', [ProductController::class, 'edit'])->name('dashboard_products_edit');

    });

    Route::group(['prefix' => 'variants'], function () {
        Route::get('/', [VariantController::class, 'index'])->name('dashboard_variants');
        Route::get('/get', [VariantController::class, 'get'])->name('dashboard_variants_detail');
        Route::get('/delete', [VariantController::class, 'destroy'])->name('dashboard_variants_delete');
        Route::post('/', [VariantController::class, 'store'])->name('dashboard_variants_post');
        Route::get('/datatable.json', [VariantController::class, '__datatable'])->name('dashboard_variants_table');

        Route::get('/getBy', [VariantController::class, 'getBy'])->name('dashboard_variants_get_by');
    });

    Route::group(['prefix' => 'hubs'], function () {
        Route::get('/', [HubController::class, 'index'])->name('dashboard_hubs');
        Route::get('/get', [HubController::class, 'get'])->name('dashboard_hubs_detail');
        Route::get('/delete', [HubController::class, 'destroy'])->name('dashboard_hubs_delete');
        Route::post('/', [HubController::class, 'store'])->name('dashboard_hubs_post');
        Route::get('/datatable.json', [HubController::class, '__datatable'])->name('dashboard_hubs_table');
    });

    Route::group(['prefix' => 'areas'], function () {
        Route::get('/', [AreaController::class, 'index'])->name('dashboard_areas');
        Route::get('/get', [AreaController::class, 'get'])->name('dashboard_areas_detail');
        Route::get('/delete', [AreaController::class, 'destroy'])->name('dashboard_areas_delete');
        Route::post('/', [AreaController::class, 'store'])->name('dashboard_areas_post');
        Route::get('/datatable.json', [AreaController::class, '__datatable'])->name('dashboard_areas_table');

        Route::get('/changeHubArea', [AreaController::class, 'changeHubArea'])->name('dashboard_areas_change_hub');
        Route::get('/hubArea.json', [AreaController::class, '__hubArea'])->name('dashboard_areas_hub_area');

        Route::post('/post-coverage-area', [AreaController::class, 'storeCoverageArea'])->name('dashboard_areas_post_coverage');
        Route::get('/coverageArea.json', [AreaController::class, '__coverageArea'])->name('dashboard_areas_coverage_area');
        Route::get('/deleteCoverageArea', [AreaController::class, 'destroyCoverageArea'])->name('dashboard_areas_coverage_area_delete');
        Route::get('/find-by-district', [AreaController::class, 'findByDistrict'])->name('dashboard_areas_get_coverage_by_district');
    });

    Route::group(['prefix' => 'brands'], function () {
        Route::get('/', [BrandController::class, 'index'])->name('dashboard_brands');
        Route::get('/get', [BrandController::class, 'get'])->name('dashboard_brands_detail');
        Route::get('/delete', [BrandController::class, 'destroy'])->name('dashboard_brands_delete');
        Route::post('/', [BrandController::class, 'store'])->name('dashboard_brands_post');
        Route::get('/datatable.json', [BrandController::class, '__datatable'])->name('dashboard_brands_table');

        Route::get('/getBrandByCategory', [BrandController::class, 'getBy'])->name('dashboard_brands_get_by_2_category');

        Route::get('/getBrand', [BrandController::class, 'getByIn'])->name('dashboard_brands_get_by_category');
        //        Route::get('/reformatGroupData', [BrandController::class, 'reformatGroupData'])->name('dashboard_brands_reformat_group_data');
        //        Route::get('/refillGroupData', [BrandController::class, 'refillGroupData'])->name('dashboard_brands_refill_group_data');
    });

    Route::group(['prefix' => 'akumaju_brands'], function () {
        Route::get('/', [AkumajuBrandController::class, 'index'])->name('dashboard_akumaju_brands');
        Route::get('/get', [AkumajuBrandController::class, 'get'])->name('dashboard_akumaju_brands_detail');
        Route::get('/delete', [AkumajuBrandController::class, 'destroy'])->name('dashboard_akumaju_brands_delete');
        Route::post('/', [AkumajuBrandController::class, 'store'])->name('dashboard_akumaju_brands_post');
        Route::get('/datatable.json', [AkumajuBrandController::class, '__datatable'])->name('dashboard_akumaju_brands_table');

        Route::get('/getBrandByCategory', [AkumajuBrandController::class, 'getBy'])->name('dashboard_akumaju_brands_get_by_2_category');

        Route::get('/getBrand', [AkumajuBrandController::class, 'getByIn'])->name('dashboard_akumaju_brands_get_by_category');
        //        Route::get('/reformatGroupData', [AkumajuBrandController::class, 'reformatGroupData'])->name('dashboard_akumaju_brands_reformat_group_data');
        //        Route::get('/refillGroupData', [AkumajuBrandController::class, 'refillGroupData'])->name('dashboard_akumaju_brands_refill_group_data');
    });

    Route::group(['prefix' => 'categories'], function () {
        Route::get('/', [CategoryController::class, 'index'])->name('dashboard_categories');
        Route::get('/get', [CategoryController::class, 'get'])->name('dashboard_categories_detail');
        Route::get('/delete', [CategoryController::class, 'destroy'])->name('dashboard_categories_delete');
        Route::post('/', [CategoryController::class, 'store'])->name('dashboard_categories_post');
        Route::get('/datatable.json', [CategoryController::class, '__datatable'])->name('dashboard_categories_table');
    });

    Route::group(['prefix' => 'akumaju_categories'], function () {
        Route::get('/', [AkumajuCategoryController::class, 'index'])->name('dashboard_akumaju_categories');
        Route::get('/get', [AkumajuCategoryController::class, 'get'])->name('dashboard_akumaju_categories_detail');
        Route::get('/delete', [AkumajuCategoryController::class, 'destroy'])->name('dashboard_akumaju_categories_delete');
        Route::post('/', [AkumajuCategoryController::class, 'store'])->name('dashboard_akumaju_categories_post');
        Route::get('/datatable.json', [AkumajuCategoryController::class, '__datatable'])->name('dashboard_akumaju_categories_table');
    });


    /*LOCATION*/
    Route::group(['prefix' => 'location'], function () {

        Route::group(['prefix' => 'province'], function () {
            Route::get('/', [ProvinceController::class, 'index'])->name('dashboard_master_province');
            Route::get('/get', [ProvinceController::class, 'get'])->name('dashboard_master_province_detail');
            Route::get('/delete', [ProvinceController::class, 'destroy'])->name('dashboard_master_province_delete');
            Route::post('/', [ProvinceController::class, 'store'])->name('dashboard_master_province_post');
            Route::get('/datatable.json', [ProvinceController::class, '__datatable'])->name('dashboard_master_province_table');
        });

        Route::group(['prefix' => 'regency'], function () {
            Route::get('/', [RegencyController::class, 'index'])->name('dashboard_master_regency');
            Route::get('/get', [RegencyController::class, 'get'])->name('dashboard_master_regency_detail');
            Route::get('/delete', [RegencyController::class, 'destroy'])->name('dashboard_master_regency_delete');
            Route::post('/', [RegencyController::class, 'store'])->name('dashboard_master_regency_post');
            Route::get('/datatable.json', [RegencyController::class, '__datatable'])->name('dashboard_master_regency_table');

            Route::get('/get_regency', [RegencyController::class, 'getRegencyByProvince'])->name('dashboard_master_regency_get_by_province');
        });

        Route::group(['prefix' => 'district'], function () {
            Route::get('/', [DistrictController::class, 'index'])->name('dashboard_master_district');
            Route::get('/get', [DistrictController::class, 'get'])->name('dashboard_master_district_detail');
            Route::get('/delete', [DistrictController::class, 'destroy'])->name('dashboard_master_district_delete');
            Route::post('/', [DistrictController::class, 'store'])->name('dashboard_master_district_post');
            Route::get('/datatable.json', [DistrictController::class, '__datatable'])->name('dashboard_master_district_table');

            Route::get('/get_district', [DistrictController::class, 'getDistrictByRegency'])->name('dashboard_master_district_get_by_regency');
        });

        Route::group(['prefix' => 'villages'], function () {
            Route::get('/', [VillagesController::class, 'index'])->name('dashboard_master_villages');
            Route::get('/get', [VillagesController::class, 'get'])->name('dashboard_master_villages_detail');
            Route::get('/delete', [VillagesController::class, 'destroy'])->name('dashboard_master_villages_delete');
            Route::post('/', [VillagesController::class, 'store'])->name('dashboard_master_villages_post');
            Route::get('/datatable.json', [VillagesController::class, '__datatable'])->name('dashboard_master_villages_table');

            Route::get('/get_village', [VillagesController::class, 'getVillageByDistrict'])->name('dashboard_master_village_get_by_district');
        });
    });
    /*END LOCATION*/


    Route::group(['prefix' => 'profile'], function () {
        Route::get('/', [ProfileController::class, 'index'])->name('dashboard_profile');
        Route::post('/', [ProfileController::class, 'store'])->name('dashboard_profile_post');
    });

    Route::group(['prefix' => 'menu'], function () {
        Route::get('/', [MenuController::class, 'index'])->name('dashboard_menu');
        Route::get('/get', [MenuController::class, 'get'])->name('dashboard_menu_detail');
        Route::get('/delete', [MenuController::class, 'destroy'])->name('dashboard_menu_delete');
        Route::post('/', [MenuController::class, 'store'])->name('dashboard_menu_post');
        Route::get('/datatable.json', [MenuController::class, '__datatable'])->name('dashboard_menu_table');
    });

    Route::group(['prefix' => 'user'], function () {
        Route::get('/', [UserController::class, 'index'])->name('dashboard_user');
        Route::get('/get', [UserController::class, 'get'])->name('dashboard_user_detail');
        Route::get('/delete', [UserController::class, 'destroy'])->name('dashboard_user_delete');
        Route::post('/', [UserController::class, 'store'])->name('dashboard_user_post');
        Route::get('/datatable.json', [UserController::class, '__datatable'])->name('dashboard_user_table');
    });

    Route::group(['prefix' => 'group'], function () {
        Route::get('/', [GroupConttroller::class, 'index'])->name('dashboard_group');
        Route::get('/get', [GroupConttroller::class, 'get'])->name('dashboard_group_detail');
        Route::get('/delete', [GroupConttroller::class, 'destroy'])->name('dashboard_group_delete');
        Route::post('/', [GroupConttroller::class, 'store'])->name('dashboard_group_post');
        Route::get('/changeAccess', [GroupConttroller::class, 'changeAccess'])->name('dashboard_group_change_access');
        Route::get('/datatable.json', [GroupConttroller::class, '__datatable'])->name('dashboard_group_table');
        Route::get('/menuAccess.json', [GroupConttroller::class, '__menuAccess'])->name('dashboard_group_menu_access');
    });

    Route::group(['prefix' => 'setting'], function () {
        Route::get('/', [SettingController::class, 'index'])->name('dashboard_setting');
        Route::get('/get', [SettingController::class, 'get'])->name('dashboard_setting_detail');
        Route::get('/delete', [SettingController::class, 'destroy'])->name('dashboard_setting_delete');
        Route::post('/', [SettingController::class, 'store'])->name('dashboard_setting_post');
        Route::get('/datatable.json', [SettingController::class, '__datatable'])->name('dashboard_setting_table');
    });

    Route::group(['prefix' => 'permission'], function () {
        Route::get('/administrator/permission', [MenuController::class, 'index'])->name('dashboard_permission');
    });
});


//
//Route::get('/', [HomeController::class, 'index'])->name('home');
//Route::get('/administrator', [DashboardController::class, 'index'])->name('dashboard');
//Route::get('/administrator/menu', [MenuController::class, 'index'])->name('dashboard_menu');
//Route::get('/administrator/user', [UserController::class, 'index'])->name('dashboard_user');
//Route::post('/administrator/user', [UserController::class, 'store'])->name('dashboard_user_post');
//Route::get('/administrator/user/datatable.json', [UserController::class ,'datatable'])->name('user_table');
//
//Route::get('/administrator/group', [GroupConttroller::class, 'index'])->name('dashboard_group');
//Route::get('/administrator/setting', [MenuController::class, 'index'])->name('dashboard_setting');
//Route::get('/administrator/permission', [MenuController::class, 'index'])->name('dashboard_permission');


//Route::get('/home', [App\Http\Controllers\HomeController::class, 'index'])->name('home');

Auth::routes();

Route::get('/home', [App\Http\Controllers\HomeController::class, 'index'])->name('home');
