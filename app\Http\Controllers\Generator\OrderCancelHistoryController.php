<?php

/**
 * <AUTHOR>
 */

namespace App\Http\Controllers\Generator;

use App\Models\Generator\OrderCancelHistory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Http\Controllers\CoreController;


use App\Repository\Generator\OrderCancelHistoryRepository;
use App\Service\Generator\OrderCancelHistoryService;


class OrderCancelHistoryController extends CoreController
{
    protected $menu;
    private $settingVal;
    protected $ordercancelhistoryRepository;
    protected $ordercancelhistoryService;

    public function __construct(OrderCancelHistoryRepository $ordercancelhistoryRepository, OrderCancelHistoryService $ordercancelhistoryService)
    {
        $this->menu = $this->get_menu();
        $this->ordercancelhistoryRepository = $ordercancelhistoryRepository;
        $this->ordercancelhistoryService = $ordercancelhistoryService;
        $this->settingVal = $this->get_all_setting();
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View|\Illuminate\Http\Response
     */
    public function index()
    {
        return view('admin.contents.ordercancelhistory.index', [
            'menu' => ($this->menu ? $this->menu : ''),
            'setting' => ($this->settingVal ? $this->settingVal : '')
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validate = $this->ordercancelhistoryService->formValidate($request->all());
        if ($validate) {
            return response()->json(
                $validate,
                200
            );
        }
        $input = $request->all();
        $ordercancelhistory = $this->ordercancelhistoryRepository->save($input);

        return response()->json([
            'status' => 'success',
            'message' => "Data is successfully  " . (is_object($ordercancelhistory) == true ? 'added' : 'updated')
        ], 200);
    }

    public function destroy(Request $request)
    {
        $id  = $request->only('id');
        $ordercancelhistory = $this->ordercancelhistoryRepository->destroy($id);

        return response()->json([
            'status' => 'success',
            'message' => 'Data is successfully deleted'
        ], 200);
    }

    public function get(Request $request)
    {
        $id = $request->get('id');
        $data = $this->ordercancelhistoryRepository->find($id);

        return response()->json(['data' => $data], 200);
    }

    public function __datatable(Request $request)
    {
        // dd($request);
        $filter = [
            'no_order' => $request->get('no_order')
        ];
        // dd($filter);
        return $this->load_data_table($this->ordercancelhistoryRepository, $filter);
    }
}
