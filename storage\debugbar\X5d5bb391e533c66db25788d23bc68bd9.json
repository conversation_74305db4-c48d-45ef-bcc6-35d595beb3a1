{"__meta": {"id": "X5d5bb391e533c66db25788d23bc68bd9", "datetime": "2025-07-09 15:20:48", "utime": 1752049248.057144, "method": "GET", "uri": "/administrator/group/menuAccess.json?draw=1&columns%5B0%5D%5Bdata%5D=DT_RowIndex&columns%5B0%5D%5Bname%5D=DT_RowIndex&columns%5B0%5D%5Bsearchable%5D=true&columns%5B0%5D%5Borderable%5D=true&columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B1%5D%5Bdata%5D=name&columns%5B1%5D%5Bname%5D=name&columns%5B1%5D%5Bsearchable%5D=true&columns%5B1%5D%5Borderable%5D=true&columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B2%5D%5Bdata%5D=parent.name&columns%5B2%5D%5Bname%5D=parent&columns%5B2%5D%5Bsearchable%5D=true&columns%5B2%5D%5Borderable%5D=true&columns%5B2%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B2%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B3%5D%5Bdata%5D=code&columns%5B3%5D%5Bname%5D=code&columns%5B3%5D%5Bsearchable%5D=true&columns%5B3%5D%5Borderable%5D=true&columns%5B3%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B3%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B4%5D%5Bdata%5D=&columns%5B4%5D%5Bname%5D=is_viewable&columns%5B4%5D%5Bsearchable%5D=true&columns%5B4%5D%5Borderable%5D=true&columns%5B4%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B4%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B5%5D%5Bdata%5D=&columns%5B5%5D%5Bname%5D=is_addable&columns%5B5%5D%5Bsearchable%5D=true&columns%5B5%5D%5Borderable%5D=true&columns%5B5%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B5%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B6%5D%5Bdata%5D=&columns%5B6%5D%5Bname%5D=is_editable&columns%5B6%5D%5Bsearchable%5D=true&columns%5B6%5D%5Borderable%5D=true&columns%5B6%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B6%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B7%5D%5Bdata%5D=&columns%5B7%5D%5Bname%5D=is_deletable&columns%5B7%5D%5Bsearchable%5D=true&columns%5B7%5D%5Borderable%5D=true&columns%5B7%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B7%5D%5Bsearch%5D%5Bregex%5D=false&order%5B0%5D%5Bcolumn%5D=0&order%5B0%5D%5Bdir%5D=asc&start=0&length=10&search%5Bvalue%5D=&search%5Bregex%5D=false&group_id=ff9759b5-2dc5-410b-b86c-f8146c1f66d8&_=1752049215917", "ip": "127.0.0.1"}, "php": {"version": "8.3.6", "interface": "cli-server"}, "messages": {"count": 5, "messages": [{"message": "[15:20:46] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1752049246.425521, "xdebug_link": null, "collector": "log"}, {"message": "[15:20:47] LOG.warning: Creation of dynamic property App\\Http\\Controllers\\GroupConttroller::$user is deprecated in D:\\Topan\\cms-juraganbeku\\app\\Http\\Controllers\\CoreController.php on line 56", "message_html": null, "is_string": false, "label": "warning", "time": 1752049247.725873, "xdebug_link": null, "collector": "log"}, {"message": "[15:20:47] LOG.warning: Creation of dynamic property Yajra\\DataTables\\Processors\\DataProcessor::$onlyColumns is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\Processors\\DataProcessor.php on line 83", "message_html": null, "is_string": false, "label": "warning", "time": 1752049247.806963, "xdebug_link": null, "collector": "log"}, {"message": "[15:20:47] LOG.warning: Creation of dynamic property Yajra\\DataTables\\Processors\\DataProcessor::$makeHidden is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\Processors\\DataProcessor.php on line 87", "message_html": null, "is_string": false, "label": "warning", "time": 1752049247.807049, "xdebug_link": null, "collector": "log"}, {"message": "[15:20:47] LOG.warning: Creation of dynamic property Yajra\\DataTables\\Processors\\DataProcessor::$makeVisible is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\Processors\\DataProcessor.php on line 88", "message_html": null, "is_string": false, "label": "warning", "time": 1752049247.807111, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1752049245.731222, "end": 1752049248.057191, "duration": 2.3259689807891846, "duration_str": "2.33s", "measures": [{"label": "Booting", "start": 1752049245.731222, "relative_start": 0, "end": 1752049246.354896, "relative_end": 1752049246.354896, "duration": 0.6236741542816162, "duration_str": "624ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1752049246.354924, "relative_start": 0.6237020492553711, "end": 1752049248.057195, "relative_end": 4.0531158447265625e-06, "duration": 1.7022709846496582, "duration_str": "1.7s", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 48874072, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET administrator/group/menuAccess.json", "middleware": "web, auth, roles", "controller": "App\\Http\\Controllers\\GroupConttroller@__menuAccess", "namespace": null, "prefix": "administrator/group", "where": [], "as": "dashboard_group_menu_access", "file": "<a href=\"phpstorm://open?file=D:\\Topan\\cms-juraganbeku\\app\\Http\\Controllers\\GroupConttroller.php&line=105\">\\app\\Http\\Controllers\\GroupConttroller.php:105-112</a>"}, "queries": {"nb_statements": 124, "nb_failed_statements": 0, "accumulated_duration": 0.2915999999999998, "accumulated_duration_str": "292ms", "statements": [{"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'juragan_beku' and table_name = 'conf_group'", "type": "query", "params": [], "bindings": ["juragan_beku", "conf_group"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php", "line": 33}, {"index": 12, "namespace": null, "name": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php", "line": 16}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.02989, "duration_str": "29.89ms", "stmt_id": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php:33", "connection": "juragan_beku", "start_percent": 0, "width_percent": 10.25}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'juragan_beku' and table_name = 'conf_menu'", "type": "query", "params": [], "bindings": ["juragan_beku", "conf_menu"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php", "line": 33}, {"index": 12, "namespace": null, "name": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php", "line": 16}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00289, "duration_str": "2.89ms", "stmt_id": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php:33", "connection": "juragan_beku", "start_percent": 10.25, "width_percent": 0.991}, {"sql": "select * from `conf_menu` where `parent_id` is null order by `menu_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\CoreController.php", "line": 61}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\GroupConttroller.php", "line": 22}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.004070000000000001, "duration_str": "4.07ms", "stmt_id": "\\app\\Http\\Controllers\\CoreController.php:61", "connection": "juragan_beku", "start_percent": 11.241, "width_percent": 1.396}, {"sql": "select `conf_group`.*, `conf_group_menu`.`menu_id` as `pivot_menu_id`, `conf_group_menu`.`group_id` as `pivot_group_id`, `conf_group_menu`.`id` as `pivot_id`, `conf_group_menu`.`is_addable` as `pivot_is_addable`, `conf_group_menu`.`is_editable` as `pivot_is_editable`, `conf_group_menu`.`is_deletable` as `pivot_is_deletable`, `conf_group_menu`.`is_viewable` as `pivot_is_viewable`, `conf_group_menu`.`created_at` as `pivot_created_at`, `conf_group_menu`.`updated_at` as `pivot_updated_at` from `conf_group` inner join `conf_group_menu` on `conf_group`.`id` = `conf_group_menu`.`group_id` where `conf_group_menu`.`deleted_at` is null and `conf_group_menu`.`menu_id` in ('12172b03-c059-47f6-8b3a-ec45fff0bfb0', '1e171ae1-4f20-47b8-898c-a3d7028a3ab8', '2412018e-b714-4252-8674-1f7ced3b4c8e', '2afdc4cd-6699-4fdb-8774-02fd3c52c7a9', '35bdff65-5393-4c72-a885-1026cdfdaed6', '3629349a-e969-47f6-ba61-2d75c110dba7', '3927daac-5667-4d64-9c1e-ba804d715ad6', '42ecc46f-d988-4079-8fcc-e80f086ae0b8', '6d3265c5-86a7-4f93-a5e5-dbacf271bf08', '6d34fee1-1985-4885-863d-a7f34134c662', '6e655b95-8adb-45e6-9bad-aedb2917aa95', '6eb47bc6-8aa1-4548-965e-b17280f8265e', '710fd92b-bb27-4596-96a1-cc4cc9d4da53', '7afebf9b-18a3-4663-945b-cbba8403c5e5', '7c8ba58d-5450-4607-859c-152617371562', '89c3ba3a-beca-4eaf-b825-9df62b18f100', '8b6c271e-5599-4599-a037-6f36cbaf5df4', '908d7755-397d-459a-8dd9-b5c1cf2933a1', '95a301cc-df41-4af8-a735-42a88252cc28', '9775dc51-db4d-4ff3-a0eb-bc02582b2519', '9963370e-03d2-47fc-bf10-d8afb45f2778', 'a663bdd0-dd56-435c-b01c-56914d355319', 'a858dc43-a5b2-47ac-8b19-2122748997d7', 'a99e9853-75af-41ad-ab42-e367c434342e', 'ab6c7821-03a9-4d1e-b62d-4aa1e75ac2bc', 'b1ce929b-2658-464d-8423-f83025eba65a', 'b6ae8a73-8645-411b-9f3a-664ca8d0d0fb', 'b88aa02c-a949-490e-82dd-dd99fde4384d', 'ba1d3b8c-b6e1-4a5c-b0ba-9fcb73895e79', 'd302114a-df52-4183-9416-17beeb3ed264', 'f8a46c4d-7463-464c-abd5-a2187680201c', 'fb06a851-d01d-4cfd-a738-fcbc90b2ea43', 'fb962710-d5f6-4ba6-aeb4-ef7a749091ed', 'fc77a5a4-0e46-4650-8ba9-a1b32a449466') and `conf_group`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["12172b03-c059-47f6-8b3a-ec45fff0bfb0", "1e171ae1-4f20-47b8-898c-a3d7028a3ab8", "2412018e-b714-4252-8674-1f7ced3b4c8e", "2afdc4cd-6699-4fdb-8774-02fd3c52c7a9", "35bdff65-5393-4c72-a885-1026cdfdaed6", "3629349a-e969-47f6-ba61-2d75c110dba7", "3927daac-5667-4d64-9c1e-ba804d715ad6", "42ecc46f-d988-4079-8fcc-e80f086ae0b8", "6d3265c5-86a7-4f93-a5e5-dbacf271bf08", "6d34fee1-1985-4885-863d-a7f34134c662", "6e655b95-8adb-45e6-9bad-aedb2917aa95", "6eb47bc6-8aa1-4548-965e-b17280f8265e", "710fd92b-bb27-4596-96a1-cc4cc9d4da53", "7afebf9b-18a3-4663-945b-cbba8403c5e5", "7c8ba58d-5450-4607-859c-152617371562", "89c3ba3a-beca-4eaf-b825-9df62b18f100", "8b6c271e-5599-4599-a037-6f36cbaf5df4", "908d7755-397d-459a-8dd9-b5c1cf2933a1", "95a301cc-df41-4af8-a735-42a88252cc28", "9775dc51-db4d-4ff3-a0eb-bc02582b2519", "9963370e-03d2-47fc-bf10-d8afb45f2778", "a663bdd0-dd56-435c-b01c-56914d355319", "a858dc43-a5b2-47ac-8b19-2122748997d7", "a99e9853-75af-41ad-ab42-e367c434342e", "ab6c7821-03a9-4d1e-b62d-4aa1e75ac2bc", "b1ce929b-2658-464d-8423-f83025eba65a", "b6ae8a73-8645-411b-9f3a-664ca8d0d0fb", "b88aa02c-a949-490e-82dd-dd99fde4384d", "ba1d3b8c-b6e1-4a5c-b0ba-9fcb73895e79", "d302114a-df52-4183-9416-17beeb3ed264", "f8a46c4d-7463-464c-abd5-a2187680201c", "fb06a851-d01d-4cfd-a738-fcbc90b2ea43", "fb962710-d5f6-4ba6-aeb4-ef7a749091ed", "fc77a5a4-0e46-4650-8ba9-a1b32a449466"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "\\app\\Http\\Controllers\\CoreController.php", "line": 61}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\GroupConttroller.php", "line": 22}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.046270000000000006, "duration_str": "46.27ms", "stmt_id": "\\app\\Http\\Controllers\\CoreController.php:61", "connection": "juragan_beku", "start_percent": 12.637, "width_percent": 15.868}, {"sql": "select * from `conf_menu` where `conf_menu`.`parent_id` in ('12172b03-c059-47f6-8b3a-ec45fff0bfb0', '1e171ae1-4f20-47b8-898c-a3d7028a3ab8', '2412018e-b714-4252-8674-1f7ced3b4c8e', '2afdc4cd-6699-4fdb-8774-02fd3c52c7a9', '35bdff65-5393-4c72-a885-1026cdfdaed6', '3629349a-e969-47f6-ba61-2d75c110dba7', '3927daac-5667-4d64-9c1e-ba804d715ad6', '42ecc46f-d988-4079-8fcc-e80f086ae0b8', '6d3265c5-86a7-4f93-a5e5-dbacf271bf08', '6d34fee1-1985-4885-863d-a7f34134c662', '6e655b95-8adb-45e6-9bad-aedb2917aa95', '6eb47bc6-8aa1-4548-965e-b17280f8265e', '710fd92b-bb27-4596-96a1-cc4cc9d4da53', '7afebf9b-18a3-4663-945b-cbba8403c5e5', '7c8ba58d-5450-4607-859c-152617371562', '89c3ba3a-beca-4eaf-b825-9df62b18f100', '8b6c271e-5599-4599-a037-6f36cbaf5df4', '908d7755-397d-459a-8dd9-b5c1cf2933a1', '95a301cc-df41-4af8-a735-42a88252cc28', '9775dc51-db4d-4ff3-a0eb-bc02582b2519', '9963370e-03d2-47fc-bf10-d8afb45f2778', 'a663bdd0-dd56-435c-b01c-56914d355319', 'a858dc43-a5b2-47ac-8b19-2122748997d7', 'a99e9853-75af-41ad-ab42-e367c434342e', 'ab6c7821-03a9-4d1e-b62d-4aa1e75ac2bc', 'b1ce929b-2658-464d-8423-f83025eba65a', 'b6ae8a73-8645-411b-9f3a-664ca8d0d0fb', 'b88aa02c-a949-490e-82dd-dd99fde4384d', 'ba1d3b8c-b6e1-4a5c-b0ba-9fcb73895e79', 'd302114a-df52-4183-9416-17beeb3ed264', 'f8a46c4d-7463-464c-abd5-a2187680201c', 'fb06a851-d01d-4cfd-a738-fcbc90b2ea43', 'fb962710-d5f6-4ba6-aeb4-ef7a749091ed', 'fc77a5a4-0e46-4650-8ba9-a1b32a449466') and `conf_menu`.`deleted_at` is null order by `menu_order` asc", "type": "query", "params": [], "bindings": ["12172b03-c059-47f6-8b3a-ec45fff0bfb0", "1e171ae1-4f20-47b8-898c-a3d7028a3ab8", "2412018e-b714-4252-8674-1f7ced3b4c8e", "2afdc4cd-6699-4fdb-8774-02fd3c52c7a9", "35bdff65-5393-4c72-a885-1026cdfdaed6", "3629349a-e969-47f6-ba61-2d75c110dba7", "3927daac-5667-4d64-9c1e-ba804d715ad6", "42ecc46f-d988-4079-8fcc-e80f086ae0b8", "6d3265c5-86a7-4f93-a5e5-dbacf271bf08", "6d34fee1-1985-4885-863d-a7f34134c662", "6e655b95-8adb-45e6-9bad-aedb2917aa95", "6eb47bc6-8aa1-4548-965e-b17280f8265e", "710fd92b-bb27-4596-96a1-cc4cc9d4da53", "7afebf9b-18a3-4663-945b-cbba8403c5e5", "7c8ba58d-5450-4607-859c-152617371562", "89c3ba3a-beca-4eaf-b825-9df62b18f100", "8b6c271e-5599-4599-a037-6f36cbaf5df4", "908d7755-397d-459a-8dd9-b5c1cf2933a1", "95a301cc-df41-4af8-a735-42a88252cc28", "9775dc51-db4d-4ff3-a0eb-bc02582b2519", "9963370e-03d2-47fc-bf10-d8afb45f2778", "a663bdd0-dd56-435c-b01c-56914d355319", "a858dc43-a5b2-47ac-8b19-2122748997d7", "a99e9853-75af-41ad-ab42-e367c434342e", "ab6c7821-03a9-4d1e-b62d-4aa1e75ac2bc", "b1ce929b-2658-464d-8423-f83025eba65a", "b6ae8a73-8645-411b-9f3a-664ca8d0d0fb", "b88aa02c-a949-490e-82dd-dd99fde4384d", "ba1d3b8c-b6e1-4a5c-b0ba-9fcb73895e79", "d302114a-df52-4183-9416-17beeb3ed264", "f8a46c4d-7463-464c-abd5-a2187680201c", "fb06a851-d01d-4cfd-a738-fcbc90b2ea43", "fb962710-d5f6-4ba6-aeb4-ef7a749091ed", "fc77a5a4-0e46-4650-8ba9-a1b32a449466"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\CoreController.php", "line": 61}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\GroupConttroller.php", "line": 22}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00219, "duration_str": "2.19ms", "stmt_id": "\\app\\Http\\Controllers\\CoreController.php:61", "connection": "juragan_beku", "start_percent": 28.505, "width_percent": 0.751}, {"sql": "select `conf_group`.*, `conf_group_menu`.`menu_id` as `pivot_menu_id`, `conf_group_menu`.`group_id` as `pivot_group_id`, `conf_group_menu`.`id` as `pivot_id`, `conf_group_menu`.`is_addable` as `pivot_is_addable`, `conf_group_menu`.`is_editable` as `pivot_is_editable`, `conf_group_menu`.`is_deletable` as `pivot_is_deletable`, `conf_group_menu`.`is_viewable` as `pivot_is_viewable`, `conf_group_menu`.`created_at` as `pivot_created_at`, `conf_group_menu`.`updated_at` as `pivot_updated_at` from `conf_group` inner join `conf_group_menu` on `conf_group`.`id` = `conf_group_menu`.`group_id` where `conf_group_menu`.`deleted_at` is null and `conf_group_menu`.`menu_id` in ('01dcecc0-c805-4bfe-a8ad-f523d7fffa26', '04bc1af2-bad8-468b-84c8-0c47a89bae8c', '04e863de-047a-4d8e-973c-e789797e4a4f', '05425918-a3ef-4756-88a2-e907ca98a81e', '09af7259-5b37-4716-87dd-b8d1fd422e63', '0a347679-1d34-4bd5-8c36-eae1fe1c916c', '0a5aaeef-b6cc-4672-b533-7026809842dd', '0c919881-3ab7-40e6-b446-ab9c57998718', '0ddb84c4-da19-4449-9528-dafa05bc0504', '0f3d0fc9-fe09-490b-bb11-207f4948af0a', '163ae8f3-3516-4e53-938e-287538f37482', '1b656c21-3078-499f-b44a-d8f7a21eb29f', '1b705957-9f8a-4bed-a7b6-855a26a45576', '1ced30ab-5fa2-40f2-ba6d-f7a038b61983', '1e372edd-6218-4752-89fd-4507f1078de1', '2b93aa72-d810-46ea-9679-957fe4f5d997', '2e51431a-b769-4d0d-a627-d944f0bd2957', '30c7e095-e70e-4266-814f-cc421663c6e6', '354a271e-7e20-40fc-ba5a-1f23db8c00f5', '3aeb613c-d5c4-448c-b376-503252c7aa48', '3d8ae353-3736-40b9-bb1b-44b60b67eb1c', '3ec2b653-f948-46a8-8c57-aa28c8435923', '40904c07-fe95-4a4f-93e2-4ca303de8647', '41646f45-ea80-4049-8d6b-afd1ebbac87c', '426eedee-9489-48b4-93f7-c6e591342236', '4296cf9b-3658-4625-842a-5b66171da48b', '435cce6d-9645-4243-814e-c9918615d5e4', '47b4ee0e-8478-464e-acf8-c3d7c612113c', '48092a19-ba5a-4dad-b20f-5cb59aedc777', '4975676e-a170-44c6-b3ad-4304a9be5eb8', '4d9d0b21-b8b2-4abf-ac97-1a9368604300', '4ecdb1cd-aeb4-4fd0-8485-338b7439dc9d', '4f1223e9-a91b-433d-ac70-e30bec7566ad', '536ec0f4-7462-4ceb-9b39-802455cc5764', '5874e370-59e5-4e6a-8e41-52546e466492', '59b49c35-3850-4a9d-8635-d31c77e3f4bc', '5af03417-8b42-4c89-bdfb-7110826d1441', '5d2d475f-60d4-4d60-978c-cfabae2cf488', '5e8771fe-bb86-438b-b196-c4da91e6cd10', '66da0bfd-7aa5-40f6-a76c-24194c4e786a', '6725a57f-7506-43df-92a6-9b29421b93b8', '6bb01a1d-2ec1-46c9-8b6f-47b9beb76e96', '6d1dce4c-9857-41f4-84a7-f469f554b804', '6eda8f6d-c16f-4137-8142-d8c6e942d8c7', '70fbab85-b7ff-4e67-8474-14459141bb23', '713480b2-6382-4387-8d29-31a3f1f103a2', '714c0690-9a04-401b-b80b-48ed26a1c645', '74c092e4-f8bb-4d6f-996d-b1c88406adf7', '75e3e698-2b7b-4499-b449-feca72328d2e', '7d9f2c14-aea1-4d6b-8a11-42d0a619f292', '7e61030c-af9c-4edd-984b-4c414fc05b45', '7ff92f91-6239-4529-84cb-c510eaaf7394', '81212dac-52a2-4f5a-9d02-985d5b0b811c', '814fc401-5b17-4101-9816-5bab2bd106b6', '81a05ce5-b3e7-460e-b679-92eb66688c00', '835ccc11-1949-4614-a50e-577c14a03347', '843d55ee-7c77-451f-88c1-e30f3b0248e4', '84aa075a-cbf0-4863-93a0-a2659a2cd8ed', '86110001-4b90-4979-923f-9de27628d239', '88df6019-8237-471b-a436-83cc1cb0056a', '8a7c26db-38c1-4dba-8d0a-e3b76201abc8', '8c0247b3-7f55-453d-91a5-9c5b41a94b94', '8e781df6-5a3f-4f0b-bea1-865c4a659a9b', '9297b20b-2f4f-44cc-b6dc-19b46c15f549', '92fa1318-1a87-4ecf-871e-293596788039', '9c7a42d5-daf8-48fe-aa83-fa1e5c2a857b', '9e8352da-5e4d-49be-910e-1ffc6cfa909f', '9faed98a-be0f-41a8-b766-12bf76d40a22', 'a022819b-21cf-450c-93a5-6ec9841d2743', 'a15ff6a6-42e8-4a60-bdbb-ea80f41a9dac', 'a79cf485-f37f-4328-b89b-b119339398ef', 'a987c846-5c72-447b-a68c-733498ed977a', 'acd0dd1c-a5b0-4049-affe-e95ebb296478', 'ae8388f0-5f1e-4723-8de6-e708293ec9c4', 'aecd278f-abed-4f66-b295-c7aa1b078e27', 'b0bd9344-18bf-4960-9eef-bed0b2da1546', 'b1a17b3b-208f-4345-810c-ece101c456a7', 'b58b7b6f-5c00-4977-9fbd-076996aab6d0', 'b948eccc-ac77-4cca-bcbf-cb22e39fe1c1', 'bb734894-6d11-465f-b58f-fbdbbcd3f210', 'c02e2a45-bc33-40ea-88ca-8fb995680830', 'c15fc73a-270c-4044-92d1-154d10a4e503', 'c28d32ca-ae7f-44be-8058-ebddf4cea773', 'c29e03e8-4c41-4f25-9d45-ca2ee037d2a8', 'c43d7685-d7fa-45c2-bc4d-bc9242d4b688', 'c45901c9-5b09-4904-bf85-8030ba2022ed', 'c56a1871-b751-4414-8b30-1614d1eb01b8', 'c6f57b96-9ce4-4f4c-9e7a-e673aa3ba05d', 'c8562214-65c1-4aaa-a7a5-90e8d247bf04', 'c98db184-64a9-448e-b4b5-f8f94e6bb3f6', 'c9961577-ba7a-4723-bfc8-66e0227d9655', 'cace2334-2bf3-448c-b648-076e1924d4d5', 'cb56118d-3ec1-45ff-9d02-90afacb29444', 'cd3961dc-f993-46e4-b8bb-e2f95b366f7c', 'd75631a4-7999-4d30-820e-68ee58aff55b', 'e0a4f8a8-7ea4-4f50-aed0-e8c6cde2f477', 'e0f71c5e-1081-472e-bff8-d696445331fe', 'e32d1b6a-6991-4822-a4b6-367d023be2ef', 'e3408275-6056-4c66-9fda-50d2c505f806', 'e6d88533-cf7f-4ef3-b23a-caeaa0c2c08f', 'e8895d0e-590b-445b-ae8d-1bad4f81180f', 'eadc51c2-6e22-401f-8780-2d898a8db951', 'f392d4c0-6807-41ff-81a8-6c82e8753c89', 'f4cc69d3-6dcc-4dc0-ada2-5c11b4253391', 'f594b66d-2f6d-4f16-8a4e-8c81539be1d8', 'fb3a59bb-2e5b-423f-aae6-2b54a491cdb7', 'fb3f014b-7f39-4ffe-a02a-86d490496ee4', 'fc6d2d25-de43-4ec1-bb16-df8a56986091', 'fc7448fc-3f39-4894-a8e3-d194c63ee73e', 'fe956fd4-b5ee-4b7d-a33f-e9e56328e493') and `conf_group`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["01dcecc0-c805-4bfe-a8ad-f523d7fffa26", "04bc1af2-bad8-468b-84c8-0c47a89bae8c", "04e863de-047a-4d8e-973c-e789797e4a4f", "05425918-a3ef-4756-88a2-e907ca98a81e", "09af7259-5b37-4716-87dd-b8d1fd422e63", "0a347679-1d34-4bd5-8c36-eae1fe1c916c", "0a5aaeef-b6cc-4672-b533-7026809842dd", "0c919881-3ab7-40e6-b446-ab9c57998718", "0ddb84c4-da19-4449-9528-dafa05bc0504", "0f3d0fc9-fe09-490b-bb11-207f4948af0a", "163ae8f3-3516-4e53-938e-287538f37482", "1b656c21-3078-499f-b44a-d8f7a21eb29f", "1b705957-9f8a-4bed-a7b6-855a26a45576", "1ced30ab-5fa2-40f2-ba6d-f7a038b61983", "1e372edd-6218-4752-89fd-4507f1078de1", "2b93aa72-d810-46ea-9679-957fe4f5d997", "2e51431a-b769-4d0d-a627-d944f0bd2957", "30c7e095-e70e-4266-814f-cc421663c6e6", "354a271e-7e20-40fc-ba5a-1f23db8c00f5", "3aeb613c-d5c4-448c-b376-503252c7aa48", "3d8ae353-3736-40b9-bb1b-44b60b67eb1c", "3ec2b653-f948-46a8-8c57-aa28c8435923", "40904c07-fe95-4a4f-93e2-4ca303de8647", "41646f45-ea80-4049-8d6b-afd1ebbac87c", "426eedee-9489-48b4-93f7-c6e591342236", "4296cf9b-3658-4625-842a-5b66171da48b", "435cce6d-9645-4243-814e-c9918615d5e4", "47b4ee0e-8478-464e-acf8-c3d7c612113c", "48092a19-ba5a-4dad-b20f-5cb59aedc777", "4975676e-a170-44c6-b3ad-4304a9be5eb8", "4d9d0b21-b8b2-4abf-ac97-1a9368604300", "4ecdb1cd-aeb4-4fd0-8485-338b7439dc9d", "4f1223e9-a91b-433d-ac70-e30bec7566ad", "536ec0f4-7462-4ceb-9b39-802455cc5764", "5874e370-59e5-4e6a-8e41-52546e466492", "59b49c35-3850-4a9d-8635-d31c77e3f4bc", "5af03417-8b42-4c89-bdfb-7110826d1441", "5d2d475f-60d4-4d60-978c-cfabae2cf488", "5e8771fe-bb86-438b-b196-c4da91e6cd10", "66da0bfd-7aa5-40f6-a76c-24194c4e786a", "6725a57f-7506-43df-92a6-9b29421b93b8", "6bb01a1d-2ec1-46c9-8b6f-47b9beb76e96", "6d1dce4c-9857-41f4-84a7-f469f554b804", "6eda8f6d-c16f-4137-8142-d8c6e942d8c7", "70fbab85-b7ff-4e67-8474-14459141bb23", "713480b2-6382-4387-8d29-31a3f1f103a2", "714c0690-9a04-401b-b80b-48ed26a1c645", "74c092e4-f8bb-4d6f-996d-b1c88406adf7", "75e3e698-2b7b-4499-b449-feca72328d2e", "7d9f2c14-aea1-4d6b-8a11-42d0a619f292", "7e61030c-af9c-4edd-984b-4c414fc05b45", "7ff92f91-6239-4529-84cb-c510eaaf7394", "81212dac-52a2-4f5a-9d02-985d5b0b811c", "814fc401-5b17-4101-9816-5bab2bd106b6", "81a05ce5-b3e7-460e-b679-92eb66688c00", "835ccc11-1949-4614-a50e-577c14a03347", "843d55ee-7c77-451f-88c1-e30f3b0248e4", "84aa075a-cbf0-4863-93a0-a2659a2cd8ed", "86110001-4b90-4979-923f-9de27628d239", "88df6019-8237-471b-a436-83cc1cb0056a", "8a7c26db-38c1-4dba-8d0a-e3b76201abc8", "8c0247b3-7f55-453d-91a5-9c5b41a94b94", "8e781df6-5a3f-4f0b-bea1-865c4a659a9b", "9297b20b-2f4f-44cc-b6dc-19b46c15f549", "92fa1318-1a87-4ecf-871e-293596788039", "9c7a42d5-daf8-48fe-aa83-fa1e5c2a857b", "9e8352da-5e4d-49be-910e-1ffc6cfa909f", "9faed98a-be0f-41a8-b766-12bf76d40a22", "a022819b-21cf-450c-93a5-6ec9841d2743", "a15ff6a6-42e8-4a60-bdbb-ea80f41a9dac", "a79cf485-f37f-4328-b89b-b119339398ef", "a987c846-5c72-447b-a68c-733498ed977a", "acd0dd1c-a5b0-4049-affe-e95ebb296478", "ae8388f0-5f1e-4723-8de6-e708293ec9c4", "aecd278f-abed-4f66-b295-c7aa1b078e27", "b0bd9344-18bf-4960-9eef-bed0b2da1546", "b1a17b3b-208f-4345-810c-ece101c456a7", "b58b7b6f-5c00-4977-9fbd-076996aab6d0", "b948eccc-ac77-4cca-bcbf-cb22e39fe1c1", "bb734894-6d11-465f-b58f-fbdbbcd3f210", "c02e2a45-bc33-40ea-88ca-8fb995680830", "c15fc73a-270c-4044-92d1-154d10a4e503", "c28d32ca-ae7f-44be-8058-ebddf4cea773", "c29e03e8-4c41-4f25-9d45-ca2ee037d2a8", "c43d7685-d7fa-45c2-bc4d-bc9242d4b688", "c45901c9-5b09-4904-bf85-8030ba2022ed", "c56a1871-b751-4414-8b30-1614d1eb01b8", "c6f57b96-9ce4-4f4c-9e7a-e673aa3ba05d", "c8562214-65c1-4aaa-a7a5-90e8d247bf04", "c98db184-64a9-448e-b4b5-f8f94e6bb3f6", "c9961577-ba7a-4723-bfc8-66e0227d9655", "cace2334-2bf3-448c-b648-076e1924d4d5", "cb56118d-3ec1-45ff-9d02-90afacb29444", "cd3961dc-f993-46e4-b8bb-e2f95b366f7c", "d75631a4-7999-4d30-820e-68ee58aff55b", "e0a4f8a8-7ea4-4f50-aed0-e8c6cde2f477", "e0f71c5e-1081-472e-bff8-d696445331fe", "e32d1b6a-6991-4822-a4b6-367d023be2ef", "e3408275-6056-4c66-9fda-50d2c505f806", "e6d88533-cf7f-4ef3-b23a-caeaa0c2c08f", "e8895d0e-590b-445b-ae8d-1bad4f81180f", "eadc51c2-6e22-401f-8780-2d898a8db951", "f392d4c0-6807-41ff-81a8-6c82e8753c89", "f4cc69d3-6dcc-4dc0-ada2-5c11b4253391", "f594b66d-2f6d-4f16-8a4e-8c81539be1d8", "fb3a59bb-2e5b-423f-aae6-2b54a491cdb7", "fb3f014b-7f39-4ffe-a02a-86d490496ee4", "fc6d2d25-de43-4ec1-bb16-df8a56986091", "fc7448fc-3f39-4894-a8e3-d194c63ee73e", "fe956fd4-b5ee-4b7d-a33f-e9e56328e493"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "\\app\\Http\\Controllers\\CoreController.php", "line": 61}, {"index": 24, "namespace": null, "name": "\\app\\Http\\Controllers\\GroupConttroller.php", "line": 22}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.03068, "duration_str": "30.68ms", "stmt_id": "\\app\\Http\\Controllers\\CoreController.php:61", "connection": "juragan_beku", "start_percent": 29.256, "width_percent": 10.521}, {"sql": "select * from `conf_menu` where `route_name` = 'dashboard_group_menu_access' limit 1", "type": "query", "params": [], "bindings": ["dashboard_group_menu_access"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 23}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Controllers\\GroupConttroller.php", "line": 22}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00204, "duration_str": "2.04ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:23", "connection": "juragan_beku", "start_percent": 39.777, "width_percent": 0.7}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'juragan_beku' and table_name = 'conf_setting'", "type": "query", "params": [], "bindings": ["juragan_beku", "conf_setting"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php", "line": 33}, {"index": 12, "namespace": null, "name": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php", "line": 16}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\CoreController.php", "line": 25}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\GroupConttroller.php", "line": 23}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}], "duration": 0.00256, "duration_str": "2.56ms", "stmt_id": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php:33", "connection": "juragan_beku", "start_percent": 40.477, "width_percent": 0.878}, {"sql": "select * from `conf_setting` where `conf_setting`.`deleted_at` is null and `parameter` in ('logo', 'app_name', 'app_name_short', 'footer', 'logo_icon')", "type": "query", "params": [], "bindings": ["logo", "app_name", "app_name_short", "footer", "logo_icon"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\CoreController.php", "line": 25}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\GroupConttroller.php", "line": 23}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.0017, "duration_str": "1.7ms", "stmt_id": "\\app\\Http\\Controllers\\CoreController.php:25", "connection": "juragan_beku", "start_percent": 41.355, "width_percent": 0.583}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'juragan_beku' and table_name = 'conf_users'", "type": "query", "params": [], "bindings": ["juragan_beku", "conf_users"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php", "line": 33}, {"index": 12, "namespace": null, "name": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php", "line": 16}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 185}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 49}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}], "duration": 0.003, "duration_str": "3ms", "stmt_id": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php:33", "connection": "juragan_beku", "start_percent": 41.938, "width_percent": 1.029}, {"sql": "select * from `conf_users` where `id` = 'c065dc93-6b2f-4bf5-b746-6bb48713d4bf' and `conf_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["c065dc93-6b2f-4bf5-b746-6bb48713d4bf"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.0021000000000000003, "duration_str": "2.1ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "juragan_beku", "start_percent": 42.966, "width_percent": 0.72}, {"sql": "select * from `conf_menu` where `conf_menu`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": "middleware", "name": "roles", "line": 23}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.004019999999999999, "duration_str": "4.02ms", "stmt_id": "middleware::roles:23", "connection": "juragan_beku", "start_percent": 43.687, "width_percent": 1.379}, {"sql": "select `conf_group`.*, `conf_group_menu`.`menu_id` as `pivot_menu_id`, `conf_group_menu`.`group_id` as `pivot_group_id`, `conf_group_menu`.`id` as `pivot_id`, `conf_group_menu`.`is_addable` as `pivot_is_addable`, `conf_group_menu`.`is_editable` as `pivot_is_editable`, `conf_group_menu`.`is_deletable` as `pivot_is_deletable`, `conf_group_menu`.`is_viewable` as `pivot_is_viewable`, `conf_group_menu`.`created_at` as `pivot_created_at`, `conf_group_menu`.`updated_at` as `pivot_updated_at` from `conf_group` inner join `conf_group_menu` on `conf_group`.`id` = `conf_group_menu`.`group_id` where `conf_group_menu`.`deleted_at` is null and `group_id` = 'ff9759b5-2dc5-410b-b86c-f8146c1f66d8' and `conf_group_menu`.`menu_id` in ('01dcecc0-c805-4bfe-a8ad-f523d7fffa26', '04bc1af2-bad8-468b-84c8-0c47a89bae8c', '04e863de-047a-4d8e-973c-e789797e4a4f', '05425918-a3ef-4756-88a2-e907ca98a81e', '09af7259-5b37-4716-87dd-b8d1fd422e63', '0a347679-1d34-4bd5-8c36-eae1fe1c916c', '0a5aaeef-b6cc-4672-b533-7026809842dd', '0c919881-3ab7-40e6-b446-ab9c57998718', '0ddb84c4-da19-4449-9528-dafa05bc0504', '0f3d0fc9-fe09-490b-bb11-207f4948af0a', '12172b03-c059-47f6-8b3a-ec45fff0bfb0', '163ae8f3-3516-4e53-938e-287538f37482', '1b656c21-3078-499f-b44a-d8f7a21eb29f', '1b705957-9f8a-4bed-a7b6-855a26a45576', '1ced30ab-5fa2-40f2-ba6d-f7a038b61983', '1e171ae1-4f20-47b8-898c-a3d7028a3ab8', '1e372edd-6218-4752-89fd-4507f1078de1', '2412018e-b714-4252-8674-1f7ced3b4c8e', '2afdc4cd-6699-4fdb-8774-02fd3c52c7a9', '2b93aa72-d810-46ea-9679-957fe4f5d997', '2e51431a-b769-4d0d-a627-d944f0bd2957', '30c7e095-e70e-4266-814f-cc421663c6e6', '354a271e-7e20-40fc-ba5a-1f23db8c00f5', '35bdff65-5393-4c72-a885-1026cdfdaed6', '3629349a-e969-47f6-ba61-2d75c110dba7', '3927daac-5667-4d64-9c1e-ba804d715ad6', '3aeb613c-d5c4-448c-b376-503252c7aa48', '3d8ae353-3736-40b9-bb1b-44b60b67eb1c', '3ec2b653-f948-46a8-8c57-aa28c8435923', '40904c07-fe95-4a4f-93e2-4ca303de8647', '41646f45-ea80-4049-8d6b-afd1ebbac87c', '426eedee-9489-48b4-93f7-c6e591342236', '4296cf9b-3658-4625-842a-5b66171da48b', '42ecc46f-d988-4079-8fcc-e80f086ae0b8', '435cce6d-9645-4243-814e-c9918615d5e4', '4705379c-5fb2-4c32-9cd0-f8510de5f8c1', '47b4ee0e-8478-464e-acf8-c3d7c612113c', '48092a19-ba5a-4dad-b20f-5cb59aedc777', '4975676e-a170-44c6-b3ad-4304a9be5eb8', '4d9d0b21-b8b2-4abf-ac97-1a9368604300', '4ecdb1cd-aeb4-4fd0-8485-338b7439dc9d', '4f1223e9-a91b-433d-ac70-e30bec7566ad', '536ec0f4-7462-4ceb-9b39-802455cc5764', '5874e370-59e5-4e6a-8e41-52546e466492', '59b49c35-3850-4a9d-8635-d31c77e3f4bc', '5af03417-8b42-4c89-bdfb-7110826d1441', '5d2d475f-60d4-4d60-978c-cfabae2cf488', '5e8771fe-bb86-438b-b196-c4da91e6cd10', '66da0bfd-7aa5-40f6-a76c-24194c4e786a', '6725a57f-7506-43df-92a6-9b29421b93b8', '6bb01a1d-2ec1-46c9-8b6f-47b9beb76e96', '6d1dce4c-9857-41f4-84a7-f469f554b804', '6d3265c5-86a7-4f93-a5e5-dbacf271bf08', '6d34fee1-1985-4885-863d-a7f34134c662', '6e655b95-8adb-45e6-9bad-aedb2917aa95', '6eb47bc6-8aa1-4548-965e-b17280f8265e', '6eda8f6d-c16f-4137-8142-d8c6e942d8c7', '70fbab85-b7ff-4e67-8474-14459141bb23', '710fd92b-bb27-4596-96a1-cc4cc9d4da53', '713480b2-6382-4387-8d29-31a3f1f103a2', '714c0690-9a04-401b-b80b-48ed26a1c645', '74c092e4-f8bb-4d6f-996d-b1c88406adf7', '751def3a-b03e-44d2-83d3-bc5c4df78c9d', '75e3e698-2b7b-4499-b449-feca72328d2e', '7afebf9b-18a3-4663-945b-cbba8403c5e5', '7c8ba58d-5450-4607-859c-152617371562', '7d9f2c14-aea1-4d6b-8a11-42d0a619f292', '7e61030c-af9c-4edd-984b-4c414fc05b45', '7ff92f91-6239-4529-84cb-c510eaaf7394', '81212dac-52a2-4f5a-9d02-985d5b0b811c', '814fc401-5b17-4101-9816-5bab2bd106b6', '81a05ce5-b3e7-460e-b679-92eb66688c00', '835ccc11-1949-4614-a50e-577c14a03347', '843d55ee-7c77-451f-88c1-e30f3b0248e4', '84aa075a-cbf0-4863-93a0-a2659a2cd8ed', '86110001-4b90-4979-923f-9de27628d239', '88df6019-8237-471b-a436-83cc1cb0056a', '89c3ba3a-beca-4eaf-b825-9df62b18f100', '8a7c26db-38c1-4dba-8d0a-e3b76201abc8', '8b6c271e-5599-4599-a037-6f36cbaf5df4', '8c0247b3-7f55-453d-91a5-9c5b41a94b94', '8e781df6-5a3f-4f0b-bea1-865c4a659a9b', '908d7755-397d-459a-8dd9-b5c1cf2933a1', '9297b20b-2f4f-44cc-b6dc-19b46c15f549', '92fa1318-1a87-4ecf-871e-293596788039', '95a301cc-df41-4af8-a735-42a88252cc28', '9775dc51-db4d-4ff3-a0eb-bc02582b2519', '9963370e-03d2-47fc-bf10-d8afb45f2777', '9963370e-03d2-47fc-bf10-d8afb45f2778', '9c7a42d5-daf8-48fe-aa83-fa1e5c2a857b', '9e8352da-5e4d-49be-910e-1ffc6cfa909f', '9faed98a-be0f-41a8-b766-12bf76d40a22', 'a022819b-21cf-450c-93a5-6ec9841d2743', 'a15ff6a6-42e8-4a60-bdbb-ea80f41a9dac', 'a663bdd0-dd56-435c-b01c-56914d355319', 'a79cf485-f37f-4328-b89b-b119339398ef', 'a858dc43-a5b2-47ac-8b19-2122748997d7', 'a987c846-5c72-447b-a68c-733498ed977a', 'a99e9853-75af-41ad-ab42-e367c434342e', 'ab6c7821-03a9-4d1e-b62d-4aa1e75ac2bc', 'acd0dd1c-a5b0-4049-affe-e95ebb296478', 'ae8388f0-5f1e-4723-8de6-e708293ec9c4', 'aecd278f-abed-4f66-b295-c7aa1b078e27', 'b0bd9344-18bf-4960-9eef-bed0b2da1546', 'b1a17b3b-208f-4345-810c-ece101c456a7', 'b1ce929b-2658-464d-8423-f83025eba65a', 'b58b7b6f-5c00-4977-9fbd-076996aab6d0', 'b6ae8a73-8645-411b-9f3a-664ca8d0d0fb', 'b88aa02c-a949-490e-82dd-dd99fde4384d', 'b948eccc-ac77-4cca-bcbf-cb22e39fe1c1', 'ba1d3b8c-b6e1-4a5c-b0ba-9fcb73895e79', 'bb734894-6d11-465f-b58f-fbdbbcd3f210', 'c02e2a45-bc33-40ea-88ca-8fb995680830', 'c15fc73a-270c-4044-92d1-154d10a4e503', 'c28d32ca-ae7f-44be-8058-ebddf4cea773', 'c29e03e8-4c41-4f25-9d45-ca2ee037d2a8', 'c43d7685-d7fa-45c2-bc4d-bc9242d4b688', 'c45901c9-5b09-4904-bf85-8030ba2022ed', 'c56a1871-b751-4414-8b30-1614d1eb01b8', 'c6f57b96-9ce4-4f4c-9e7a-e673aa3ba05d', 'c8562214-65c1-4aaa-a7a5-90e8d247bf04', 'c98db184-64a9-448e-b4b5-f8f94e6bb3f6', 'c9961577-ba7a-4723-bfc8-66e0227d9655', 'cace2334-2bf3-448c-b648-076e1924d4d5', 'cb56118d-3ec1-45ff-9d02-90afacb29444', 'cd3961dc-f993-46e4-b8bb-e2f95b366f7c', 'd302114a-df52-4183-9416-17beeb3ed264', 'd75631a4-7999-4d30-820e-68ee58aff55b', 'e0a4f8a8-7ea4-4f50-aed0-e8c6cde2f477', 'e0f71c5e-1081-472e-bff8-d696445331fe', 'e32d1b6a-6991-4822-a4b6-367d023be2ef', 'e3408275-6056-4c66-9fda-50d2c505f806', 'e6d88533-cf7f-4ef3-b23a-caeaa0c2c08f', 'e8895d0e-590b-445b-ae8d-1bad4f81180f', 'eadc51c2-6e22-401f-8780-2d898a8db951', 'f392d4c0-6807-41ff-81a8-6c82e8753c89', 'f4cc69d3-6dcc-4dc0-ada2-5c11b4253391', 'f4cc69d3-6dcc-4dc0-ada2-5c11b4253392', 'f594b66d-2f6d-4f16-8a4e-8c81539be1d8', 'f8a46c4d-7463-464c-abd5-a2187680201c', 'fb06a851-d01d-4cfd-a738-fcbc90b2ea43', 'fb3a59bb-2e5b-423f-aae6-2b54a491cdb7', 'fb3f014b-7f39-4ffe-a02a-86d490496ee4', 'fb962710-d5f6-4ba6-aeb4-ef7a749091ed', 'fc6d2d25-de43-4ec1-bb16-df8a56986091', 'fc7448fc-3f39-4894-a8e3-d194c63ee73e', 'fc77a5a4-0e46-4650-8ba9-a1b32a449466', 'fe956fd4-b5ee-4b7d-a33f-e9e56328e493') and `conf_group`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["ff9759b5-2dc5-410b-b86c-f8146c1f66d8", "01dcecc0-c805-4bfe-a8ad-f523d7fffa26", "04bc1af2-bad8-468b-84c8-0c47a89bae8c", "04e863de-047a-4d8e-973c-e789797e4a4f", "05425918-a3ef-4756-88a2-e907ca98a81e", "09af7259-5b37-4716-87dd-b8d1fd422e63", "0a347679-1d34-4bd5-8c36-eae1fe1c916c", "0a5aaeef-b6cc-4672-b533-7026809842dd", "0c919881-3ab7-40e6-b446-ab9c57998718", "0ddb84c4-da19-4449-9528-dafa05bc0504", "0f3d0fc9-fe09-490b-bb11-207f4948af0a", "12172b03-c059-47f6-8b3a-ec45fff0bfb0", "163ae8f3-3516-4e53-938e-287538f37482", "1b656c21-3078-499f-b44a-d8f7a21eb29f", "1b705957-9f8a-4bed-a7b6-855a26a45576", "1ced30ab-5fa2-40f2-ba6d-f7a038b61983", "1e171ae1-4f20-47b8-898c-a3d7028a3ab8", "1e372edd-6218-4752-89fd-4507f1078de1", "2412018e-b714-4252-8674-1f7ced3b4c8e", "2afdc4cd-6699-4fdb-8774-02fd3c52c7a9", "2b93aa72-d810-46ea-9679-957fe4f5d997", "2e51431a-b769-4d0d-a627-d944f0bd2957", "30c7e095-e70e-4266-814f-cc421663c6e6", "354a271e-7e20-40fc-ba5a-1f23db8c00f5", "35bdff65-5393-4c72-a885-1026cdfdaed6", "3629349a-e969-47f6-ba61-2d75c110dba7", "3927daac-5667-4d64-9c1e-ba804d715ad6", "3aeb613c-d5c4-448c-b376-503252c7aa48", "3d8ae353-3736-40b9-bb1b-44b60b67eb1c", "3ec2b653-f948-46a8-8c57-aa28c8435923", "40904c07-fe95-4a4f-93e2-4ca303de8647", "41646f45-ea80-4049-8d6b-afd1ebbac87c", "426eedee-9489-48b4-93f7-c6e591342236", "4296cf9b-3658-4625-842a-5b66171da48b", "42ecc46f-d988-4079-8fcc-e80f086ae0b8", "435cce6d-9645-4243-814e-c9918615d5e4", "4705379c-5fb2-4c32-9cd0-f8510de5f8c1", "47b4ee0e-8478-464e-acf8-c3d7c612113c", "48092a19-ba5a-4dad-b20f-5cb59aedc777", "4975676e-a170-44c6-b3ad-4304a9be5eb8", "4d9d0b21-b8b2-4abf-ac97-1a9368604300", "4ecdb1cd-aeb4-4fd0-8485-338b7439dc9d", "4f1223e9-a91b-433d-ac70-e30bec7566ad", "536ec0f4-7462-4ceb-9b39-802455cc5764", "5874e370-59e5-4e6a-8e41-52546e466492", "59b49c35-3850-4a9d-8635-d31c77e3f4bc", "5af03417-8b42-4c89-bdfb-7110826d1441", "5d2d475f-60d4-4d60-978c-cfabae2cf488", "5e8771fe-bb86-438b-b196-c4da91e6cd10", "66da0bfd-7aa5-40f6-a76c-24194c4e786a", "6725a57f-7506-43df-92a6-9b29421b93b8", "6bb01a1d-2ec1-46c9-8b6f-47b9beb76e96", "6d1dce4c-9857-41f4-84a7-f469f554b804", "6d3265c5-86a7-4f93-a5e5-dbacf271bf08", "6d34fee1-1985-4885-863d-a7f34134c662", "6e655b95-8adb-45e6-9bad-aedb2917aa95", "6eb47bc6-8aa1-4548-965e-b17280f8265e", "6eda8f6d-c16f-4137-8142-d8c6e942d8c7", "70fbab85-b7ff-4e67-8474-14459141bb23", "710fd92b-bb27-4596-96a1-cc4cc9d4da53", "713480b2-6382-4387-8d29-31a3f1f103a2", "714c0690-9a04-401b-b80b-48ed26a1c645", "74c092e4-f8bb-4d6f-996d-b1c88406adf7", "751def3a-b03e-44d2-83d3-bc5c4df78c9d", "75e3e698-2b7b-4499-b449-feca72328d2e", "7afebf9b-18a3-4663-945b-cbba8403c5e5", "7c8ba58d-5450-4607-859c-152617371562", "7d9f2c14-aea1-4d6b-8a11-42d0a619f292", "7e61030c-af9c-4edd-984b-4c414fc05b45", "7ff92f91-6239-4529-84cb-c510eaaf7394", "81212dac-52a2-4f5a-9d02-985d5b0b811c", "814fc401-5b17-4101-9816-5bab2bd106b6", "81a05ce5-b3e7-460e-b679-92eb66688c00", "835ccc11-1949-4614-a50e-577c14a03347", "843d55ee-7c77-451f-88c1-e30f3b0248e4", "84aa075a-cbf0-4863-93a0-a2659a2cd8ed", "86110001-4b90-4979-923f-9de27628d239", "88df6019-8237-471b-a436-83cc1cb0056a", "89c3ba3a-beca-4eaf-b825-9df62b18f100", "8a7c26db-38c1-4dba-8d0a-e3b76201abc8", "8b6c271e-5599-4599-a037-6f36cbaf5df4", "8c0247b3-7f55-453d-91a5-9c5b41a94b94", "8e781df6-5a3f-4f0b-bea1-865c4a659a9b", "908d7755-397d-459a-8dd9-b5c1cf2933a1", "9297b20b-2f4f-44cc-b6dc-19b46c15f549", "92fa1318-1a87-4ecf-871e-293596788039", "95a301cc-df41-4af8-a735-42a88252cc28", "9775dc51-db4d-4ff3-a0eb-bc02582b2519", "9963370e-03d2-47fc-bf10-d8afb45f2777", "9963370e-03d2-47fc-bf10-d8afb45f2778", "9c7a42d5-daf8-48fe-aa83-fa1e5c2a857b", "9e8352da-5e4d-49be-910e-1ffc6cfa909f", "9faed98a-be0f-41a8-b766-12bf76d40a22", "a022819b-21cf-450c-93a5-6ec9841d2743", "a15ff6a6-42e8-4a60-bdbb-ea80f41a9dac", "a663bdd0-dd56-435c-b01c-56914d355319", "a79cf485-f37f-4328-b89b-b119339398ef", "a858dc43-a5b2-47ac-8b19-2122748997d7", "a987c846-5c72-447b-a68c-733498ed977a", "a99e9853-75af-41ad-ab42-e367c434342e", "ab6c7821-03a9-4d1e-b62d-4aa1e75ac2bc", "acd0dd1c-a5b0-4049-affe-e95ebb296478", "ae8388f0-5f1e-4723-8de6-e708293ec9c4", "aecd278f-abed-4f66-b295-c7aa1b078e27", "b0bd9344-18bf-4960-9eef-bed0b2da1546", "b1a17b3b-208f-4345-810c-ece101c456a7", "b1ce929b-2658-464d-8423-f83025eba65a", "b58b7b6f-5c00-4977-9fbd-076996aab6d0", "b6ae8a73-8645-411b-9f3a-664ca8d0d0fb", "b88aa02c-a949-490e-82dd-dd99fde4384d", "b948eccc-ac77-4cca-bcbf-cb22e39fe1c1", "ba1d3b8c-b6e1-4a5c-b0ba-9fcb73895e79", "bb734894-6d11-465f-b58f-fbdbbcd3f210", "c02e2a45-bc33-40ea-88ca-8fb995680830", "c15fc73a-270c-4044-92d1-154d10a4e503", "c28d32ca-ae7f-44be-8058-ebddf4cea773", "c29e03e8-4c41-4f25-9d45-ca2ee037d2a8", "c43d7685-d7fa-45c2-bc4d-bc9242d4b688", "c45901c9-5b09-4904-bf85-8030ba2022ed", "c56a1871-b751-4414-8b30-1614d1eb01b8", "c6f57b96-9ce4-4f4c-9e7a-e673aa3ba05d", "c8562214-65c1-4aaa-a7a5-90e8d247bf04", "c98db184-64a9-448e-b4b5-f8f94e6bb3f6", "c9961577-ba7a-4723-bfc8-66e0227d9655", "cace2334-2bf3-448c-b648-076e1924d4d5", "cb56118d-3ec1-45ff-9d02-90afacb29444", "cd3961dc-f993-46e4-b8bb-e2f95b366f7c", "d302114a-df52-4183-9416-17beeb3ed264", "d75631a4-7999-4d30-820e-68ee58aff55b", "e0a4f8a8-7ea4-4f50-aed0-e8c6cde2f477", "e0f71c5e-1081-472e-bff8-d696445331fe", "e32d1b6a-6991-4822-a4b6-367d023be2ef", "e3408275-6056-4c66-9fda-50d2c505f806", "e6d88533-cf7f-4ef3-b23a-caeaa0c2c08f", "e8895d0e-590b-445b-ae8d-1bad4f81180f", "eadc51c2-6e22-401f-8780-2d898a8db951", "f392d4c0-6807-41ff-81a8-6c82e8753c89", "f4cc69d3-6dcc-4dc0-ada2-5c11b4253391", "f4cc69d3-6dcc-4dc0-ada2-5c11b4253392", "f594b66d-2f6d-4f16-8a4e-8c81539be1d8", "f8a46c4d-7463-464c-abd5-a2187680201c", "fb06a851-d01d-4cfd-a738-fcbc90b2ea43", "fb3a59bb-2e5b-423f-aae6-2b54a491cdb7", "fb3f014b-7f39-4ffe-a02a-86d490496ee4", "fb962710-d5f6-4ba6-aeb4-ef7a749091ed", "fc6d2d25-de43-4ec1-bb16-df8a56986091", "fc7448fc-3f39-4894-a8e3-d194c63ee73e", "fc77a5a4-0e46-4650-8ba9-a1b32a449466", "fe956fd4-b5ee-4b7d-a33f-e9e56328e493"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": "middleware", "name": "roles", "line": 23}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00811, "duration_str": "8.11ms", "stmt_id": "middleware::roles:23", "connection": "juragan_beku", "start_percent": 45.065, "width_percent": 2.781}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'b88aa02c-a949-490e-82dd-dd99fde4384d' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["b88aa02c-a949-490e-82dd-dd99fde4384d"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.0012, "duration_str": "1.2ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 47.846, "width_percent": 0.412}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'a663bdd0-dd56-435c-b01c-56914d355319' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["a663bdd0-dd56-435c-b01c-56914d355319"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.0009599999999999999, "duration_str": "960μs", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 48.258, "width_percent": 0.329}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'b6ae8a73-8645-411b-9f3a-664ca8d0d0fb' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["b6ae8a73-8645-411b-9f3a-664ca8d0d0fb"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00099, "duration_str": "990μs", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 48.587, "width_percent": 0.34}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'b6ae8a73-8645-411b-9f3a-664ca8d0d0fb' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["b6ae8a73-8645-411b-9f3a-664ca8d0d0fb"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00132, "duration_str": "1.32ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 48.927, "width_percent": 0.453}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '7afebf9b-18a3-4663-945b-cbba8403c5e5' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7afebf9b-18a3-4663-945b-cbba8403c5e5"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00111, "duration_str": "1.11ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 49.379, "width_percent": 0.381}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '3927daac-5667-4d64-9c1e-ba804d715ad6' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["3927daac-5667-4d64-9c1e-ba804d715ad6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00101, "duration_str": "1.01ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 49.76, "width_percent": 0.346}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '710fd92b-bb27-4596-96a1-cc4cc9d4da53' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["710fd92b-bb27-4596-96a1-cc4cc9d4da53"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.0009599999999999999, "duration_str": "960μs", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 50.106, "width_percent": 0.329}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'b88aa02c-a949-490e-82dd-dd99fde4384d' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["b88aa02c-a949-490e-82dd-dd99fde4384d"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.0011899999999999999, "duration_str": "1.19ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 50.436, "width_percent": 0.408}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'a99e9853-75af-41ad-ab42-e367c434342e' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["a99e9853-75af-41ad-ab42-e367c434342e"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.0010500000000000002, "duration_str": "1.05ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 50.844, "width_percent": 0.36}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'b6ae8a73-8645-411b-9f3a-664ca8d0d0fb' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["b6ae8a73-8645-411b-9f3a-664ca8d0d0fb"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00123, "duration_str": "1.23ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 51.204, "width_percent": 0.422}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '908d7755-397d-459a-8dd9-b5c1cf2933a1' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["908d7755-397d-459a-8dd9-b5c1cf2933a1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00135, "duration_str": "1.35ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 51.626, "width_percent": 0.463}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '95a301cc-df41-4af8-a735-42a88252cc28' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["95a301cc-df41-4af8-a735-42a88252cc28"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00103, "duration_str": "1.03ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 52.088, "width_percent": 0.353}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'b88aa02c-a949-490e-82dd-dd99fde4384d' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["b88aa02c-a949-490e-82dd-dd99fde4384d"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.001, "duration_str": "1ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 52.442, "width_percent": 0.343}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '6e655b95-8adb-45e6-9bad-aedb2917aa95' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6e655b95-8adb-45e6-9bad-aedb2917aa95"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00086, "duration_str": "860μs", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 52.785, "width_percent": 0.295}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'b88aa02c-a949-490e-82dd-dd99fde4384d' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["b88aa02c-a949-490e-82dd-dd99fde4384d"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00115, "duration_str": "1.15ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 53.08, "width_percent": 0.394}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '42ecc46f-d988-4079-8fcc-e80f086ae0b8' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["42ecc46f-d988-4079-8fcc-e80f086ae0b8"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00123, "duration_str": "1.23ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 53.474, "width_percent": 0.422}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'b1ce929b-2658-464d-8423-f83025eba65a' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["b1ce929b-2658-464d-8423-f83025eba65a"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.0014299999999999998, "duration_str": "1.43ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 53.896, "width_percent": 0.49}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '6eb47bc6-8aa1-4548-965e-b17280f8265e' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6eb47bc6-8aa1-4548-965e-b17280f8265e"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00149, "duration_str": "1.49ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 54.386, "width_percent": 0.511}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'b88aa02c-a949-490e-82dd-dd99fde4384d' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["b88aa02c-a949-490e-82dd-dd99fde4384d"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.0015300000000000001, "duration_str": "1.53ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 54.897, "width_percent": 0.525}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'fb06a851-d01d-4cfd-a738-fcbc90b2ea43' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["fb06a851-d01d-4cfd-a738-fcbc90b2ea43"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00113, "duration_str": "1.13ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 55.422, "width_percent": 0.388}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '6e655b95-8adb-45e6-9bad-aedb2917aa95' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6e655b95-8adb-45e6-9bad-aedb2917aa95"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.0011899999999999999, "duration_str": "1.19ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 55.809, "width_percent": 0.408}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '6e655b95-8adb-45e6-9bad-aedb2917aa95' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6e655b95-8adb-45e6-9bad-aedb2917aa95"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.0014399999999999999, "duration_str": "1.44ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 56.217, "width_percent": 0.494}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '6e655b95-8adb-45e6-9bad-aedb2917aa95' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6e655b95-8adb-45e6-9bad-aedb2917aa95"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.0012, "duration_str": "1.2ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 56.711, "width_percent": 0.412}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '7afebf9b-18a3-4663-945b-cbba8403c5e5' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7afebf9b-18a3-4663-945b-cbba8403c5e5"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00155, "duration_str": "1.55ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 57.123, "width_percent": 0.532}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'a663bdd0-dd56-435c-b01c-56914d355319' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["a663bdd0-dd56-435c-b01c-56914d355319"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00146, "duration_str": "1.46ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 57.654, "width_percent": 0.501}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '2afdc4cd-6699-4fdb-8774-02fd3c52c7a9' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2afdc4cd-6699-4fdb-8774-02fd3c52c7a9"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00149, "duration_str": "1.49ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 58.155, "width_percent": 0.511}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'fb06a851-d01d-4cfd-a738-fcbc90b2ea43' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["fb06a851-d01d-4cfd-a738-fcbc90b2ea43"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00138, "duration_str": "1.38ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 58.666, "width_percent": 0.473}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'b0bd9344-18bf-4960-9eef-bed0b2da1546' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["b0bd9344-18bf-4960-9eef-bed0b2da1546"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.0011200000000000001, "duration_str": "1.12ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 59.139, "width_percent": 0.384}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '710fd92b-bb27-4596-96a1-cc4cc9d4da53' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["710fd92b-bb27-4596-96a1-cc4cc9d4da53"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.0015, "duration_str": "1.5ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 59.523, "width_percent": 0.514}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'a663bdd0-dd56-435c-b01c-56914d355319' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["a663bdd0-dd56-435c-b01c-56914d355319"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00106, "duration_str": "1.06ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 60.038, "width_percent": 0.364}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '6eb47bc6-8aa1-4548-965e-b17280f8265e' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6eb47bc6-8aa1-4548-965e-b17280f8265e"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.001, "duration_str": "1ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 60.401, "width_percent": 0.343}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '2412018e-b714-4252-8674-1f7ced3b4c8e' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2412018e-b714-4252-8674-1f7ced3b4c8e"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00111, "duration_str": "1.11ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 60.744, "width_percent": 0.381}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '2afdc4cd-6699-4fdb-8774-02fd3c52c7a9' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2afdc4cd-6699-4fdb-8774-02fd3c52c7a9"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00117, "duration_str": "1.17ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 61.125, "width_percent": 0.401}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '2412018e-b714-4252-8674-1f7ced3b4c8e' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2412018e-b714-4252-8674-1f7ced3b4c8e"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00141, "duration_str": "1.41ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 61.526, "width_percent": 0.484}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '89c3ba3a-beca-4eaf-b825-9df62b18f100' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["89c3ba3a-beca-4eaf-b825-9df62b18f100"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.0013700000000000001, "duration_str": "1.37ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 62.01, "width_percent": 0.47}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '1e171ae1-4f20-47b8-898c-a3d7028a3ab8' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1e171ae1-4f20-47b8-898c-a3d7028a3ab8"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00103, "duration_str": "1.03ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 62.479, "width_percent": 0.353}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '2412018e-b714-4252-8674-1f7ced3b4c8e' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2412018e-b714-4252-8674-1f7ced3b4c8e"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.0009599999999999999, "duration_str": "960μs", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 62.833, "width_percent": 0.329}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '6e655b95-8adb-45e6-9bad-aedb2917aa95' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6e655b95-8adb-45e6-9bad-aedb2917aa95"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00114, "duration_str": "1.14ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 63.162, "width_percent": 0.391}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '710fd92b-bb27-4596-96a1-cc4cc9d4da53' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["710fd92b-bb27-4596-96a1-cc4cc9d4da53"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00128, "duration_str": "1.28ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 63.553, "width_percent": 0.439}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'a663bdd0-dd56-435c-b01c-56914d355319' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["a663bdd0-dd56-435c-b01c-56914d355319"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00113, "duration_str": "1.13ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 63.992, "width_percent": 0.388}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '95a301cc-df41-4af8-a735-42a88252cc28' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["95a301cc-df41-4af8-a735-42a88252cc28"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.0011200000000000001, "duration_str": "1.12ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 64.379, "width_percent": 0.384}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '2412018e-b714-4252-8674-1f7ced3b4c8e' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2412018e-b714-4252-8674-1f7ced3b4c8e"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00125, "duration_str": "1.25ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 64.763, "width_percent": 0.429}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '6eb47bc6-8aa1-4548-965e-b17280f8265e' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6eb47bc6-8aa1-4548-965e-b17280f8265e"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.0011899999999999999, "duration_str": "1.19ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 65.192, "width_percent": 0.408}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'f8a46c4d-7463-464c-abd5-a2187680201c' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["f8a46c4d-7463-464c-abd5-a2187680201c"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.0009, "duration_str": "900μs", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 65.6, "width_percent": 0.309}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '908d7755-397d-459a-8dd9-b5c1cf2933a1' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["908d7755-397d-459a-8dd9-b5c1cf2933a1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00114, "duration_str": "1.14ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 65.909, "width_percent": 0.391}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '12172b03-c059-47f6-8b3a-ec45fff0bfb0' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["12172b03-c059-47f6-8b3a-ec45fff0bfb0"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00116, "duration_str": "1.16ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 66.3, "width_percent": 0.398}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'a663bdd0-dd56-435c-b01c-56914d355319' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["a663bdd0-dd56-435c-b01c-56914d355319"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00151, "duration_str": "1.51ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 66.698, "width_percent": 0.518}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '6eb47bc6-8aa1-4548-965e-b17280f8265e' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6eb47bc6-8aa1-4548-965e-b17280f8265e"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00133, "duration_str": "1.33ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 67.215, "width_percent": 0.456}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'a663bdd0-dd56-435c-b01c-56914d355319' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["a663bdd0-dd56-435c-b01c-56914d355319"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00124, "duration_str": "1.24ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 67.671, "width_percent": 0.425}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '435cce6d-9645-4243-814e-c9918615d5e4' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["435cce6d-9645-4243-814e-c9918615d5e4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00141, "duration_str": "1.41ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 68.097, "width_percent": 0.484}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'fb06a851-d01d-4cfd-a738-fcbc90b2ea43' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["fb06a851-d01d-4cfd-a738-fcbc90b2ea43"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00166, "duration_str": "1.66ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 68.58, "width_percent": 0.569}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'a663bdd0-dd56-435c-b01c-56914d355319' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["a663bdd0-dd56-435c-b01c-56914d355319"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00148, "duration_str": "1.48ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 69.15, "width_percent": 0.508}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'a663bdd0-dd56-435c-b01c-56914d355319' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["a663bdd0-dd56-435c-b01c-56914d355319"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.0013700000000000001, "duration_str": "1.37ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 69.657, "width_percent": 0.47}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '6e655b95-8adb-45e6-9bad-aedb2917aa95' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6e655b95-8adb-45e6-9bad-aedb2917aa95"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00122, "duration_str": "1.22ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 70.127, "width_percent": 0.418}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'a663bdd0-dd56-435c-b01c-56914d355319' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["a663bdd0-dd56-435c-b01c-56914d355319"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00109, "duration_str": "1.09ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 70.545, "width_percent": 0.374}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'b88aa02c-a949-490e-82dd-dd99fde4384d' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["b88aa02c-a949-490e-82dd-dd99fde4384d"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00149, "duration_str": "1.49ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 70.919, "width_percent": 0.511}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '710fd92b-bb27-4596-96a1-cc4cc9d4da53' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["710fd92b-bb27-4596-96a1-cc4cc9d4da53"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.0009, "duration_str": "900μs", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 71.43, "width_percent": 0.309}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'a99e9853-75af-41ad-ab42-e367c434342e' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["a99e9853-75af-41ad-ab42-e367c434342e"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.0013700000000000001, "duration_str": "1.37ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 71.739, "width_percent": 0.47}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'a858dc43-a5b2-47ac-8b19-2122748997d7' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["a858dc43-a5b2-47ac-8b19-2122748997d7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00136, "duration_str": "1.36ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 72.209, "width_percent": 0.466}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'fb06a851-d01d-4cfd-a738-fcbc90b2ea43' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["fb06a851-d01d-4cfd-a738-fcbc90b2ea43"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00124, "duration_str": "1.24ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 72.675, "width_percent": 0.425}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '6eb47bc6-8aa1-4548-965e-b17280f8265e' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6eb47bc6-8aa1-4548-965e-b17280f8265e"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00134, "duration_str": "1.34ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 73.1, "width_percent": 0.46}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '89c3ba3a-beca-4eaf-b825-9df62b18f100' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["89c3ba3a-beca-4eaf-b825-9df62b18f100"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.0015, "duration_str": "1.5ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 73.56, "width_percent": 0.514}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '710fd92b-bb27-4596-96a1-cc4cc9d4da53' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["710fd92b-bb27-4596-96a1-cc4cc9d4da53"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00149, "duration_str": "1.49ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 74.074, "width_percent": 0.511}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '7afebf9b-18a3-4663-945b-cbba8403c5e5' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7afebf9b-18a3-4663-945b-cbba8403c5e5"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00127, "duration_str": "1.27ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 74.585, "width_percent": 0.436}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'b1ce929b-2658-464d-8423-f83025eba65a' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["b1ce929b-2658-464d-8423-f83025eba65a"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.0011899999999999999, "duration_str": "1.19ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 75.021, "width_percent": 0.408}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '710fd92b-bb27-4596-96a1-cc4cc9d4da53' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["710fd92b-bb27-4596-96a1-cc4cc9d4da53"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00122, "duration_str": "1.22ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 75.429, "width_percent": 0.418}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'b1ce929b-2658-464d-8423-f83025eba65a' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["b1ce929b-2658-464d-8423-f83025eba65a"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00103, "duration_str": "1.03ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 75.847, "width_percent": 0.353}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '5af03417-8b42-4c89-bdfb-7110826d1441' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["5af03417-8b42-4c89-bdfb-7110826d1441"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00086, "duration_str": "860μs", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 76.2, "width_percent": 0.295}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'a858dc43-a5b2-47ac-8b19-2122748997d7' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["a858dc43-a5b2-47ac-8b19-2122748997d7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.0011, "duration_str": "1.1ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 76.495, "width_percent": 0.377}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '95a301cc-df41-4af8-a735-42a88252cc28' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["95a301cc-df41-4af8-a735-42a88252cc28"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.0010500000000000002, "duration_str": "1.05ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 76.872, "width_percent": 0.36}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'b1ce929b-2658-464d-8423-f83025eba65a' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["b1ce929b-2658-464d-8423-f83025eba65a"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00107, "duration_str": "1.07ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 77.233, "width_percent": 0.367}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '710fd92b-bb27-4596-96a1-cc4cc9d4da53' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["710fd92b-bb27-4596-96a1-cc4cc9d4da53"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.0010500000000000002, "duration_str": "1.05ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 77.599, "width_percent": 0.36}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '7afebf9b-18a3-4663-945b-cbba8403c5e5' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7afebf9b-18a3-4663-945b-cbba8403c5e5"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.0010500000000000002, "duration_str": "1.05ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 77.96, "width_percent": 0.36}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '3927daac-5667-4d64-9c1e-ba804d715ad6' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["3927daac-5667-4d64-9c1e-ba804d715ad6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.0009, "duration_str": "900μs", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 78.32, "width_percent": 0.309}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '3629349a-e969-47f6-ba61-2d75c110dba7' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["3629349a-e969-47f6-ba61-2d75c110dba7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00093, "duration_str": "930μs", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 78.628, "width_percent": 0.319}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'a99e9853-75af-41ad-ab42-e367c434342e' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["a99e9853-75af-41ad-ab42-e367c434342e"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.0009, "duration_str": "900μs", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 78.947, "width_percent": 0.309}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '6e655b95-8adb-45e6-9bad-aedb2917aa95' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6e655b95-8adb-45e6-9bad-aedb2917aa95"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00111, "duration_str": "1.11ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 79.256, "width_percent": 0.381}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'b88aa02c-a949-490e-82dd-dd99fde4384d' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["b88aa02c-a949-490e-82dd-dd99fde4384d"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00082, "duration_str": "820μs", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 79.636, "width_percent": 0.281}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '95a301cc-df41-4af8-a735-42a88252cc28' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["95a301cc-df41-4af8-a735-42a88252cc28"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00103, "duration_str": "1.03ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 79.918, "width_percent": 0.353}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'f8a46c4d-7463-464c-abd5-a2187680201c' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["f8a46c4d-7463-464c-abd5-a2187680201c"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00102, "duration_str": "1.02ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 80.271, "width_percent": 0.35}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '6e655b95-8adb-45e6-9bad-aedb2917aa95' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6e655b95-8adb-45e6-9bad-aedb2917aa95"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00133, "duration_str": "1.33ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 80.621, "width_percent": 0.456}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'b88aa02c-a949-490e-82dd-dd99fde4384d' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["b88aa02c-a949-490e-82dd-dd99fde4384d"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00148, "duration_str": "1.48ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 81.077, "width_percent": 0.508}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '95a301cc-df41-4af8-a735-42a88252cc28' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["95a301cc-df41-4af8-a735-42a88252cc28"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.0011899999999999999, "duration_str": "1.19ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 81.584, "width_percent": 0.408}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '6d3265c5-86a7-4f93-a5e5-dbacf271bf08' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6d3265c5-86a7-4f93-a5e5-dbacf271bf08"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.0017, "duration_str": "1.7ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 81.992, "width_percent": 0.583}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'a858dc43-a5b2-47ac-8b19-2122748997d7' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["a858dc43-a5b2-47ac-8b19-2122748997d7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.0013, "duration_str": "1.3ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 82.575, "width_percent": 0.446}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'b1ce929b-2658-464d-8423-f83025eba65a' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["b1ce929b-2658-464d-8423-f83025eba65a"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00149, "duration_str": "1.49ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 83.021, "width_percent": 0.511}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '89c3ba3a-beca-4eaf-b825-9df62b18f100' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["89c3ba3a-beca-4eaf-b825-9df62b18f100"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00115, "duration_str": "1.15ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 83.532, "width_percent": 0.394}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'b88aa02c-a949-490e-82dd-dd99fde4384d' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["b88aa02c-a949-490e-82dd-dd99fde4384d"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00111, "duration_str": "1.11ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 83.927, "width_percent": 0.381}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'fb06a851-d01d-4cfd-a738-fcbc90b2ea43' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["fb06a851-d01d-4cfd-a738-fcbc90b2ea43"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00149, "duration_str": "1.49ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 84.307, "width_percent": 0.511}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '1e171ae1-4f20-47b8-898c-a3d7028a3ab8' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1e171ae1-4f20-47b8-898c-a3d7028a3ab8"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00101, "duration_str": "1.01ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 84.818, "width_percent": 0.346}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '2afdc4cd-6699-4fdb-8774-02fd3c52c7a9' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2afdc4cd-6699-4fdb-8774-02fd3c52c7a9"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00131, "duration_str": "1.31ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 85.165, "width_percent": 0.449}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '12172b03-c059-47f6-8b3a-ec45fff0bfb0' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["12172b03-c059-47f6-8b3a-ec45fff0bfb0"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.0014, "duration_str": "1.4ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 85.614, "width_percent": 0.48}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '710fd92b-bb27-4596-96a1-cc4cc9d4da53' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["710fd92b-bb27-4596-96a1-cc4cc9d4da53"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.0014199999999999998, "duration_str": "1.42ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 86.094, "width_percent": 0.487}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '3927daac-5667-4d64-9c1e-ba804d715ad6' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["3927daac-5667-4d64-9c1e-ba804d715ad6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00136, "duration_str": "1.36ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 86.581, "width_percent": 0.466}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '42ecc46f-d988-4079-8fcc-e80f086ae0b8' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["42ecc46f-d988-4079-8fcc-e80f086ae0b8"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.0011899999999999999, "duration_str": "1.19ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 87.047, "width_percent": 0.408}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '710fd92b-bb27-4596-96a1-cc4cc9d4da53' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["710fd92b-bb27-4596-96a1-cc4cc9d4da53"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.0013700000000000001, "duration_str": "1.37ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 87.455, "width_percent": 0.47}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '95a301cc-df41-4af8-a735-42a88252cc28' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["95a301cc-df41-4af8-a735-42a88252cc28"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.0015300000000000001, "duration_str": "1.53ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 87.925, "width_percent": 0.525}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '6d3265c5-86a7-4f93-a5e5-dbacf271bf08' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6d3265c5-86a7-4f93-a5e5-dbacf271bf08"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00133, "duration_str": "1.33ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 88.45, "width_percent": 0.456}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '3629349a-e969-47f6-ba61-2d75c110dba7' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["3629349a-e969-47f6-ba61-2d75c110dba7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00109, "duration_str": "1.09ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 88.906, "width_percent": 0.374}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '710fd92b-bb27-4596-96a1-cc4cc9d4da53' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["710fd92b-bb27-4596-96a1-cc4cc9d4da53"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00098, "duration_str": "980μs", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 89.28, "width_percent": 0.336}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'f8a46c4d-7463-464c-abd5-a2187680201c' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["f8a46c4d-7463-464c-abd5-a2187680201c"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00128, "duration_str": "1.28ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 89.616, "width_percent": 0.439}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '95a301cc-df41-4af8-a735-42a88252cc28' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["95a301cc-df41-4af8-a735-42a88252cc28"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00135, "duration_str": "1.35ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 90.055, "width_percent": 0.463}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '95a301cc-df41-4af8-a735-42a88252cc28' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["95a301cc-df41-4af8-a735-42a88252cc28"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00114, "duration_str": "1.14ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 90.518, "width_percent": 0.391}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'a99e9853-75af-41ad-ab42-e367c434342e' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["a99e9853-75af-41ad-ab42-e367c434342e"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00111, "duration_str": "1.11ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 90.909, "width_percent": 0.381}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'b88aa02c-a949-490e-82dd-dd99fde4384d' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["b88aa02c-a949-490e-82dd-dd99fde4384d"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00138, "duration_str": "1.38ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 91.289, "width_percent": 0.473}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'a858dc43-a5b2-47ac-8b19-2122748997d7' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["a858dc43-a5b2-47ac-8b19-2122748997d7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00114, "duration_str": "1.14ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 91.763, "width_percent": 0.391}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'a99e9853-75af-41ad-ab42-e367c434342e' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["a99e9853-75af-41ad-ab42-e367c434342e"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00115, "duration_str": "1.15ms", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 92.154, "width_percent": 0.394}, {"sql": "select * from `conf_group_menu` as `gp` inner join `conf_menu` as `m` on `gp`.`menu_id` = `m`.`id` where `gp`.`deleted_at` is null and `gp`.`group_id` = 'ff9759b5-2dc5-410b-b86c-f8146c1f66d8'", "type": "query", "params": [], "bindings": ["ff9759b5-2dc5-410b-b86c-f8146c1f66d8"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "\\app\\Http\\Controllers\\CoreController.php", "line": 107}, {"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\CoreController.php", "line": 57}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 149}, {"index": 15, "namespace": "middleware", "name": "roles", "line": 47}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.00449, "duration_str": "4.49ms", "stmt_id": "\\app\\Http\\Controllers\\CoreController.php:107", "connection": "juragan_beku", "start_percent": 92.548, "width_percent": 1.54}, {"sql": "select * from `conf_menu` where `conf_menu`.`deleted_at` is null order by `menu_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\GroupConttroller.php", "line": 110}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00312, "duration_str": "3.12ms", "stmt_id": "\\app\\Http\\Controllers\\GroupConttroller.php:110", "connection": "juragan_beku", "start_percent": 94.088, "width_percent": 1.07}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` in ('12172b03-c059-47f6-8b3a-ec45fff0bfb0', '1e171ae1-4f20-47b8-898c-a3d7028a3ab8', '2412018e-b714-4252-8674-1f7ced3b4c8e', '2afdc4cd-6699-4fdb-8774-02fd3c52c7a9', '3629349a-e969-47f6-ba61-2d75c110dba7', '3927daac-5667-4d64-9c1e-ba804d715ad6', '42ecc46f-d988-4079-8fcc-e80f086ae0b8', '435cce6d-9645-4243-814e-c9918615d5e4', '5af03417-8b42-4c89-bdfb-7110826d1441', '6d3265c5-86a7-4f93-a5e5-dbacf271bf08', '6e655b95-8adb-45e6-9bad-aedb2917aa95', '6eb47bc6-8aa1-4548-965e-b17280f8265e', '710fd92b-bb27-4596-96a1-cc4cc9d4da53', '7afebf9b-18a3-4663-945b-cbba8403c5e5', '89c3ba3a-beca-4eaf-b825-9df62b18f100', '908d7755-397d-459a-8dd9-b5c1cf2933a1', '95a301cc-df41-4af8-a735-42a88252cc28', 'a663bdd0-dd56-435c-b01c-56914d355319', 'a858dc43-a5b2-47ac-8b19-2122748997d7', 'a99e9853-75af-41ad-ab42-e367c434342e', 'b0bd9344-18bf-4960-9eef-bed0b2da1546', 'b1ce929b-2658-464d-8423-f83025eba65a', 'b6ae8a73-8645-411b-9f3a-664ca8d0d0fb', 'b88aa02c-a949-490e-82dd-dd99fde4384d', 'f8a46c4d-7463-464c-abd5-a2187680201c', 'fb06a851-d01d-4cfd-a738-fcbc90b2ea43') and `conf_menu`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["12172b03-c059-47f6-8b3a-ec45fff0bfb0", "1e171ae1-4f20-47b8-898c-a3d7028a3ab8", "2412018e-b714-4252-8674-1f7ced3b4c8e", "2afdc4cd-6699-4fdb-8774-02fd3c52c7a9", "3629349a-e969-47f6-ba61-2d75c110dba7", "3927daac-5667-4d64-9c1e-ba804d715ad6", "42ecc46f-d988-4079-8fcc-e80f086ae0b8", "435cce6d-9645-4243-814e-c9918615d5e4", "5af03417-8b42-4c89-bdfb-7110826d1441", "6d3265c5-86a7-4f93-a5e5-dbacf271bf08", "6e655b95-8adb-45e6-9bad-aedb2917aa95", "6eb47bc6-8aa1-4548-965e-b17280f8265e", "710fd92b-bb27-4596-96a1-cc4cc9d4da53", "7afebf9b-18a3-4663-945b-cbba8403c5e5", "89c3ba3a-beca-4eaf-b825-9df62b18f100", "908d7755-397d-459a-8dd9-b5c1cf2933a1", "95a301cc-df41-4af8-a735-42a88252cc28", "a663bdd0-dd56-435c-b01c-56914d355319", "a858dc43-a5b2-47ac-8b19-2122748997d7", "a99e9853-75af-41ad-ab42-e367c434342e", "b0bd9344-18bf-4960-9eef-bed0b2da1546", "b1ce929b-2658-464d-8423-f83025eba65a", "b6ae8a73-8645-411b-9f3a-664ca8d0d0fb", "b88aa02c-a949-490e-82dd-dd99fde4384d", "f8a46c4d-7463-464c-abd5-a2187680201c", "fb06a851-d01d-4cfd-a738-fcbc90b2ea43"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\GroupConttroller.php", "line": 110}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00184, "duration_str": "1.84ms", "stmt_id": "\\app\\Http\\Controllers\\GroupConttroller.php:110", "connection": "juragan_beku", "start_percent": 95.158, "width_percent": 0.631}, {"sql": "select `conf_group`.*, `conf_group_menu`.`menu_id` as `pivot_menu_id`, `conf_group_menu`.`group_id` as `pivot_group_id`, `conf_group_menu`.`id` as `pivot_id`, `conf_group_menu`.`is_addable` as `pivot_is_addable`, `conf_group_menu`.`is_editable` as `pivot_is_editable`, `conf_group_menu`.`is_deletable` as `pivot_is_deletable`, `conf_group_menu`.`is_viewable` as `pivot_is_viewable`, `conf_group_menu`.`created_at` as `pivot_created_at`, `conf_group_menu`.`updated_at` as `pivot_updated_at` from `conf_group` inner join `conf_group_menu` on `conf_group`.`id` = `conf_group_menu`.`group_id` where `conf_group_menu`.`deleted_at` is null and `conf_group_menu`.`menu_id` in ('01dcecc0-c805-4bfe-a8ad-f523d7fffa26', '04bc1af2-bad8-468b-84c8-0c47a89bae8c', '04e863de-047a-4d8e-973c-e789797e4a4f', '05425918-a3ef-4756-88a2-e907ca98a81e', '09af7259-5b37-4716-87dd-b8d1fd422e63', '0a347679-1d34-4bd5-8c36-eae1fe1c916c', '0a5aaeef-b6cc-4672-b533-7026809842dd', '0c919881-3ab7-40e6-b446-ab9c57998718', '0ddb84c4-da19-4449-9528-dafa05bc0504', '0f3d0fc9-fe09-490b-bb11-207f4948af0a', '12172b03-c059-47f6-8b3a-ec45fff0bfb0', '163ae8f3-3516-4e53-938e-287538f37482', '1b656c21-3078-499f-b44a-d8f7a21eb29f', '1b705957-9f8a-4bed-a7b6-855a26a45576', '1ced30ab-5fa2-40f2-ba6d-f7a038b61983', '1e171ae1-4f20-47b8-898c-a3d7028a3ab8', '1e372edd-6218-4752-89fd-4507f1078de1', '2412018e-b714-4252-8674-1f7ced3b4c8e', '2afdc4cd-6699-4fdb-8774-02fd3c52c7a9', '2b93aa72-d810-46ea-9679-957fe4f5d997', '2e51431a-b769-4d0d-a627-d944f0bd2957', '30c7e095-e70e-4266-814f-cc421663c6e6', '354a271e-7e20-40fc-ba5a-1f23db8c00f5', '35bdff65-5393-4c72-a885-1026cdfdaed6', '3629349a-e969-47f6-ba61-2d75c110dba7', '3927daac-5667-4d64-9c1e-ba804d715ad6', '3aeb613c-d5c4-448c-b376-503252c7aa48', '3d8ae353-3736-40b9-bb1b-44b60b67eb1c', '3ec2b653-f948-46a8-8c57-aa28c8435923', '40904c07-fe95-4a4f-93e2-4ca303de8647', '41646f45-ea80-4049-8d6b-afd1ebbac87c', '426eedee-9489-48b4-93f7-c6e591342236', '4296cf9b-3658-4625-842a-5b66171da48b', '42ecc46f-d988-4079-8fcc-e80f086ae0b8', '435cce6d-9645-4243-814e-c9918615d5e4', '4705379c-5fb2-4c32-9cd0-f8510de5f8c1', '47b4ee0e-8478-464e-acf8-c3d7c612113c', '48092a19-ba5a-4dad-b20f-5cb59aedc777', '4975676e-a170-44c6-b3ad-4304a9be5eb8', '4d9d0b21-b8b2-4abf-ac97-1a9368604300', '4ecdb1cd-aeb4-4fd0-8485-338b7439dc9d', '4f1223e9-a91b-433d-ac70-e30bec7566ad', '536ec0f4-7462-4ceb-9b39-802455cc5764', '5874e370-59e5-4e6a-8e41-52546e466492', '59b49c35-3850-4a9d-8635-d31c77e3f4bc', '5af03417-8b42-4c89-bdfb-7110826d1441', '5d2d475f-60d4-4d60-978c-cfabae2cf488', '5e8771fe-bb86-438b-b196-c4da91e6cd10', '66da0bfd-7aa5-40f6-a76c-24194c4e786a', '6725a57f-7506-43df-92a6-9b29421b93b8', '6bb01a1d-2ec1-46c9-8b6f-47b9beb76e96', '6d1dce4c-9857-41f4-84a7-f469f554b804', '6d3265c5-86a7-4f93-a5e5-dbacf271bf08', '6d34fee1-1985-4885-863d-a7f34134c662', '6e655b95-8adb-45e6-9bad-aedb2917aa95', '6eb47bc6-8aa1-4548-965e-b17280f8265e', '6eda8f6d-c16f-4137-8142-d8c6e942d8c7', '70fbab85-b7ff-4e67-8474-14459141bb23', '710fd92b-bb27-4596-96a1-cc4cc9d4da53', '713480b2-6382-4387-8d29-31a3f1f103a2', '714c0690-9a04-401b-b80b-48ed26a1c645', '74c092e4-f8bb-4d6f-996d-b1c88406adf7', '751def3a-b03e-44d2-83d3-bc5c4df78c9d', '75e3e698-2b7b-4499-b449-feca72328d2e', '7afebf9b-18a3-4663-945b-cbba8403c5e5', '7c8ba58d-5450-4607-859c-152617371562', '7d9f2c14-aea1-4d6b-8a11-42d0a619f292', '7e61030c-af9c-4edd-984b-4c414fc05b45', '7ff92f91-6239-4529-84cb-c510eaaf7394', '81212dac-52a2-4f5a-9d02-985d5b0b811c', '814fc401-5b17-4101-9816-5bab2bd106b6', '81a05ce5-b3e7-460e-b679-92eb66688c00', '835ccc11-1949-4614-a50e-577c14a03347', '843d55ee-7c77-451f-88c1-e30f3b0248e4', '84aa075a-cbf0-4863-93a0-a2659a2cd8ed', '86110001-4b90-4979-923f-9de27628d239', '88df6019-8237-471b-a436-83cc1cb0056a', '89c3ba3a-beca-4eaf-b825-9df62b18f100', '8a7c26db-38c1-4dba-8d0a-e3b76201abc8', '8b6c271e-5599-4599-a037-6f36cbaf5df4', '8c0247b3-7f55-453d-91a5-9c5b41a94b94', '8e781df6-5a3f-4f0b-bea1-865c4a659a9b', '908d7755-397d-459a-8dd9-b5c1cf2933a1', '9297b20b-2f4f-44cc-b6dc-19b46c15f549', '92fa1318-1a87-4ecf-871e-293596788039', '95a301cc-df41-4af8-a735-42a88252cc28', '9775dc51-db4d-4ff3-a0eb-bc02582b2519', '9963370e-03d2-47fc-bf10-d8afb45f2777', '9963370e-03d2-47fc-bf10-d8afb45f2778', '9c7a42d5-daf8-48fe-aa83-fa1e5c2a857b', '9e8352da-5e4d-49be-910e-1ffc6cfa909f', '9faed98a-be0f-41a8-b766-12bf76d40a22', 'a022819b-21cf-450c-93a5-6ec9841d2743', 'a15ff6a6-42e8-4a60-bdbb-ea80f41a9dac', 'a663bdd0-dd56-435c-b01c-56914d355319', 'a79cf485-f37f-4328-b89b-b119339398ef', 'a858dc43-a5b2-47ac-8b19-2122748997d7', 'a987c846-5c72-447b-a68c-733498ed977a', 'a99e9853-75af-41ad-ab42-e367c434342e', 'ab6c7821-03a9-4d1e-b62d-4aa1e75ac2bc', 'acd0dd1c-a5b0-4049-affe-e95ebb296478', 'ae8388f0-5f1e-4723-8de6-e708293ec9c4', 'aecd278f-abed-4f66-b295-c7aa1b078e27', 'b0bd9344-18bf-4960-9eef-bed0b2da1546', 'b1a17b3b-208f-4345-810c-ece101c456a7', 'b1ce929b-2658-464d-8423-f83025eba65a', 'b58b7b6f-5c00-4977-9fbd-076996aab6d0', 'b6ae8a73-8645-411b-9f3a-664ca8d0d0fb', 'b88aa02c-a949-490e-82dd-dd99fde4384d', 'b948eccc-ac77-4cca-bcbf-cb22e39fe1c1', 'ba1d3b8c-b6e1-4a5c-b0ba-9fcb73895e79', 'bb734894-6d11-465f-b58f-fbdbbcd3f210', 'c02e2a45-bc33-40ea-88ca-8fb995680830', 'c15fc73a-270c-4044-92d1-154d10a4e503', 'c28d32ca-ae7f-44be-8058-ebddf4cea773', 'c29e03e8-4c41-4f25-9d45-ca2ee037d2a8', 'c43d7685-d7fa-45c2-bc4d-bc9242d4b688', 'c45901c9-5b09-4904-bf85-8030ba2022ed', 'c56a1871-b751-4414-8b30-1614d1eb01b8', 'c6f57b96-9ce4-4f4c-9e7a-e673aa3ba05d', 'c8562214-65c1-4aaa-a7a5-90e8d247bf04', 'c98db184-64a9-448e-b4b5-f8f94e6bb3f6', 'c9961577-ba7a-4723-bfc8-66e0227d9655', 'cace2334-2bf3-448c-b648-076e1924d4d5', 'cb56118d-3ec1-45ff-9d02-90afacb29444', 'cd3961dc-f993-46e4-b8bb-e2f95b366f7c', 'd302114a-df52-4183-9416-17beeb3ed264', 'd75631a4-7999-4d30-820e-68ee58aff55b', 'e0a4f8a8-7ea4-4f50-aed0-e8c6cde2f477', 'e0f71c5e-1081-472e-bff8-d696445331fe', 'e32d1b6a-6991-4822-a4b6-367d023be2ef', 'e3408275-6056-4c66-9fda-50d2c505f806', 'e6d88533-cf7f-4ef3-b23a-caeaa0c2c08f', 'e8895d0e-590b-445b-ae8d-1bad4f81180f', 'eadc51c2-6e22-401f-8780-2d898a8db951', 'f392d4c0-6807-41ff-81a8-6c82e8753c89', 'f4cc69d3-6dcc-4dc0-ada2-5c11b4253391', 'f4cc69d3-6dcc-4dc0-ada2-5c11b4253392', 'f594b66d-2f6d-4f16-8a4e-8c81539be1d8', 'f8a46c4d-7463-464c-abd5-a2187680201c', 'fb06a851-d01d-4cfd-a738-fcbc90b2ea43', 'fb3a59bb-2e5b-423f-aae6-2b54a491cdb7', 'fb3f014b-7f39-4ffe-a02a-86d490496ee4', 'fb962710-d5f6-4ba6-aeb4-ef7a749091ed', 'fc6d2d25-de43-4ec1-bb16-df8a56986091', 'fc7448fc-3f39-4894-a8e3-d194c63ee73e', 'fc77a5a4-0e46-4650-8ba9-a1b32a449466', 'fe956fd4-b5ee-4b7d-a33f-e9e56328e493') and `group_id` = 'ff9759b5-2dc5-410b-b86c-f8146c1f66d8' and `conf_group`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["01dcecc0-c805-4bfe-a8ad-f523d7fffa26", "04bc1af2-bad8-468b-84c8-0c47a89bae8c", "04e863de-047a-4d8e-973c-e789797e4a4f", "05425918-a3ef-4756-88a2-e907ca98a81e", "09af7259-5b37-4716-87dd-b8d1fd422e63", "0a347679-1d34-4bd5-8c36-eae1fe1c916c", "0a5aaeef-b6cc-4672-b533-7026809842dd", "0c919881-3ab7-40e6-b446-ab9c57998718", "0ddb84c4-da19-4449-9528-dafa05bc0504", "0f3d0fc9-fe09-490b-bb11-207f4948af0a", "12172b03-c059-47f6-8b3a-ec45fff0bfb0", "163ae8f3-3516-4e53-938e-287538f37482", "1b656c21-3078-499f-b44a-d8f7a21eb29f", "1b705957-9f8a-4bed-a7b6-855a26a45576", "1ced30ab-5fa2-40f2-ba6d-f7a038b61983", "1e171ae1-4f20-47b8-898c-a3d7028a3ab8", "1e372edd-6218-4752-89fd-4507f1078de1", "2412018e-b714-4252-8674-1f7ced3b4c8e", "2afdc4cd-6699-4fdb-8774-02fd3c52c7a9", "2b93aa72-d810-46ea-9679-957fe4f5d997", "2e51431a-b769-4d0d-a627-d944f0bd2957", "30c7e095-e70e-4266-814f-cc421663c6e6", "354a271e-7e20-40fc-ba5a-1f23db8c00f5", "35bdff65-5393-4c72-a885-1026cdfdaed6", "3629349a-e969-47f6-ba61-2d75c110dba7", "3927daac-5667-4d64-9c1e-ba804d715ad6", "3aeb613c-d5c4-448c-b376-503252c7aa48", "3d8ae353-3736-40b9-bb1b-44b60b67eb1c", "3ec2b653-f948-46a8-8c57-aa28c8435923", "40904c07-fe95-4a4f-93e2-4ca303de8647", "41646f45-ea80-4049-8d6b-afd1ebbac87c", "426eedee-9489-48b4-93f7-c6e591342236", "4296cf9b-3658-4625-842a-5b66171da48b", "42ecc46f-d988-4079-8fcc-e80f086ae0b8", "435cce6d-9645-4243-814e-c9918615d5e4", "4705379c-5fb2-4c32-9cd0-f8510de5f8c1", "47b4ee0e-8478-464e-acf8-c3d7c612113c", "48092a19-ba5a-4dad-b20f-5cb59aedc777", "4975676e-a170-44c6-b3ad-4304a9be5eb8", "4d9d0b21-b8b2-4abf-ac97-1a9368604300", "4ecdb1cd-aeb4-4fd0-8485-338b7439dc9d", "4f1223e9-a91b-433d-ac70-e30bec7566ad", "536ec0f4-7462-4ceb-9b39-802455cc5764", "5874e370-59e5-4e6a-8e41-52546e466492", "59b49c35-3850-4a9d-8635-d31c77e3f4bc", "5af03417-8b42-4c89-bdfb-7110826d1441", "5d2d475f-60d4-4d60-978c-cfabae2cf488", "5e8771fe-bb86-438b-b196-c4da91e6cd10", "66da0bfd-7aa5-40f6-a76c-24194c4e786a", "6725a57f-7506-43df-92a6-9b29421b93b8", "6bb01a1d-2ec1-46c9-8b6f-47b9beb76e96", "6d1dce4c-9857-41f4-84a7-f469f554b804", "6d3265c5-86a7-4f93-a5e5-dbacf271bf08", "6d34fee1-1985-4885-863d-a7f34134c662", "6e655b95-8adb-45e6-9bad-aedb2917aa95", "6eb47bc6-8aa1-4548-965e-b17280f8265e", "6eda8f6d-c16f-4137-8142-d8c6e942d8c7", "70fbab85-b7ff-4e67-8474-14459141bb23", "710fd92b-bb27-4596-96a1-cc4cc9d4da53", "713480b2-6382-4387-8d29-31a3f1f103a2", "714c0690-9a04-401b-b80b-48ed26a1c645", "74c092e4-f8bb-4d6f-996d-b1c88406adf7", "751def3a-b03e-44d2-83d3-bc5c4df78c9d", "75e3e698-2b7b-4499-b449-feca72328d2e", "7afebf9b-18a3-4663-945b-cbba8403c5e5", "7c8ba58d-5450-4607-859c-152617371562", "7d9f2c14-aea1-4d6b-8a11-42d0a619f292", "7e61030c-af9c-4edd-984b-4c414fc05b45", "7ff92f91-6239-4529-84cb-c510eaaf7394", "81212dac-52a2-4f5a-9d02-985d5b0b811c", "814fc401-5b17-4101-9816-5bab2bd106b6", "81a05ce5-b3e7-460e-b679-92eb66688c00", "835ccc11-1949-4614-a50e-577c14a03347", "843d55ee-7c77-451f-88c1-e30f3b0248e4", "84aa075a-cbf0-4863-93a0-a2659a2cd8ed", "86110001-4b90-4979-923f-9de27628d239", "88df6019-8237-471b-a436-83cc1cb0056a", "89c3ba3a-beca-4eaf-b825-9df62b18f100", "8a7c26db-38c1-4dba-8d0a-e3b76201abc8", "8b6c271e-5599-4599-a037-6f36cbaf5df4", "8c0247b3-7f55-453d-91a5-9c5b41a94b94", "8e781df6-5a3f-4f0b-bea1-865c4a659a9b", "908d7755-397d-459a-8dd9-b5c1cf2933a1", "9297b20b-2f4f-44cc-b6dc-19b46c15f549", "92fa1318-1a87-4ecf-871e-293596788039", "95a301cc-df41-4af8-a735-42a88252cc28", "9775dc51-db4d-4ff3-a0eb-bc02582b2519", "9963370e-03d2-47fc-bf10-d8afb45f2777", "9963370e-03d2-47fc-bf10-d8afb45f2778", "9c7a42d5-daf8-48fe-aa83-fa1e5c2a857b", "9e8352da-5e4d-49be-910e-1ffc6cfa909f", "9faed98a-be0f-41a8-b766-12bf76d40a22", "a022819b-21cf-450c-93a5-6ec9841d2743", "a15ff6a6-42e8-4a60-bdbb-ea80f41a9dac", "a663bdd0-dd56-435c-b01c-56914d355319", "a79cf485-f37f-4328-b89b-b119339398ef", "a858dc43-a5b2-47ac-8b19-2122748997d7", "a987c846-5c72-447b-a68c-733498ed977a", "a99e9853-75af-41ad-ab42-e367c434342e", "ab6c7821-03a9-4d1e-b62d-4aa1e75ac2bc", "acd0dd1c-a5b0-4049-affe-e95ebb296478", "ae8388f0-5f1e-4723-8de6-e708293ec9c4", "aecd278f-abed-4f66-b295-c7aa1b078e27", "b0bd9344-18bf-4960-9eef-bed0b2da1546", "b1a17b3b-208f-4345-810c-ece101c456a7", "b1ce929b-2658-464d-8423-f83025eba65a", "b58b7b6f-5c00-4977-9fbd-076996aab6d0", "b6ae8a73-8645-411b-9f3a-664ca8d0d0fb", "b88aa02c-a949-490e-82dd-dd99fde4384d", "b948eccc-ac77-4cca-bcbf-cb22e39fe1c1", "ba1d3b8c-b6e1-4a5c-b0ba-9fcb73895e79", "bb734894-6d11-465f-b58f-fbdbbcd3f210", "c02e2a45-bc33-40ea-88ca-8fb995680830", "c15fc73a-270c-4044-92d1-154d10a4e503", "c28d32ca-ae7f-44be-8058-ebddf4cea773", "c29e03e8-4c41-4f25-9d45-ca2ee037d2a8", "c43d7685-d7fa-45c2-bc4d-bc9242d4b688", "c45901c9-5b09-4904-bf85-8030ba2022ed", "c56a1871-b751-4414-8b30-1614d1eb01b8", "c6f57b96-9ce4-4f4c-9e7a-e673aa3ba05d", "c8562214-65c1-4aaa-a7a5-90e8d247bf04", "c98db184-64a9-448e-b4b5-f8f94e6bb3f6", "c9961577-ba7a-4723-bfc8-66e0227d9655", "cace2334-2bf3-448c-b648-076e1924d4d5", "cb56118d-3ec1-45ff-9d02-90afacb29444", "cd3961dc-f993-46e4-b8bb-e2f95b366f7c", "d302114a-df52-4183-9416-17beeb3ed264", "d75631a4-7999-4d30-820e-68ee58aff55b", "e0a4f8a8-7ea4-4f50-aed0-e8c6cde2f477", "e0f71c5e-1081-472e-bff8-d696445331fe", "e32d1b6a-6991-4822-a4b6-367d023be2ef", "e3408275-6056-4c66-9fda-50d2c505f806", "e6d88533-cf7f-4ef3-b23a-caeaa0c2c08f", "e8895d0e-590b-445b-ae8d-1bad4f81180f", "eadc51c2-6e22-401f-8780-2d898a8db951", "f392d4c0-6807-41ff-81a8-6c82e8753c89", "f4cc69d3-6dcc-4dc0-ada2-5c11b4253391", "f4cc69d3-6dcc-4dc0-ada2-5c11b4253392", "f594b66d-2f6d-4f16-8a4e-8c81539be1d8", "f8a46c4d-7463-464c-abd5-a2187680201c", "fb06a851-d01d-4cfd-a738-fcbc90b2ea43", "fb3a59bb-2e5b-423f-aae6-2b54a491cdb7", "fb3f014b-7f39-4ffe-a02a-86d490496ee4", "fb962710-d5f6-4ba6-aeb4-ef7a749091ed", "fc6d2d25-de43-4ec1-bb16-df8a56986091", "fc7448fc-3f39-4894-a8e3-d194c63ee73e", "fc77a5a4-0e46-4650-8ba9-a1b32a449466", "fe956fd4-b5ee-4b7d-a33f-e9e56328e493", "ff9759b5-2dc5-410b-b86c-f8146c1f66d8"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "\\app\\Http\\Controllers\\GroupConttroller.php", "line": 110}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.01228, "duration_str": "12.28ms", "stmt_id": "\\app\\Http\\Controllers\\GroupConttroller.php:110", "connection": "juragan_beku", "start_percent": 95.789, "width_percent": 4.211}]}, "models": {"data": {"App\\Models\\User": 1, "App\\Models\\Setting": 4, "App\\Models\\Group": 2742, "App\\Models\\Menu": 573}, "count": 3320}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QoZh1I8mypHP2aMUh0DEtCk2CpnpWBdxW5ZtIuuB", "url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/administrator/group\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "c065dc93-6b2f-4bf5-b746-6bb48713d4bf"}, "request": {"path_info": "/administrator/group/menuAccess.json", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-38352678 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>draw</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>columns</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"11 characters\">DT_RowIndex</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">DT_RowIndex</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"11 characters\">parent.name</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">parent</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"4 characters\">code</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">code</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">is_viewable</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">is_addable</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">is_editable</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">is_deletable</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>column</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>dir</span>\" => \"<span class=sf-dump-str title=\"3 characters\">asc</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>start</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>length</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>group_id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">ff9759b5-2dc5-410b-b86c-f8146c1f66d8</span>\"\n  \"<span class=sf-dump-key>_</span>\" => \"<span class=sf-dump-str title=\"13 characters\">1752049215917</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-38352678\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-248747043 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>draw</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>columns</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"11 characters\">DT_RowIndex</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">DT_RowIndex</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"11 characters\">parent.name</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">parent</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"4 characters\">code</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">code</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">is_viewable</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">is_addable</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">is_editable</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">is_deletable</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>column</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>dir</span>\" => \"<span class=sf-dump-str title=\"3 characters\">asc</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>start</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>length</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>group_id</span>\" => \"<span class=sf-dump-str title=\"36 characters\">ff9759b5-2dc5-410b-b86c-f8146c1f66d8</span>\"\n  \"<span class=sf-dump-key>_</span>\" => \"<span class=sf-dump-str title=\"13 characters\">1752049215917</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-248747043\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-799253172 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/administrator/group</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">en-US,en;q=0.9,id;q=0.8,zh-CN;q=0.7,zh;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"808 characters\">_ga=GA1.1.587546091.1749434964; _ga_GBZ3SGGX85=GS2.1.s1751877726$o26$g0$t1751877726$j60$l0$h0; XSRF-TOKEN=eyJpdiI6InlsWmxvZG85ZEVGMStjMDJXNWZpaFE9PSIsInZhbHVlIjoiU1BhUStwamZMaXpZL0hiOU9VTGJmcVZhcVM3Z3NzUVVhd3VhR1g0STNtcHVrcURxdENEOHoyVVZFem8yNWp5S1ZHenVUSmg0Mk5wU3BBd3FBUG0rRTIrN2EwcHNNZ1UwWStMd0l6SktGRG5laGZrRzhNZnBIeDd3aE1keXhhYk0iLCJtYWMiOiIzYzJjYWI5YzQxNTQxZjA5YmQwMzNiMjQxMjU4OTZlNDZhMmQ1NTRkMWEyODFiMDExMjYxZjc5MzdlM2IxMDU1IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ikt4YUd4Q2UyR1ZxTUh1RG9VL2VIT3c9PSIsInZhbHVlIjoieldoSVVBbmp5bzVmdVA4UllWcFNnS3pENTJLSzFRSm1icmRJQ3RXUjNTREJSdXVTMGg1V2tudEZaYUwzaDhxWjJQcTNrQXVnU2NXN3hTQTFkcmxpdUU2V2Evd0o0WS9Kc0o4RkdMU3J6UnRYS0tyMkljUDliRElhWUxwcTdVUG0iLCJtYWMiOiJhM2MwOTRmZDFmZDAxN2VjZmYwMDRiY2E0NDFhY2Y0MDJmMzMzNzNjOTRmNmM1YjNiMDBlOGJhNDQ1MDliMzkzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-799253172\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1235332748 data-indent-pad=\"  \"><span class=sf-dump-note>array:30</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">D:\\Topan\\cms-juraganbeku\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">55278</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"30 characters\">PHP/8.3.6 (Development Server)</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"1966 characters\">/administrator/group/menuAccess.json?draw=1&amp;columns%5B0%5D%5Bdata%5D=DT_RowIndex&amp;columns%5B0%5D%5Bname%5D=DT_RowIndex&amp;columns%5B0%5D%5Bsearchable%5D=true&amp;columns%5B0%5D%5Borderable%5D=true&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=name&amp;columns%5B1%5D%5Bname%5D=name&amp;columns%5B1%5D%5Bsearchable%5D=true&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=parent.name&amp;columns%5B2%5D%5Bname%5D=parent&amp;columns%5B2%5D%5Bsearchable%5D=true&amp;columns%5B2%5D%5Borderable%5D=true&amp;columns%5B2%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B2%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B3%5D%5Bdata%5D=code&amp;columns%5B3%5D%5Bname%5D=code&amp;columns%5B3%5D%5Bsearchable%5D=true&amp;columns%5B3%5D%5Borderable%5D=true&amp;columns%5B3%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B3%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B4%5D%5Bdata%5D=&amp;columns%5B4%5D%5Bname%5D=is_viewable&amp;columns%5B4%5D%5Bsearchable%5D=true&amp;columns%5B4%5D%5Borderable%5D=true&amp;columns%5B4%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B4%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B5%5D%5Bdata%5D=&amp;columns%5B5%5D%5Bname%5D=is_addable&amp;columns%5B5%5D%5Bsearchable%5D=true&amp;columns%5B5%5D%5Borderable%5D=true&amp;columns%5B5%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B5%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B6%5D%5Bdata%5D=&amp;columns%5B6%5D%5Bname%5D=is_editable&amp;columns%5B6%5D%5Bsearchable%5D=true&amp;columns%5B6%5D%5Borderable%5D=true&amp;columns%5B6%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B6%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B7%5D%5Bdata%5D=&amp;columns%5B7%5D%5Bname%5D=is_deletable&amp;columns%5B7%5D%5Bsearchable%5D=true&amp;columns%5B7%5D%5Borderable%5D=true&amp;columns%5B7%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B7%5D%5Bsearch%5D%5Bregex%5D=false&amp;order%5B0%5D%5Bcolumn%5D=0&amp;order%5B0%5D%5Bdir%5D=asc&amp;start=0&amp;length=10&amp;search%5Bvalue%5D=&amp;search%5Bregex%5D=false&amp;group_id=ff9759b5-2dc5-410b-b86c-f8146c1f66d8&amp;_=1752049215917</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"36 characters\">/administrator/group/menuAccess.json</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"35 characters\">D:\\Topan\\cms-juraganbeku\\server.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"36 characters\">/administrator/group/menuAccess.json</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"1929 characters\">draw=1&amp;columns%5B0%5D%5Bdata%5D=DT_RowIndex&amp;columns%5B0%5D%5Bname%5D=DT_RowIndex&amp;columns%5B0%5D%5Bsearchable%5D=true&amp;columns%5B0%5D%5Borderable%5D=true&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=name&amp;columns%5B1%5D%5Bname%5D=name&amp;columns%5B1%5D%5Bsearchable%5D=true&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=parent.name&amp;columns%5B2%5D%5Bname%5D=parent&amp;columns%5B2%5D%5Bsearchable%5D=true&amp;columns%5B2%5D%5Borderable%5D=true&amp;columns%5B2%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B2%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B3%5D%5Bdata%5D=code&amp;columns%5B3%5D%5Bname%5D=code&amp;columns%5B3%5D%5Bsearchable%5D=true&amp;columns%5B3%5D%5Borderable%5D=true&amp;columns%5B3%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B3%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B4%5D%5Bdata%5D=&amp;columns%5B4%5D%5Bname%5D=is_viewable&amp;columns%5B4%5D%5Bsearchable%5D=true&amp;columns%5B4%5D%5Borderable%5D=true&amp;columns%5B4%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B4%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B5%5D%5Bdata%5D=&amp;columns%5B5%5D%5Bname%5D=is_addable&amp;columns%5B5%5D%5Bsearchable%5D=true&amp;columns%5B5%5D%5Borderable%5D=true&amp;columns%5B5%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B5%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B6%5D%5Bdata%5D=&amp;columns%5B6%5D%5Bname%5D=is_editable&amp;columns%5B6%5D%5Bsearchable%5D=true&amp;columns%5B6%5D%5Borderable%5D=true&amp;columns%5B6%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B6%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B7%5D%5Bdata%5D=&amp;columns%5B7%5D%5Bname%5D=is_deletable&amp;columns%5B7%5D%5Bsearchable%5D=true&amp;columns%5B7%5D%5Borderable%5D=true&amp;columns%5B7%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B7%5D%5Bsearch%5D%5Bregex%5D=false&amp;order%5B0%5D%5Bcolumn%5D=0&amp;order%5B0%5D%5Bdir%5D=asc&amp;start=0&amp;length=10&amp;search%5Bvalue%5D=&amp;search%5Bregex%5D=false&amp;group_id=ff9759b5-2dc5-410b-b86c-f8146c1f66d8&amp;_=1752049215917</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_REQUESTED_WITH</span>\" => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/administrator/group</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"44 characters\">en-US,en;q=0.9,id;q=0.8,zh-CN;q=0.7,zh;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"808 characters\">_ga=GA1.1.587546091.1749434964; _ga_GBZ3SGGX85=GS2.1.s1751877726$o26$g0$t1751877726$j60$l0$h0; XSRF-TOKEN=eyJpdiI6InlsWmxvZG85ZEVGMStjMDJXNWZpaFE9PSIsInZhbHVlIjoiU1BhUStwamZMaXpZL0hiOU9VTGJmcVZhcVM3Z3NzUVVhd3VhR1g0STNtcHVrcURxdENEOHoyVVZFem8yNWp5S1ZHenVUSmg0Mk5wU3BBd3FBUG0rRTIrN2EwcHNNZ1UwWStMd0l6SktGRG5laGZrRzhNZnBIeDd3aE1keXhhYk0iLCJtYWMiOiIzYzJjYWI5YzQxNTQxZjA5YmQwMzNiMjQxMjU4OTZlNDZhMmQ1NTRkMWEyODFiMDExMjYxZjc5MzdlM2IxMDU1IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ikt4YUd4Q2UyR1ZxTUh1RG9VL2VIT3c9PSIsInZhbHVlIjoieldoSVVBbmp5bzVmdVA4UllWcFNnS3pENTJLSzFRSm1icmRJQ3RXUjNTREJSdXVTMGg1V2tudEZaYUwzaDhxWjJQcTNrQXVnU2NXN3hTQTFkcmxpdUU2V2Evd0o0WS9Kc0o4RkdMU3J6UnRYS0tyMkljUDliRElhWUxwcTdVUG0iLCJtYWMiOiJhM2MwOTRmZDFmZDAxN2VjZmYwMDRiY2E0NDFhY2Y0MDJmMzMzNzNjOTRmNmM1YjNiMDBlOGJhNDQ1MDliMzkzIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1752049245.7312</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1752049245</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1235332748\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1950763720 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_GBZ3SGGX85</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QoZh1I8mypHP2aMUh0DEtCk2CpnpWBdxW5ZtIuuB</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OXEmGaq68dByCoDl0SJP8YEGquktzNtL3aXCzLzt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1950763720\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1523700774 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 09 Jul 2025 08:20:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlN4RWR1NjVvL2dZVmxyVVhJWU00UEE9PSIsInZhbHVlIjoibTYrTGtTS0VkV3JYbm5ham00TWFVSkowQzFHSm81UWFIRmpSelozNysyYlE2cW5YRnRDQ0dibmF1WU81Z0FJQ3lRbzVXaHB4ZTErYXJxbW9mOCtKd25Mc3NKVGcvYzd1cE14a0MvZXN1OEZzYVMrUFJnc2wyMFZlZU9ueUFwTEQiLCJtYWMiOiIwNGU1MmVjMGQ1MWZhNDNlOTFlZDBlYzQ0YzRiYWI5ODdiYjExMTVjNDc0NjgyNmVmNmYyNjQzMmJlOTY0NTk4IiwidGFnIjoiIn0%3D; expires=Wed, 09-Jul-2025 10:20:48 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IkJHVmdtT3Bxb1kwdndoeHFUbGZxWWc9PSIsInZhbHVlIjoicGFHRTNZRDZZVEpacDlPYitVZVFnNUtMSGUvU21QMDFPbExWdVBDWDh0VDB6WVdmRnRUYkxSeFVTL2V4TUlDWllHd2JSa3RQem5SSStldDBLZWhwUEQwSzFLZWlrNlQ0UEN4ZXZyNldtS0hWQWNGS21OVXVUWnBxa0JOV293dTkiLCJtYWMiOiIyM2UxMWRlMjlhNWE2MjExN2JiNTcyODE4ZGI3NTVkNGQ5ZDBhMTU1ZTU3ZmUzMzZmODIwMmIwY2M5MTg0MjU0IiwidGFnIjoiIn0%3D; expires=Wed, 09-Jul-2025 10:20:48 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlN4RWR1NjVvL2dZVmxyVVhJWU00UEE9PSIsInZhbHVlIjoibTYrTGtTS0VkV3JYbm5ham00TWFVSkowQzFHSm81UWFIRmpSelozNysyYlE2cW5YRnRDQ0dibmF1WU81Z0FJQ3lRbzVXaHB4ZTErYXJxbW9mOCtKd25Mc3NKVGcvYzd1cE14a0MvZXN1OEZzYVMrUFJnc2wyMFZlZU9ueUFwTEQiLCJtYWMiOiIwNGU1MmVjMGQ1MWZhNDNlOTFlZDBlYzQ0YzRiYWI5ODdiYjExMTVjNDc0NjgyNmVmNmYyNjQzMmJlOTY0NTk4IiwidGFnIjoiIn0%3D; expires=Wed, 09-Jul-2025 10:20:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IkJHVmdtT3Bxb1kwdndoeHFUbGZxWWc9PSIsInZhbHVlIjoicGFHRTNZRDZZVEpacDlPYitVZVFnNUtMSGUvU21QMDFPbExWdVBDWDh0VDB6WVdmRnRUYkxSeFVTL2V4TUlDWllHd2JSa3RQem5SSStldDBLZWhwUEQwSzFLZWlrNlQ0UEN4ZXZyNldtS0hWQWNGS21OVXVUWnBxa0JOV293dTkiLCJtYWMiOiIyM2UxMWRlMjlhNWE2MjExN2JiNTcyODE4ZGI3NTVkNGQ5ZDBhMTU1ZTU3ZmUzMzZmODIwMmIwY2M5MTg0MjU0IiwidGFnIjoiIn0%3D; expires=Wed, 09-Jul-2025 10:20:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1523700774\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1017214482 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QoZh1I8mypHP2aMUh0DEtCk2CpnpWBdxW5ZtIuuB</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/administrator/group</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"36 characters\">c065dc93-6b2f-4bf5-b746-6bb48713d4bf</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1017214482\", {\"maxDepth\":0})</script>\n"}}