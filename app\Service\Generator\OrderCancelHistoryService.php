<?php

/**
 * <AUTHOR>
 */

namespace App\Service\Generator;


use App\Models\Generator\OrderCancelHistory;
use App\Repository\Generator\OrderCancelHistoryRepository;
use Illuminate\Support\Facades\Validator;
use App\Service\CoreService;
use Yajra\DataTables\Facades\DataTables;

class OrderCancelHistoryService extends CoreService
{
    protected $ordercancelhistoryRepository;

    public function __construct(OrderCancelHistoryRepository $ordercancelhistoryRepository)
    {
        $this->ordercancelhistoryRepository = $ordercancelhistoryRepository;
    }

    public function formValidate($request)
    {
        $rules = [
            //            'email' => 'required|min:1|unique:conf_users,email,NULL,id,deleted_at,NULL'
        ];
        $messages = [
            'email.unique' => 'Email sudah terdaftar.',
        ];
        $validator = Validator::make($request, $rules, $messages);

        if ($validator->fails()) {
            return [
                'status' => 'error',
                'message' => $messages
            ];
        }
        return 0;
    }

    public function all()
    {
        return $this->ordercancelhistoryRepository->all();
    }

    public function find($id, $relation = null)
    {
        return $this->ordercancelhistoryRepository->find($id, $relation);
    }

    public function loadDataTable($access, $filter = null)
    {
        $model = OrderCancelHistory::withoutTrashed()->with(['order', 'user', 'user.group'])->orderBy('created_at', 'desc');
        // dd($model->get());

        if (isset($filter['no_order'])) {
            // Only filter by order code and reseller name
            $model->whereHas('order', function ($query) use ($filter) {
                $query->where('order_code', $filter['no_order']);
            });
        }

        $data = DataTables::of($model)->addIndexColumn()

            ->addColumn('action', function ($model) use ($access) {
                $delete_btn = '';
                $update_btn = '';
                $view_btn = '';

                if ($access->is_viewable == true) {
                    $view_btn = "<button class='btn btn-icon btn-info btn-glow mr-1 mb-1 view'
                                     data-toggle='tooltip' data-placement='top' title='View Data' id='view' data-id='$model->id' data-file='$model->file' data-upload='$model->created_at'>
                                        <i class='la la-eye'  style='font-size: 2vh !important;'></i>
                                </button>";
                }
                if ($access->is_editable == true) {
                    //                    $url = route('dashboard_products_edit', ['id' => $model->id]);
                    $update_btn = "<a class='btn btn-icon btn-warning btn-glow mr-1 mb-1 update'
                                     data-toggle='tooltip' data-placement='top' title='Edit Data' data-name='$model->name' data-id='$model->id'>
                                        <i class='la la-edit' style='font-size: 2vh !important;'></i>
                                   </a>";
                }


                $action = $view_btn . $update_btn;
                return $action;
            })
            ->make(true);
        return $data;
        // return $this->privilageBtnDatatable($model, $access);
    }
}
