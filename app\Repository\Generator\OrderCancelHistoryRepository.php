<?php

/**
 * <AUTHOR>
 */

namespace App\Repository\Generator;

use App\Models\Generator\OrderCancelHistory;
use App\Service\Generator\OrderCancelHistoryService;
use App\Repository\CoreRepository;

class OrderCancelHistoryRepository extends CoreRepository
{
    protected $ordercancelhistory;

    public function __construct(OrderCancelHistory $ordercancelhistory)
    {
        $this->setModel($ordercancelhistory);
        $this->ordercancelhistory = $ordercancelhistory;
    }

    public function findWith($id, $relation)
    {
        return $this->ordercancelhistory->with("$relation")->find($id);
    }

    public function get_all()
    {
        return $this->ordercancelhistory->withTrashed()->get();
    }

    public function dataTable($access, $filter = null)
    {
        $data = new OrderCancelHistoryService($this);
        return $data->loadDataTable($access, $filter);
    }
}
