{"__meta": {"id": "Xa9e7e1cdfe807b2a53bee33ff7cd2198", "datetime": "2025-07-09 14:58:15", "utime": 1752047895.576638, "method": "GET", "uri": "/login", "ip": "127.0.0.1"}, "php": {"version": "8.3.6", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[14:58:14] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1752047894.265392, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1752047893.618517, "end": 1752047895.576701, "duration": 1.958184003829956, "duration_str": "1.96s", "measures": [{"label": "Booting", "start": 1752047893.618517, "relative_start": 0, "end": 1752047894.205582, "relative_end": 1752047894.205582, "duration": 0.5870649814605713, "duration_str": "587ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1752047894.205604, "relative_start": 0.5870871543884277, "end": 1752047895.576706, "relative_end": 5.0067901611328125e-06, "duration": 1.3711018562316895, "duration_str": "1.37s", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 23421240, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "auth.login (\\resources\\views\\auth\\login.blade.php)", "param_count": 2, "params": ["setting", "message"], "type": "blade"}]}, "route": {"uri": "GET login", "middleware": "web", "controller": "App\\Http\\Controllers\\Auth\\LoginController@showLoginForm", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=D:\\Topan\\cms-juraganbeku\\app\\Http\\Controllers\\Auth\\LoginController.php&line=66\">\\app\\Http\\Controllers\\Auth\\LoginController.php:66-73</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.0582, "accumulated_duration_str": "58.2ms", "statements": [{"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'juragan_beku' and table_name = 'conf_setting'", "type": "query", "params": [], "bindings": ["juragan_beku", "conf_setting"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php", "line": 33}, {"index": 12, "namespace": null, "name": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php", "line": 16}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.01222, "duration_str": "12.22ms", "stmt_id": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php:33", "connection": "juragan_beku", "start_percent": 0, "width_percent": 20.997}, {"sql": "select * from `conf_setting` where `conf_setting`.`deleted_at` is null and (`parameter` = 'super_password') limit 1", "type": "query", "params": [], "bindings": ["super_password"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repository\\CoreRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\Auth\\LoginController.php", "line": 60}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.04056, "duration_str": "40.56ms", "stmt_id": "\\app\\Repository\\CoreRepository.php:34", "connection": "juragan_beku", "start_percent": 20.997, "width_percent": 69.691}, {"sql": "select * from `conf_setting` where `conf_setting`.`deleted_at` is null and (`parameter` = 'logo') limit 1", "type": "query", "params": [], "bindings": ["logo"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repository\\CoreRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\Auth\\LoginController.php", "line": 61}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.0020299999999999997, "duration_str": "2.03ms", "stmt_id": "\\app\\Repository\\CoreRepository.php:34", "connection": "juragan_beku", "start_percent": 90.687, "width_percent": 3.488}, {"sql": "select * from `conf_setting` where `conf_setting`.`deleted_at` is null and (`parameter` = 'app_name') limit 1", "type": "query", "params": [], "bindings": ["app_name"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repository\\CoreRepository.php", "line": 34}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\Auth\\LoginController.php", "line": 62}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.0015300000000000001, "duration_str": "1.53ms", "stmt_id": "\\app\\Repository\\CoreRepository.php:34", "connection": "juragan_beku", "start_percent": 94.175, "width_percent": 2.629}, {"sql": "select * from `conf_setting` where `conf_setting`.`deleted_at` is null and `parameter` in ('logo', 'app_name', 'app_name_short', 'footer', 'logo_icon')", "type": "query", "params": [], "bindings": ["logo", "app_name", "app_name_short", "footer", "logo_icon"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Repository\\SettingRepository.php", "line": 41}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Auth\\LoginController.php", "line": 63}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00186, "duration_str": "1.86ms", "stmt_id": "\\app\\Repository\\SettingRepository.php:41", "connection": "juragan_beku", "start_percent": 96.804, "width_percent": 3.196}]}, "models": {"data": {"App\\Models\\Setting": 6}, "count": 6}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "uqU8XcYdjZEChvw0H37yWr6HFtCneckJnENtrf9H", "url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-891695676 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-891695676\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-549792562 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-549792562\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1313221827 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1313221827\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-529540168 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">en-US,en;q=0.9,id;q=0.8,zh-CN;q=0.7,zh;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"808 characters\">_ga=GA1.1.587546091.1749434964; _ga_GBZ3SGGX85=GS2.1.s1751877726$o26$g0$t1751877726$j60$l0$h0; XSRF-TOKEN=eyJpdiI6IkFaNDdTTEc5ZGd5ckN4V0pZcC93MlE9PSIsInZhbHVlIjoiOS9CSWttU2VCNy92VkNOY01iMzRlR3pJbjFBOEZ5OVAwZVhkbVdzMjAyVWsybmRpdTlyaXJ0dWp0ZmdGV3Q0UWwwTlVSdVlOMDFXSWxZcGg0OWRYdHJVR240b2tzamVRRS9LekRnNnNJS0tCS1NNSll5Rmdhenp3czZMZXdPUEEiLCJtYWMiOiI5YzNiYzNlOWU5NThlMTViZDFlYTFhNzg1YWNlMDc3N2ZjNzBjMGE5ZmJlZTlkYWRjYWIwNzc4Yzc5ZGJlZDJjIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InMraTFWVC85bTVFRnQ1QjBwc3N2cHc9PSIsInZhbHVlIjoiZ1poS3VVbm4zUDRlclpFYjlLN0JoSXZ3WTJndWJPRXRxZTkwTHJFZjhjSmtaNlJNdXZSTDFIR1NPUEJESnpUb25rc1doRTcxQWxxblBZN3NWL2R1M2t4Q3UwSzljVGlNMHExUVdzN2pOU2FlRDhTNkF5cU5FcU5OY0pQQUxTUmIiLCJtYWMiOiIxMDc1MGRmN2Q3MTZiZjc4ODYwOTVmMjA0ODI0ZTZmMzY1OWZmNWQwNDBlNDNjOTljN2U1ZTIyZTAyZGE4OTYzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-529540168\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1676341006 data-indent-pad=\"  \"><span class=sf-dump-note>array:30</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">D:\\Topan\\cms-juraganbeku\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">62250</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"30 characters\">PHP/8.3.6 (Development Server)</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"6 characters\">/login</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"41 characters\">D:\\Topan\\cms-juraganbeku\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"6 characters\">/login</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"16 characters\">/index.php/login</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"44 characters\">en-US,en;q=0.9,id;q=0.8,zh-CN;q=0.7,zh;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"808 characters\">_ga=GA1.1.587546091.1749434964; _ga_GBZ3SGGX85=GS2.1.s1751877726$o26$g0$t1751877726$j60$l0$h0; XSRF-TOKEN=eyJpdiI6IkFaNDdTTEc5ZGd5ckN4V0pZcC93MlE9PSIsInZhbHVlIjoiOS9CSWttU2VCNy92VkNOY01iMzRlR3pJbjFBOEZ5OVAwZVhkbVdzMjAyVWsybmRpdTlyaXJ0dWp0ZmdGV3Q0UWwwTlVSdVlOMDFXSWxZcGg0OWRYdHJVR240b2tzamVRRS9LekRnNnNJS0tCS1NNSll5Rmdhenp3czZMZXdPUEEiLCJtYWMiOiI5YzNiYzNlOWU5NThlMTViZDFlYTFhNzg1YWNlMDc3N2ZjNzBjMGE5ZmJlZTlkYWRjYWIwNzc4Yzc5ZGJlZDJjIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InMraTFWVC85bTVFRnQ1QjBwc3N2cHc9PSIsInZhbHVlIjoiZ1poS3VVbm4zUDRlclpFYjlLN0JoSXZ3WTJndWJPRXRxZTkwTHJFZjhjSmtaNlJNdXZSTDFIR1NPUEJESnpUb25rc1doRTcxQWxxblBZN3NWL2R1M2t4Q3UwSzljVGlNMHExUVdzN2pOU2FlRDhTNkF5cU5FcU5OY0pQQUxTUmIiLCJtYWMiOiIxMDc1MGRmN2Q3MTZiZjc4ODYwOTVmMjA0ODI0ZTZmMzY1OWZmNWQwNDBlNDNjOTljN2U1ZTIyZTAyZGE4OTYzIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1752047893.6185</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1752047893</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1676341006\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-904865749 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_GBZ3SGGX85</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">uqU8XcYdjZEChvw0H37yWr6HFtCneckJnENtrf9H</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">126wG4hebAiaQyBxiFLVTw3M3rvMyMRahdhWTtrw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-904865749\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1929382012 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 09 Jul 2025 07:58:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkEvYkM4eFBWUzZTblh4S3IxR2ZOTUE9PSIsInZhbHVlIjoiUG1vZzgyU3dicUJuUnYvR1hWR1gzWGw2bnZYZ3QvVnBZQU14TDFzREVvQmVqdk5TZ3l3bGNxL252RXdlWlByZjMzcGFURzNLVG5pWUFua08wd0lVc2x3V1NKRlgrdENLaG00VW9pdy9uNldDRmowZE0zZEFsSmw4eTRvd20wazkiLCJtYWMiOiIyZWIyNDliOTFlOTVjY2E5ZWViZDUzODMwMGJjMjE4YjE3YzJiZmU2NDc5MGVjMzBlY2M4NTUyZDAwYzFiYjlmIiwidGFnIjoiIn0%3D; expires=Wed, 09-Jul-2025 09:58:15 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6InUxSTlYSnNYU3hQYVdXSXlWMEFmTnc9PSIsInZhbHVlIjoiTjRERHgwQTNETEJnQjN3MjViWS91RmlnZVlSTUI1K29GeVQzMldlUC9aaXJEVUZyWmM1UzNMQVBsWThDVmFOVTVaU3lvWlhZajZLdGRZUTZhdEhySXBUb2h2RDVyMWtidFlxZzJVN3NJTDRNdndqd0FwSE9TT2grbHBlemVDY28iLCJtYWMiOiI1MzM5ZDEyNzcwYTk3YTljNmU4MDk4NmZhY2M3NTY2YWVjNGVhMTk2ZGFlYWUyYmU4ODI4ZTY5MWYzMTQ3MzIwIiwidGFnIjoiIn0%3D; expires=Wed, 09-Jul-2025 09:58:15 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkEvYkM4eFBWUzZTblh4S3IxR2ZOTUE9PSIsInZhbHVlIjoiUG1vZzgyU3dicUJuUnYvR1hWR1gzWGw2bnZYZ3QvVnBZQU14TDFzREVvQmVqdk5TZ3l3bGNxL252RXdlWlByZjMzcGFURzNLVG5pWUFua08wd0lVc2x3V1NKRlgrdENLaG00VW9pdy9uNldDRmowZE0zZEFsSmw4eTRvd20wazkiLCJtYWMiOiIyZWIyNDliOTFlOTVjY2E5ZWViZDUzODMwMGJjMjE4YjE3YzJiZmU2NDc5MGVjMzBlY2M4NTUyZDAwYzFiYjlmIiwidGFnIjoiIn0%3D; expires=Wed, 09-Jul-2025 09:58:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6InUxSTlYSnNYU3hQYVdXSXlWMEFmTnc9PSIsInZhbHVlIjoiTjRERHgwQTNETEJnQjN3MjViWS91RmlnZVlSTUI1K29GeVQzMldlUC9aaXJEVUZyWmM1UzNMQVBsWThDVmFOVTVaU3lvWlhZajZLdGRZUTZhdEhySXBUb2h2RDVyMWtidFlxZzJVN3NJTDRNdndqd0FwSE9TT2grbHBlemVDY28iLCJtYWMiOiI1MzM5ZDEyNzcwYTk3YTljNmU4MDk4NmZhY2M3NTY2YWVjNGVhMTk2ZGFlYWUyYmU4ODI4ZTY5MWYzMTQ3MzIwIiwidGFnIjoiIn0%3D; expires=Wed, 09-Jul-2025 09:58:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1929382012\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1735313500 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">uqU8XcYdjZEChvw0H37yWr6HFtCneckJnENtrf9H</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1735313500\", {\"maxDepth\":0})</script>\n"}}