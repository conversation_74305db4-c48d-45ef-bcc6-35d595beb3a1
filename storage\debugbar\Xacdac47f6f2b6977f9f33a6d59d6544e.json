{"__meta": {"id": "Xacdac47f6f2b6977f9f33a6d59d6544e", "datetime": "2025-07-09 15:33:09", "utime": 1752049989.23602, "method": "GET", "uri": "/administrator/ordercancelhistories/datatable.json?draw=1&columns%5B0%5D%5Bdata%5D=DT_RowIndex&columns%5B0%5D%5Bname%5D=id&columns%5B0%5D%5Bsearchable%5D=true&columns%5B0%5D%5Borderable%5D=true&columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B1%5D%5Bdata%5D=order.order_code&columns%5B1%5D%5Bname%5D=order.order_code&columns%5B1%5D%5Bsearchable%5D=false&columns%5B1%5D%5Borderable%5D=true&columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B2%5D%5Bdata%5D=reason&columns%5B2%5D%5Bname%5D=reason&columns%5B2%5D%5Bsearchable%5D=false&columns%5B2%5D%5Borderable%5D=true&columns%5B2%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B2%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B3%5D%5Bdata%5D=user.fullname&columns%5B3%5D%5Bname%5D=user.fullname&columns%5B3%5D%5Bsearchable%5D=false&columns%5B3%5D%5Borderable%5D=true&columns%5B3%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B3%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B4%5D%5Bdata%5D=created_at&columns%5B4%5D%5Bname%5D=created_at&columns%5B4%5D%5Bsearchable%5D=false&columns%5B4%5D%5Borderable%5D=true&columns%5B4%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B4%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B5%5D%5Bdata%5D=action&columns%5B5%5D%5Bname%5D=action&columns%5B5%5D%5Bsearchable%5D=false&columns%5B5%5D%5Borderable%5D=false&columns%5B5%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B5%5D%5Bsearch%5D%5Bregex%5D=false&order%5B0%5D%5Bcolumn%5D=0&order%5B0%5D%5Bdir%5D=asc&start=0&length=10&search%5Bvalue%5D=&search%5Bregex%5D=false&no_order=20185&_=1752049867294", "ip": "127.0.0.1"}, "php": {"version": "8.3.6", "interface": "cli-server"}, "messages": {"count": 32, "messages": [{"message": "[15:33:08] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1752049988.527455, "xdebug_link": null, "collector": "log"}, {"message": "[15:33:08] LOG.warning: Creation of dynamic property App\\Http\\Controllers\\Generator\\OrderCancelHistoryController::$user is deprecated in D:\\Topan\\cms-juraganbeku\\app\\Http\\Controllers\\CoreController.php on line 56", "message_html": null, "is_string": false, "label": "warning", "time": 1752049988.88004, "xdebug_link": null, "collector": "log"}, {"message": "[15:33:09] LOG.warning: Creation of dynamic property Yajra\\DataTables\\Processors\\DataProcessor::$onlyColumns is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\Processors\\DataProcessor.php on line 83", "message_html": null, "is_string": false, "label": "warning", "time": 1752049989.217551, "xdebug_link": null, "collector": "log"}, {"message": "[15:33:09] LOG.warning: Creation of dynamic property Yajra\\DataTables\\Processors\\DataProcessor::$makeHidden is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\Processors\\DataProcessor.php on line 87", "message_html": null, "is_string": false, "label": "warning", "time": 1752049989.217617, "xdebug_link": null, "collector": "log"}, {"message": "[15:33:09] LOG.warning: Creation of dynamic property Yajra\\DataTables\\Processors\\DataProcessor::$makeVisible is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\Processors\\DataProcessor.php on line 88", "message_html": null, "is_string": false, "label": "warning", "time": 1752049989.217675, "xdebug_link": null, "collector": "log"}, {"message": "[15:33:09] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049989.227541, "xdebug_link": null, "collector": "log"}, {"message": "[15:33:09] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049989.227575, "xdebug_link": null, "collector": "log"}, {"message": "[15:33:09] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049989.227604, "xdebug_link": null, "collector": "log"}, {"message": "[15:33:09] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049989.22763, "xdebug_link": null, "collector": "log"}, {"message": "[15:33:09] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049989.227658, "xdebug_link": null, "collector": "log"}, {"message": "[15:33:09] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049989.227684, "xdebug_link": null, "collector": "log"}, {"message": "[15:33:09] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049989.227711, "xdebug_link": null, "collector": "log"}, {"message": "[15:33:09] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049989.227736, "xdebug_link": null, "collector": "log"}, {"message": "[15:33:09] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049989.227762, "xdebug_link": null, "collector": "log"}, {"message": "[15:33:09] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049989.227788, "xdebug_link": null, "collector": "log"}, {"message": "[15:33:09] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049989.227814, "xdebug_link": null, "collector": "log"}, {"message": "[15:33:09] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049989.227853, "xdebug_link": null, "collector": "log"}, {"message": "[15:33:09] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049989.22788, "xdebug_link": null, "collector": "log"}, {"message": "[15:33:09] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049989.227906, "xdebug_link": null, "collector": "log"}, {"message": "[15:33:09] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049989.227934, "xdebug_link": null, "collector": "log"}, {"message": "[15:33:09] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049989.227972, "xdebug_link": null, "collector": "log"}, {"message": "[15:33:09] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049989.227999, "xdebug_link": null, "collector": "log"}, {"message": "[15:33:09] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049989.228025, "xdebug_link": null, "collector": "log"}, {"message": "[15:33:09] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049989.228051, "xdebug_link": null, "collector": "log"}, {"message": "[15:33:09] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049989.228081, "xdebug_link": null, "collector": "log"}, {"message": "[15:33:09] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049989.228118, "xdebug_link": null, "collector": "log"}, {"message": "[15:33:09] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049989.228145, "xdebug_link": null, "collector": "log"}, {"message": "[15:33:09] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049989.228172, "xdebug_link": null, "collector": "log"}, {"message": "[15:33:09] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049989.228198, "xdebug_link": null, "collector": "log"}, {"message": "[15:33:09] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049989.228224, "xdebug_link": null, "collector": "log"}, {"message": "[15:33:09] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049989.22825, "xdebug_link": null, "collector": "log"}, {"message": "[15:33:09] LOG.warning: Function utf8_encode() is deprecated in D:\\Topan\\cms-juraganbeku\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php on line 774", "message_html": null, "is_string": false, "label": "warning", "time": 1752049989.228279, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1752049988.281115, "end": 1752049989.236072, "duration": 0.9549570083618164, "duration_str": "955ms", "measures": [{"label": "Booting", "start": 1752049988.281115, "relative_start": 0, "end": 1752049988.506237, "relative_end": 1752049988.506237, "duration": 0.22512197494506836, "duration_str": "225ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1752049988.506249, "relative_start": 0.22513389587402344, "end": 1752049989.236075, "relative_end": 2.86102294921875e-06, "duration": 0.7298259735107422, "duration_str": "730ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 49947464, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET administrator/ordercancelhistories/datatable.json", "middleware": "web, auth, roles", "controller": "App\\Http\\Controllers\\Generator\\OrderCancelHistoryController@__datatable", "namespace": null, "prefix": "administrator/ordercancelhistories", "where": [], "as": "dashboard_ordercancelhistories_table", "file": "<a href=\"phpstorm://open?file=D:\\Topan\\cms-juraganbeku\\app\\Http\\Controllers\\Generator\\OrderCancelHistoryController.php&line=90\">\\app\\Http\\Controllers\\Generator\\OrderCancelHistoryController.php:90-93</a>"}, "queries": {"nb_statements": 47, "nb_failed_statements": 0, "accumulated_duration": 0.3322800000000001, "accumulated_duration_str": "332ms", "statements": [{"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'juragan_beku' and table_name = 'tbl_order_cancel'", "type": "query", "params": [], "bindings": ["juragan_beku", "tbl_order_cancel"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php", "line": 33}, {"index": 12, "namespace": null, "name": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php", "line": 16}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.024239999999999998, "duration_str": "24.24ms", "stmt_id": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php:33", "connection": "juragan_beku", "start_percent": 0, "width_percent": 7.295}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'juragan_beku' and table_name = 'conf_menu'", "type": "query", "params": [], "bindings": ["juragan_beku", "conf_menu"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php", "line": 33}, {"index": 12, "namespace": null, "name": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php", "line": 16}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\CoreController.php", "line": 61}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\Generator\\OrderCancelHistoryController.php", "line": 27}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}], "duration": 0.00252, "duration_str": "2.52ms", "stmt_id": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php:33", "connection": "juragan_beku", "start_percent": 7.295, "width_percent": 0.758}, {"sql": "select * from `conf_menu` where `parent_id` is null order by `menu_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\CoreController.php", "line": 61}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Generator\\OrderCancelHistoryController.php", "line": 27}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00159, "duration_str": "1.59ms", "stmt_id": "\\app\\Http\\Controllers\\CoreController.php:61", "connection": "juragan_beku", "start_percent": 8.053, "width_percent": 0.479}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'juragan_beku' and table_name = 'conf_group'", "type": "query", "params": [], "bindings": ["juragan_beku", "conf_group"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php", "line": 33}, {"index": 12, "namespace": null, "name": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php", "line": 16}, {"index": 20, "namespace": null, "name": "\\app\\Models\\Menu.php", "line": 39}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\CoreController.php", "line": 61}, {"index": 28, "namespace": null, "name": "\\app\\Http\\Controllers\\Generator\\OrderCancelHistoryController.php", "line": 27}], "duration": 0.00159, "duration_str": "1.59ms", "stmt_id": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php:33", "connection": "juragan_beku", "start_percent": 8.532, "width_percent": 0.479}, {"sql": "select `conf_group`.*, `conf_group_menu`.`menu_id` as `pivot_menu_id`, `conf_group_menu`.`group_id` as `pivot_group_id`, `conf_group_menu`.`id` as `pivot_id`, `conf_group_menu`.`is_addable` as `pivot_is_addable`, `conf_group_menu`.`is_editable` as `pivot_is_editable`, `conf_group_menu`.`is_deletable` as `pivot_is_deletable`, `conf_group_menu`.`is_viewable` as `pivot_is_viewable`, `conf_group_menu`.`created_at` as `pivot_created_at`, `conf_group_menu`.`updated_at` as `pivot_updated_at` from `conf_group` inner join `conf_group_menu` on `conf_group`.`id` = `conf_group_menu`.`group_id` where `conf_group_menu`.`deleted_at` is null and `conf_group_menu`.`menu_id` in ('12172b03-c059-47f6-8b3a-ec45fff0bfb0', '1e171ae1-4f20-47b8-898c-a3d7028a3ab8', '2412018e-b714-4252-8674-1f7ced3b4c8e', '2afdc4cd-6699-4fdb-8774-02fd3c52c7a9', '35bdff65-5393-4c72-a885-1026cdfdaed6', '3629349a-e969-47f6-ba61-2d75c110dba7', '3927daac-5667-4d64-9c1e-ba804d715ad6', '42ecc46f-d988-4079-8fcc-e80f086ae0b8', '6d3265c5-86a7-4f93-a5e5-dbacf271bf08', '6d34fee1-1985-4885-863d-a7f34134c662', '6e655b95-8adb-45e6-9bad-aedb2917aa95', '6eb47bc6-8aa1-4548-965e-b17280f8265e', '710fd92b-bb27-4596-96a1-cc4cc9d4da53', '7afebf9b-18a3-4663-945b-cbba8403c5e5', '7c8ba58d-5450-4607-859c-152617371562', '89c3ba3a-beca-4eaf-b825-9df62b18f100', '8b6c271e-5599-4599-a037-6f36cbaf5df4', '908d7755-397d-459a-8dd9-b5c1cf2933a1', '95a301cc-df41-4af8-a735-42a88252cc28', '9775dc51-db4d-4ff3-a0eb-bc02582b2519', '9963370e-03d2-47fc-bf10-d8afb45f2778', 'a663bdd0-dd56-435c-b01c-56914d355319', 'a858dc43-a5b2-47ac-8b19-2122748997d7', 'a99e9853-75af-41ad-ab42-e367c434342e', 'ab6c7821-03a9-4d1e-b62d-4aa1e75ac2bc', 'b1ce929b-2658-464d-8423-f83025eba65a', 'b6ae8a73-8645-411b-9f3a-664ca8d0d0fb', 'b88aa02c-a949-490e-82dd-dd99fde4384d', 'ba1d3b8c-b6e1-4a5c-b0ba-9fcb73895e79', 'd302114a-df52-4183-9416-17beeb3ed264', 'f8a46c4d-7463-464c-abd5-a2187680201c', 'fb06a851-d01d-4cfd-a738-fcbc90b2ea43', 'fb962710-d5f6-4ba6-aeb4-ef7a749091ed', 'fc77a5a4-0e46-4650-8ba9-a1b32a449466') and `conf_group`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["12172b03-c059-47f6-8b3a-ec45fff0bfb0", "1e171ae1-4f20-47b8-898c-a3d7028a3ab8", "2412018e-b714-4252-8674-1f7ced3b4c8e", "2afdc4cd-6699-4fdb-8774-02fd3c52c7a9", "35bdff65-5393-4c72-a885-1026cdfdaed6", "3629349a-e969-47f6-ba61-2d75c110dba7", "3927daac-5667-4d64-9c1e-ba804d715ad6", "42ecc46f-d988-4079-8fcc-e80f086ae0b8", "6d3265c5-86a7-4f93-a5e5-dbacf271bf08", "6d34fee1-1985-4885-863d-a7f34134c662", "6e655b95-8adb-45e6-9bad-aedb2917aa95", "6eb47bc6-8aa1-4548-965e-b17280f8265e", "710fd92b-bb27-4596-96a1-cc4cc9d4da53", "7afebf9b-18a3-4663-945b-cbba8403c5e5", "7c8ba58d-5450-4607-859c-152617371562", "89c3ba3a-beca-4eaf-b825-9df62b18f100", "8b6c271e-5599-4599-a037-6f36cbaf5df4", "908d7755-397d-459a-8dd9-b5c1cf2933a1", "95a301cc-df41-4af8-a735-42a88252cc28", "9775dc51-db4d-4ff3-a0eb-bc02582b2519", "9963370e-03d2-47fc-bf10-d8afb45f2778", "a663bdd0-dd56-435c-b01c-56914d355319", "a858dc43-a5b2-47ac-8b19-2122748997d7", "a99e9853-75af-41ad-ab42-e367c434342e", "ab6c7821-03a9-4d1e-b62d-4aa1e75ac2bc", "b1ce929b-2658-464d-8423-f83025eba65a", "b6ae8a73-8645-411b-9f3a-664ca8d0d0fb", "b88aa02c-a949-490e-82dd-dd99fde4384d", "ba1d3b8c-b6e1-4a5c-b0ba-9fcb73895e79", "d302114a-df52-4183-9416-17beeb3ed264", "f8a46c4d-7463-464c-abd5-a2187680201c", "fb06a851-d01d-4cfd-a738-fcbc90b2ea43", "fb962710-d5f6-4ba6-aeb4-ef7a749091ed", "fc77a5a4-0e46-4650-8ba9-a1b32a449466"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "\\app\\Http\\Controllers\\CoreController.php", "line": 61}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\Generator\\OrderCancelHistoryController.php", "line": 27}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.0148, "duration_str": "14.8ms", "stmt_id": "\\app\\Http\\Controllers\\CoreController.php:61", "connection": "juragan_beku", "start_percent": 9.01, "width_percent": 4.454}, {"sql": "select * from `conf_menu` where `conf_menu`.`parent_id` in ('12172b03-c059-47f6-8b3a-ec45fff0bfb0', '1e171ae1-4f20-47b8-898c-a3d7028a3ab8', '2412018e-b714-4252-8674-1f7ced3b4c8e', '2afdc4cd-6699-4fdb-8774-02fd3c52c7a9', '35bdff65-5393-4c72-a885-1026cdfdaed6', '3629349a-e969-47f6-ba61-2d75c110dba7', '3927daac-5667-4d64-9c1e-ba804d715ad6', '42ecc46f-d988-4079-8fcc-e80f086ae0b8', '6d3265c5-86a7-4f93-a5e5-dbacf271bf08', '6d34fee1-1985-4885-863d-a7f34134c662', '6e655b95-8adb-45e6-9bad-aedb2917aa95', '6eb47bc6-8aa1-4548-965e-b17280f8265e', '710fd92b-bb27-4596-96a1-cc4cc9d4da53', '7afebf9b-18a3-4663-945b-cbba8403c5e5', '7c8ba58d-5450-4607-859c-152617371562', '89c3ba3a-beca-4eaf-b825-9df62b18f100', '8b6c271e-5599-4599-a037-6f36cbaf5df4', '908d7755-397d-459a-8dd9-b5c1cf2933a1', '95a301cc-df41-4af8-a735-42a88252cc28', '9775dc51-db4d-4ff3-a0eb-bc02582b2519', '9963370e-03d2-47fc-bf10-d8afb45f2778', 'a663bdd0-dd56-435c-b01c-56914d355319', 'a858dc43-a5b2-47ac-8b19-2122748997d7', 'a99e9853-75af-41ad-ab42-e367c434342e', 'ab6c7821-03a9-4d1e-b62d-4aa1e75ac2bc', 'b1ce929b-2658-464d-8423-f83025eba65a', 'b6ae8a73-8645-411b-9f3a-664ca8d0d0fb', 'b88aa02c-a949-490e-82dd-dd99fde4384d', 'ba1d3b8c-b6e1-4a5c-b0ba-9fcb73895e79', 'd302114a-df52-4183-9416-17beeb3ed264', 'f8a46c4d-7463-464c-abd5-a2187680201c', 'fb06a851-d01d-4cfd-a738-fcbc90b2ea43', 'fb962710-d5f6-4ba6-aeb4-ef7a749091ed', 'fc77a5a4-0e46-4650-8ba9-a1b32a449466') and `conf_menu`.`deleted_at` is null order by `menu_order` asc", "type": "query", "params": [], "bindings": ["12172b03-c059-47f6-8b3a-ec45fff0bfb0", "1e171ae1-4f20-47b8-898c-a3d7028a3ab8", "2412018e-b714-4252-8674-1f7ced3b4c8e", "2afdc4cd-6699-4fdb-8774-02fd3c52c7a9", "35bdff65-5393-4c72-a885-1026cdfdaed6", "3629349a-e969-47f6-ba61-2d75c110dba7", "3927daac-5667-4d64-9c1e-ba804d715ad6", "42ecc46f-d988-4079-8fcc-e80f086ae0b8", "6d3265c5-86a7-4f93-a5e5-dbacf271bf08", "6d34fee1-1985-4885-863d-a7f34134c662", "6e655b95-8adb-45e6-9bad-aedb2917aa95", "6eb47bc6-8aa1-4548-965e-b17280f8265e", "710fd92b-bb27-4596-96a1-cc4cc9d4da53", "7afebf9b-18a3-4663-945b-cbba8403c5e5", "7c8ba58d-5450-4607-859c-152617371562", "89c3ba3a-beca-4eaf-b825-9df62b18f100", "8b6c271e-5599-4599-a037-6f36cbaf5df4", "908d7755-397d-459a-8dd9-b5c1cf2933a1", "95a301cc-df41-4af8-a735-42a88252cc28", "9775dc51-db4d-4ff3-a0eb-bc02582b2519", "9963370e-03d2-47fc-bf10-d8afb45f2778", "a663bdd0-dd56-435c-b01c-56914d355319", "a858dc43-a5b2-47ac-8b19-2122748997d7", "a99e9853-75af-41ad-ab42-e367c434342e", "ab6c7821-03a9-4d1e-b62d-4aa1e75ac2bc", "b1ce929b-2658-464d-8423-f83025eba65a", "b6ae8a73-8645-411b-9f3a-664ca8d0d0fb", "b88aa02c-a949-490e-82dd-dd99fde4384d", "ba1d3b8c-b6e1-4a5c-b0ba-9fcb73895e79", "d302114a-df52-4183-9416-17beeb3ed264", "f8a46c4d-7463-464c-abd5-a2187680201c", "fb06a851-d01d-4cfd-a738-fcbc90b2ea43", "fb962710-d5f6-4ba6-aeb4-ef7a749091ed", "fc77a5a4-0e46-4650-8ba9-a1b32a449466"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\CoreController.php", "line": 61}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\Generator\\OrderCancelHistoryController.php", "line": 27}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00257, "duration_str": "2.57ms", "stmt_id": "\\app\\Http\\Controllers\\CoreController.php:61", "connection": "juragan_beku", "start_percent": 13.465, "width_percent": 0.773}, {"sql": "select `conf_group`.*, `conf_group_menu`.`menu_id` as `pivot_menu_id`, `conf_group_menu`.`group_id` as `pivot_group_id`, `conf_group_menu`.`id` as `pivot_id`, `conf_group_menu`.`is_addable` as `pivot_is_addable`, `conf_group_menu`.`is_editable` as `pivot_is_editable`, `conf_group_menu`.`is_deletable` as `pivot_is_deletable`, `conf_group_menu`.`is_viewable` as `pivot_is_viewable`, `conf_group_menu`.`created_at` as `pivot_created_at`, `conf_group_menu`.`updated_at` as `pivot_updated_at` from `conf_group` inner join `conf_group_menu` on `conf_group`.`id` = `conf_group_menu`.`group_id` where `conf_group_menu`.`deleted_at` is null and `conf_group_menu`.`menu_id` in ('01dcecc0-c805-4bfe-a8ad-f523d7fffa26', '04bc1af2-bad8-468b-84c8-0c47a89bae8c', '04e863de-047a-4d8e-973c-e789797e4a4f', '05425918-a3ef-4756-88a2-e907ca98a81e', '09af7259-5b37-4716-87dd-b8d1fd422e63', '0a347679-1d34-4bd5-8c36-eae1fe1c916c', '0a5aaeef-b6cc-4672-b533-7026809842dd', '0c919881-3ab7-40e6-b446-ab9c57998718', '0ddb84c4-da19-4449-9528-dafa05bc0504', '0f3d0fc9-fe09-490b-bb11-207f4948af0a', '163ae8f3-3516-4e53-938e-287538f37482', '1b656c21-3078-499f-b44a-d8f7a21eb29f', '1b705957-9f8a-4bed-a7b6-855a26a45576', '1ced30ab-5fa2-40f2-ba6d-f7a038b61983', '1e372edd-6218-4752-89fd-4507f1078de1', '2b93aa72-d810-46ea-9679-957fe4f5d997', '2e51431a-b769-4d0d-a627-d944f0bd2957', '30c7e095-e70e-4266-814f-cc421663c6e6', '354a271e-7e20-40fc-ba5a-1f23db8c00f5', '3aeb613c-d5c4-448c-b376-503252c7aa48', '3d8ae353-3736-40b9-bb1b-44b60b67eb1c', '3ec2b653-f948-46a8-8c57-aa28c8435923', '40904c07-fe95-4a4f-93e2-4ca303de8647', '41646f45-ea80-4049-8d6b-afd1ebbac87c', '426eedee-9489-48b4-93f7-c6e591342236', '4296cf9b-3658-4625-842a-5b66171da48b', '435cce6d-9645-4243-814e-c9918615d5e4', '47b4ee0e-8478-464e-acf8-c3d7c612113c', '48092a19-ba5a-4dad-b20f-5cb59aedc777', '4975676e-a170-44c6-b3ad-4304a9be5eb8', '4d9d0b21-b8b2-4abf-ac97-1a9368604300', '4ecdb1cd-aeb4-4fd0-8485-338b7439dc9d', '4f1223e9-a91b-433d-ac70-e30bec7566ad', '536ec0f4-7462-4ceb-9b39-802455cc5764', '5874e370-59e5-4e6a-8e41-52546e466492', '59b49c35-3850-4a9d-8635-d31c77e3f4bc', '5af03417-8b42-4c89-bdfb-7110826d1441', '5d2d475f-60d4-4d60-978c-cfabae2cf488', '5e8771fe-bb86-438b-b196-c4da91e6cd10', '66da0bfd-7aa5-40f6-a76c-24194c4e786a', '6725a57f-7506-43df-92a6-9b29421b93b8', '6bb01a1d-2ec1-46c9-8b6f-47b9beb76e96', '6d1dce4c-9857-41f4-84a7-f469f554b804', '6eda8f6d-c16f-4137-8142-d8c6e942d8c7', '70fbab85-b7ff-4e67-8474-14459141bb23', '713480b2-6382-4387-8d29-31a3f1f103a2', '714c0690-9a04-401b-b80b-48ed26a1c645', '74c092e4-f8bb-4d6f-996d-b1c88406adf7', '75e3e698-2b7b-4499-b449-feca72328d2e', '7d9f2c14-aea1-4d6b-8a11-42d0a619f292', '7e61030c-af9c-4edd-984b-4c414fc05b45', '7ff92f91-6239-4529-84cb-c510eaaf7394', '81212dac-52a2-4f5a-9d02-985d5b0b811c', '814fc401-5b17-4101-9816-5bab2bd106b6', '81a05ce5-b3e7-460e-b679-92eb66688c00', '835ccc11-1949-4614-a50e-577c14a03347', '843d55ee-7c77-451f-88c1-e30f3b0248e4', '84aa075a-cbf0-4863-93a0-a2659a2cd8ed', '86110001-4b90-4979-923f-9de27628d239', '88df6019-8237-471b-a436-83cc1cb0056a', '8a7c26db-38c1-4dba-8d0a-e3b76201abc8', '8c0247b3-7f55-453d-91a5-9c5b41a94b94', '8e781df6-5a3f-4f0b-bea1-865c4a659a9b', '9297b20b-2f4f-44cc-b6dc-19b46c15f549', '92fa1318-1a87-4ecf-871e-293596788039', '9c7a42d5-daf8-48fe-aa83-fa1e5c2a857b', '9e8352da-5e4d-49be-910e-1ffc6cfa909f', '9faed98a-be0f-41a8-b766-12bf76d40a22', 'a022819b-21cf-450c-93a5-6ec9841d2743', 'a15ff6a6-42e8-4a60-bdbb-ea80f41a9dac', 'a79cf485-f37f-4328-b89b-b119339398ef', 'a987c846-5c72-447b-a68c-733498ed977a', 'acd0dd1c-a5b0-4049-affe-e95ebb296478', 'ae8388f0-5f1e-4723-8de6-e708293ec9c4', 'aecd278f-abed-4f66-b295-c7aa1b078e27', 'b0bd9344-18bf-4960-9eef-bed0b2da1546', 'b1a17b3b-208f-4345-810c-ece101c456a7', 'b58b7b6f-5c00-4977-9fbd-076996aab6d0', 'b948eccc-ac77-4cca-bcbf-cb22e39fe1c1', 'bb734894-6d11-465f-b58f-fbdbbcd3f210', 'c02e2a45-bc33-40ea-88ca-8fb995680830', 'c15fc73a-270c-4044-92d1-154d10a4e503', 'c28d32ca-ae7f-44be-8058-ebddf4cea773', 'c29e03e8-4c41-4f25-9d45-ca2ee037d2a8', 'c43d7685-d7fa-45c2-bc4d-bc9242d4b688', 'c45901c9-5b09-4904-bf85-8030ba2022ed', 'c56a1871-b751-4414-8b30-1614d1eb01b8', 'c6f57b96-9ce4-4f4c-9e7a-e673aa3ba05d', 'c8562214-65c1-4aaa-a7a5-90e8d247bf04', 'c98db184-64a9-448e-b4b5-f8f94e6bb3f6', 'c9961577-ba7a-4723-bfc8-66e0227d9655', 'cace2334-2bf3-448c-b648-076e1924d4d5', 'cb56118d-3ec1-45ff-9d02-90afacb29444', 'cd3961dc-f993-46e4-b8bb-e2f95b366f7c', 'd75631a4-7999-4d30-820e-68ee58aff55b', 'e0a4f8a8-7ea4-4f50-aed0-e8c6cde2f477', 'e0f71c5e-1081-472e-bff8-d696445331fe', 'e32d1b6a-6991-4822-a4b6-367d023be2ef', 'e3408275-6056-4c66-9fda-50d2c505f806', 'e6d88533-cf7f-4ef3-b23a-caeaa0c2c08f', 'e8895d0e-590b-445b-ae8d-1bad4f81180f', 'eadc51c2-6e22-401f-8780-2d898a8db951', 'f392d4c0-6807-41ff-81a8-6c82e8753c89', 'f4cc69d3-6dcc-4dc0-ada2-5c11b4253391', 'f594b66d-2f6d-4f16-8a4e-8c81539be1d8', 'fb3a59bb-2e5b-423f-aae6-2b54a491cdb7', 'fb3f014b-7f39-4ffe-a02a-86d490496ee4', 'fc6d2d25-de43-4ec1-bb16-df8a56986091', 'fc7448fc-3f39-4894-a8e3-d194c63ee73e', 'fe956fd4-b5ee-4b7d-a33f-e9e56328e493') and `conf_group`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["01dcecc0-c805-4bfe-a8ad-f523d7fffa26", "04bc1af2-bad8-468b-84c8-0c47a89bae8c", "04e863de-047a-4d8e-973c-e789797e4a4f", "05425918-a3ef-4756-88a2-e907ca98a81e", "09af7259-5b37-4716-87dd-b8d1fd422e63", "0a347679-1d34-4bd5-8c36-eae1fe1c916c", "0a5aaeef-b6cc-4672-b533-7026809842dd", "0c919881-3ab7-40e6-b446-ab9c57998718", "0ddb84c4-da19-4449-9528-dafa05bc0504", "0f3d0fc9-fe09-490b-bb11-207f4948af0a", "163ae8f3-3516-4e53-938e-287538f37482", "1b656c21-3078-499f-b44a-d8f7a21eb29f", "1b705957-9f8a-4bed-a7b6-855a26a45576", "1ced30ab-5fa2-40f2-ba6d-f7a038b61983", "1e372edd-6218-4752-89fd-4507f1078de1", "2b93aa72-d810-46ea-9679-957fe4f5d997", "2e51431a-b769-4d0d-a627-d944f0bd2957", "30c7e095-e70e-4266-814f-cc421663c6e6", "354a271e-7e20-40fc-ba5a-1f23db8c00f5", "3aeb613c-d5c4-448c-b376-503252c7aa48", "3d8ae353-3736-40b9-bb1b-44b60b67eb1c", "3ec2b653-f948-46a8-8c57-aa28c8435923", "40904c07-fe95-4a4f-93e2-4ca303de8647", "41646f45-ea80-4049-8d6b-afd1ebbac87c", "426eedee-9489-48b4-93f7-c6e591342236", "4296cf9b-3658-4625-842a-5b66171da48b", "435cce6d-9645-4243-814e-c9918615d5e4", "47b4ee0e-8478-464e-acf8-c3d7c612113c", "48092a19-ba5a-4dad-b20f-5cb59aedc777", "4975676e-a170-44c6-b3ad-4304a9be5eb8", "4d9d0b21-b8b2-4abf-ac97-1a9368604300", "4ecdb1cd-aeb4-4fd0-8485-338b7439dc9d", "4f1223e9-a91b-433d-ac70-e30bec7566ad", "536ec0f4-7462-4ceb-9b39-802455cc5764", "5874e370-59e5-4e6a-8e41-52546e466492", "59b49c35-3850-4a9d-8635-d31c77e3f4bc", "5af03417-8b42-4c89-bdfb-7110826d1441", "5d2d475f-60d4-4d60-978c-cfabae2cf488", "5e8771fe-bb86-438b-b196-c4da91e6cd10", "66da0bfd-7aa5-40f6-a76c-24194c4e786a", "6725a57f-7506-43df-92a6-9b29421b93b8", "6bb01a1d-2ec1-46c9-8b6f-47b9beb76e96", "6d1dce4c-9857-41f4-84a7-f469f554b804", "6eda8f6d-c16f-4137-8142-d8c6e942d8c7", "70fbab85-b7ff-4e67-8474-14459141bb23", "713480b2-6382-4387-8d29-31a3f1f103a2", "714c0690-9a04-401b-b80b-48ed26a1c645", "74c092e4-f8bb-4d6f-996d-b1c88406adf7", "75e3e698-2b7b-4499-b449-feca72328d2e", "7d9f2c14-aea1-4d6b-8a11-42d0a619f292", "7e61030c-af9c-4edd-984b-4c414fc05b45", "7ff92f91-6239-4529-84cb-c510eaaf7394", "81212dac-52a2-4f5a-9d02-985d5b0b811c", "814fc401-5b17-4101-9816-5bab2bd106b6", "81a05ce5-b3e7-460e-b679-92eb66688c00", "835ccc11-1949-4614-a50e-577c14a03347", "843d55ee-7c77-451f-88c1-e30f3b0248e4", "84aa075a-cbf0-4863-93a0-a2659a2cd8ed", "86110001-4b90-4979-923f-9de27628d239", "88df6019-8237-471b-a436-83cc1cb0056a", "8a7c26db-38c1-4dba-8d0a-e3b76201abc8", "8c0247b3-7f55-453d-91a5-9c5b41a94b94", "8e781df6-5a3f-4f0b-bea1-865c4a659a9b", "9297b20b-2f4f-44cc-b6dc-19b46c15f549", "92fa1318-1a87-4ecf-871e-293596788039", "9c7a42d5-daf8-48fe-aa83-fa1e5c2a857b", "9e8352da-5e4d-49be-910e-1ffc6cfa909f", "9faed98a-be0f-41a8-b766-12bf76d40a22", "a022819b-21cf-450c-93a5-6ec9841d2743", "a15ff6a6-42e8-4a60-bdbb-ea80f41a9dac", "a79cf485-f37f-4328-b89b-b119339398ef", "a987c846-5c72-447b-a68c-733498ed977a", "acd0dd1c-a5b0-4049-affe-e95ebb296478", "ae8388f0-5f1e-4723-8de6-e708293ec9c4", "aecd278f-abed-4f66-b295-c7aa1b078e27", "b0bd9344-18bf-4960-9eef-bed0b2da1546", "b1a17b3b-208f-4345-810c-ece101c456a7", "b58b7b6f-5c00-4977-9fbd-076996aab6d0", "b948eccc-ac77-4cca-bcbf-cb22e39fe1c1", "bb734894-6d11-465f-b58f-fbdbbcd3f210", "c02e2a45-bc33-40ea-88ca-8fb995680830", "c15fc73a-270c-4044-92d1-154d10a4e503", "c28d32ca-ae7f-44be-8058-ebddf4cea773", "c29e03e8-4c41-4f25-9d45-ca2ee037d2a8", "c43d7685-d7fa-45c2-bc4d-bc9242d4b688", "c45901c9-5b09-4904-bf85-8030ba2022ed", "c56a1871-b751-4414-8b30-1614d1eb01b8", "c6f57b96-9ce4-4f4c-9e7a-e673aa3ba05d", "c8562214-65c1-4aaa-a7a5-90e8d247bf04", "c98db184-64a9-448e-b4b5-f8f94e6bb3f6", "c9961577-ba7a-4723-bfc8-66e0227d9655", "cace2334-2bf3-448c-b648-076e1924d4d5", "cb56118d-3ec1-45ff-9d02-90afacb29444", "cd3961dc-f993-46e4-b8bb-e2f95b366f7c", "d75631a4-7999-4d30-820e-68ee58aff55b", "e0a4f8a8-7ea4-4f50-aed0-e8c6cde2f477", "e0f71c5e-1081-472e-bff8-d696445331fe", "e32d1b6a-6991-4822-a4b6-367d023be2ef", "e3408275-6056-4c66-9fda-50d2c505f806", "e6d88533-cf7f-4ef3-b23a-caeaa0c2c08f", "e8895d0e-590b-445b-ae8d-1bad4f81180f", "eadc51c2-6e22-401f-8780-2d898a8db951", "f392d4c0-6807-41ff-81a8-6c82e8753c89", "f4cc69d3-6dcc-4dc0-ada2-5c11b4253391", "f594b66d-2f6d-4f16-8a4e-8c81539be1d8", "fb3a59bb-2e5b-423f-aae6-2b54a491cdb7", "fb3f014b-7f39-4ffe-a02a-86d490496ee4", "fc6d2d25-de43-4ec1-bb16-df8a56986091", "fc7448fc-3f39-4894-a8e3-d194c63ee73e", "fe956fd4-b5ee-4b7d-a33f-e9e56328e493"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "\\app\\Http\\Controllers\\CoreController.php", "line": 61}, {"index": 24, "namespace": null, "name": "\\app\\Http\\Controllers\\Generator\\OrderCancelHistoryController.php", "line": 27}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.023489999999999997, "duration_str": "23.49ms", "stmt_id": "\\app\\Http\\Controllers\\CoreController.php:61", "connection": "juragan_beku", "start_percent": 14.238, "width_percent": 7.069}, {"sql": "select * from `conf_menu` where `route_name` = 'dashboard_ordercancelhistories_table' limit 1", "type": "query", "params": [], "bindings": ["dashboard_ordercancelhistories_table"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 23}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Controllers\\Generator\\OrderCancelHistoryController.php", "line": 27}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00099, "duration_str": "990μs", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:23", "connection": "juragan_beku", "start_percent": 21.307, "width_percent": 0.298}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'juragan_beku' and table_name = 'conf_setting'", "type": "query", "params": [], "bindings": ["juragan_beku", "conf_setting"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php", "line": 33}, {"index": 12, "namespace": null, "name": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php", "line": 16}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\CoreController.php", "line": 25}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\Generator\\OrderCancelHistoryController.php", "line": 30}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}], "duration": 0.0016899999999999999, "duration_str": "1.69ms", "stmt_id": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php:33", "connection": "juragan_beku", "start_percent": 21.605, "width_percent": 0.509}, {"sql": "select * from `conf_setting` where `conf_setting`.`deleted_at` is null and `parameter` in ('logo', 'app_name', 'app_name_short', 'footer', 'logo_icon')", "type": "query", "params": [], "bindings": ["logo", "app_name", "app_name_short", "footer", "logo_icon"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\CoreController.php", "line": 25}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\Generator\\OrderCancelHistoryController.php", "line": 30}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00062, "duration_str": "620μs", "stmt_id": "\\app\\Http\\Controllers\\CoreController.php:25", "connection": "juragan_beku", "start_percent": 22.114, "width_percent": 0.187}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'juragan_beku' and table_name = 'conf_users'", "type": "query", "params": [], "bindings": ["juragan_beku", "conf_users"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php", "line": 33}, {"index": 12, "namespace": null, "name": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php", "line": 16}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 185}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 49}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}], "duration": 0.0017900000000000001, "duration_str": "1.79ms", "stmt_id": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php:33", "connection": "juragan_beku", "start_percent": 22.3, "width_percent": 0.539}, {"sql": "select * from `conf_users` where `id` = 'c065dc93-6b2f-4bf5-b746-6bb48713d4bf' and `conf_users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["c065dc93-6b2f-4bf5-b746-6bb48713d4bf"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00092, "duration_str": "920μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "juragan_beku", "start_percent": 22.839, "width_percent": 0.277}, {"sql": "select * from `conf_menu` where `conf_menu`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": "middleware", "name": "roles", "line": 23}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00121, "duration_str": "1.21ms", "stmt_id": "middleware::roles:23", "connection": "juragan_beku", "start_percent": 23.116, "width_percent": 0.364}, {"sql": "select `conf_group`.*, `conf_group_menu`.`menu_id` as `pivot_menu_id`, `conf_group_menu`.`group_id` as `pivot_group_id`, `conf_group_menu`.`id` as `pivot_id`, `conf_group_menu`.`is_addable` as `pivot_is_addable`, `conf_group_menu`.`is_editable` as `pivot_is_editable`, `conf_group_menu`.`is_deletable` as `pivot_is_deletable`, `conf_group_menu`.`is_viewable` as `pivot_is_viewable`, `conf_group_menu`.`created_at` as `pivot_created_at`, `conf_group_menu`.`updated_at` as `pivot_updated_at` from `conf_group` inner join `conf_group_menu` on `conf_group`.`id` = `conf_group_menu`.`group_id` where `conf_group_menu`.`deleted_at` is null and `group_id` = 'ff9759b5-2dc5-410b-b86c-f8146c1f66d8' and `conf_group_menu`.`menu_id` in ('01dcecc0-c805-4bfe-a8ad-f523d7fffa26', '04bc1af2-bad8-468b-84c8-0c47a89bae8c', '04e863de-047a-4d8e-973c-e789797e4a4f', '05425918-a3ef-4756-88a2-e907ca98a81e', '09af7259-5b37-4716-87dd-b8d1fd422e63', '0a347679-1d34-4bd5-8c36-eae1fe1c916c', '0a5aaeef-b6cc-4672-b533-7026809842dd', '0c919881-3ab7-40e6-b446-ab9c57998718', '0ddb84c4-da19-4449-9528-dafa05bc0504', '0f3d0fc9-fe09-490b-bb11-207f4948af0a', '12172b03-c059-47f6-8b3a-ec45fff0bfb0', '163ae8f3-3516-4e53-938e-287538f37482', '1b656c21-3078-499f-b44a-d8f7a21eb29f', '1b705957-9f8a-4bed-a7b6-855a26a45576', '1ced30ab-5fa2-40f2-ba6d-f7a038b61983', '1e171ae1-4f20-47b8-898c-a3d7028a3ab8', '1e372edd-6218-4752-89fd-4507f1078de1', '2412018e-b714-4252-8674-1f7ced3b4c8e', '2afdc4cd-6699-4fdb-8774-02fd3c52c7a9', '2b93aa72-d810-46ea-9679-957fe4f5d997', '2e51431a-b769-4d0d-a627-d944f0bd2957', '30c7e095-e70e-4266-814f-cc421663c6e6', '354a271e-7e20-40fc-ba5a-1f23db8c00f5', '35bdff65-5393-4c72-a885-1026cdfdaed6', '3629349a-e969-47f6-ba61-2d75c110dba7', '3927daac-5667-4d64-9c1e-ba804d715ad6', '3aeb613c-d5c4-448c-b376-503252c7aa48', '3d8ae353-3736-40b9-bb1b-44b60b67eb1c', '3ec2b653-f948-46a8-8c57-aa28c8435923', '40904c07-fe95-4a4f-93e2-4ca303de8647', '41646f45-ea80-4049-8d6b-afd1ebbac87c', '426eedee-9489-48b4-93f7-c6e591342236', '4296cf9b-3658-4625-842a-5b66171da48b', '42ecc46f-d988-4079-8fcc-e80f086ae0b8', '435cce6d-9645-4243-814e-c9918615d5e4', '4705379c-5fb2-4c32-9cd0-f8510de5f8c1', '47b4ee0e-8478-464e-acf8-c3d7c612113c', '48092a19-ba5a-4dad-b20f-5cb59aedc777', '4975676e-a170-44c6-b3ad-4304a9be5eb8', '4d9d0b21-b8b2-4abf-ac97-1a9368604300', '4ecdb1cd-aeb4-4fd0-8485-338b7439dc9d', '4f1223e9-a91b-433d-ac70-e30bec7566ad', '536ec0f4-7462-4ceb-9b39-802455cc5764', '5874e370-59e5-4e6a-8e41-52546e466492', '59b49c35-3850-4a9d-8635-d31c77e3f4bc', '5af03417-8b42-4c89-bdfb-7110826d1441', '5d2d475f-60d4-4d60-978c-cfabae2cf488', '5e8771fe-bb86-438b-b196-c4da91e6cd10', '66da0bfd-7aa5-40f6-a76c-24194c4e786a', '6725a57f-7506-43df-92a6-9b29421b93b8', '6bb01a1d-2ec1-46c9-8b6f-47b9beb76e96', '6d1dce4c-9857-41f4-84a7-f469f554b804', '6d3265c5-86a7-4f93-a5e5-dbacf271bf08', '6d34fee1-1985-4885-863d-a7f34134c662', '6e655b95-8adb-45e6-9bad-aedb2917aa95', '6eb47bc6-8aa1-4548-965e-b17280f8265e', '6eda8f6d-c16f-4137-8142-d8c6e942d8c7', '70fbab85-b7ff-4e67-8474-14459141bb23', '710fd92b-bb27-4596-96a1-cc4cc9d4da53', '713480b2-6382-4387-8d29-31a3f1f103a2', '714c0690-9a04-401b-b80b-48ed26a1c645', '74c092e4-f8bb-4d6f-996d-b1c88406adf7', '751def3a-b03e-44d2-83d3-bc5c4df78c9d', '75e3e698-2b7b-4499-b449-feca72328d2e', '7afebf9b-18a3-4663-945b-cbba8403c5e5', '7c8ba58d-5450-4607-859c-152617371562', '7d9f2c14-aea1-4d6b-8a11-42d0a619f292', '7e61030c-af9c-4edd-984b-4c414fc05b45', '7ff92f91-6239-4529-84cb-c510eaaf7394', '81212dac-52a2-4f5a-9d02-985d5b0b811c', '814fc401-5b17-4101-9816-5bab2bd106b6', '81a05ce5-b3e7-460e-b679-92eb66688c00', '835ccc11-1949-4614-a50e-577c14a03347', '843d55ee-7c77-451f-88c1-e30f3b0248e4', '84aa075a-cbf0-4863-93a0-a2659a2cd8ed', '86110001-4b90-4979-923f-9de27628d239', '88df6019-8237-471b-a436-83cc1cb0056a', '89c3ba3a-beca-4eaf-b825-9df62b18f100', '8a7c26db-38c1-4dba-8d0a-e3b76201abc8', '8b6c271e-5599-4599-a037-6f36cbaf5df4', '8c0247b3-7f55-453d-91a5-9c5b41a94b94', '8e781df6-5a3f-4f0b-bea1-865c4a659a9b', '908d7755-397d-459a-8dd9-b5c1cf2933a1', '9297b20b-2f4f-44cc-b6dc-19b46c15f549', '92fa1318-1a87-4ecf-871e-293596788039', '95a301cc-df41-4af8-a735-42a88252cc28', '9775dc51-db4d-4ff3-a0eb-bc02582b2519', '9963370e-03d2-47fc-bf10-d8afb45f2777', '9963370e-03d2-47fc-bf10-d8afb45f2778', '9c7a42d5-daf8-48fe-aa83-fa1e5c2a857b', '9e8352da-5e4d-49be-910e-1ffc6cfa909f', '9faed98a-be0f-41a8-b766-12bf76d40a22', 'a022819b-21cf-450c-93a5-6ec9841d2743', 'a15ff6a6-42e8-4a60-bdbb-ea80f41a9dac', 'a663bdd0-dd56-435c-b01c-56914d355319', 'a79cf485-f37f-4328-b89b-b119339398ef', 'a858dc43-a5b2-47ac-8b19-2122748997d7', 'a987c846-5c72-447b-a68c-733498ed977a', 'a99e9853-75af-41ad-ab42-e367c434342e', 'ab6c7821-03a9-4d1e-b62d-4aa1e75ac2bc', 'acd0dd1c-a5b0-4049-affe-e95ebb296478', 'ae8388f0-5f1e-4723-8de6-e708293ec9c4', 'aecd278f-abed-4f66-b295-c7aa1b078e27', 'b0bd9344-18bf-4960-9eef-bed0b2da1546', 'b1a17b3b-208f-4345-810c-ece101c456a7', 'b1ce929b-2658-464d-8423-f83025eba65a', 'b58b7b6f-5c00-4977-9fbd-076996aab6d0', 'b6ae8a73-8645-411b-9f3a-664ca8d0d0fb', 'b88aa02c-a949-490e-82dd-dd99fde4384d', 'b948eccc-ac77-4cca-bcbf-cb22e39fe1c1', 'ba1d3b8c-b6e1-4a5c-b0ba-9fcb73895e79', 'bb734894-6d11-465f-b58f-fbdbbcd3f210', 'c02e2a45-bc33-40ea-88ca-8fb995680830', 'c15fc73a-270c-4044-92d1-154d10a4e503', 'c28d32ca-ae7f-44be-8058-ebddf4cea773', 'c29e03e8-4c41-4f25-9d45-ca2ee037d2a8', 'c43d7685-d7fa-45c2-bc4d-bc9242d4b688', 'c45901c9-5b09-4904-bf85-8030ba2022ed', 'c56a1871-b751-4414-8b30-1614d1eb01b8', 'c6f57b96-9ce4-4f4c-9e7a-e673aa3ba05d', 'c8562214-65c1-4aaa-a7a5-90e8d247bf04', 'c98db184-64a9-448e-b4b5-f8f94e6bb3f6', 'c9961577-ba7a-4723-bfc8-66e0227d9655', 'cace2334-2bf3-448c-b648-076e1924d4d5', 'cb56118d-3ec1-45ff-9d02-90afacb29444', 'cd3961dc-f993-46e4-b8bb-e2f95b366f7c', 'd302114a-df52-4183-9416-17beeb3ed264', 'd75631a4-7999-4d30-820e-68ee58aff55b', 'e0a4f8a8-7ea4-4f50-aed0-e8c6cde2f477', 'e0f71c5e-1081-472e-bff8-d696445331fe', 'e32d1b6a-6991-4822-a4b6-367d023be2ef', 'e3408275-6056-4c66-9fda-50d2c505f806', 'e6d88533-cf7f-4ef3-b23a-caeaa0c2c08f', 'e8895d0e-590b-445b-ae8d-1bad4f81180f', 'eadc51c2-6e22-401f-8780-2d898a8db951', 'f392d4c0-6807-41ff-81a8-6c82e8753c89', 'f4cc69d3-6dcc-4dc0-ada2-5c11b4253391', 'f4cc69d3-6dcc-4dc0-ada2-5c11b4253392', 'f594b66d-2f6d-4f16-8a4e-8c81539be1d8', 'f8a46c4d-7463-464c-abd5-a2187680201c', 'fb06a851-d01d-4cfd-a738-fcbc90b2ea43', 'fb3a59bb-2e5b-423f-aae6-2b54a491cdb7', 'fb3f014b-7f39-4ffe-a02a-86d490496ee4', 'fb962710-d5f6-4ba6-aeb4-ef7a749091ed', 'fc6d2d25-de43-4ec1-bb16-df8a56986091', 'fc7448fc-3f39-4894-a8e3-d194c63ee73e', 'fc77a5a4-0e46-4650-8ba9-a1b32a449466', 'fe956fd4-b5ee-4b7d-a33f-e9e56328e493') and `conf_group`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["ff9759b5-2dc5-410b-b86c-f8146c1f66d8", "01dcecc0-c805-4bfe-a8ad-f523d7fffa26", "04bc1af2-bad8-468b-84c8-0c47a89bae8c", "04e863de-047a-4d8e-973c-e789797e4a4f", "05425918-a3ef-4756-88a2-e907ca98a81e", "09af7259-5b37-4716-87dd-b8d1fd422e63", "0a347679-1d34-4bd5-8c36-eae1fe1c916c", "0a5aaeef-b6cc-4672-b533-7026809842dd", "0c919881-3ab7-40e6-b446-ab9c57998718", "0ddb84c4-da19-4449-9528-dafa05bc0504", "0f3d0fc9-fe09-490b-bb11-207f4948af0a", "12172b03-c059-47f6-8b3a-ec45fff0bfb0", "163ae8f3-3516-4e53-938e-287538f37482", "1b656c21-3078-499f-b44a-d8f7a21eb29f", "1b705957-9f8a-4bed-a7b6-855a26a45576", "1ced30ab-5fa2-40f2-ba6d-f7a038b61983", "1e171ae1-4f20-47b8-898c-a3d7028a3ab8", "1e372edd-6218-4752-89fd-4507f1078de1", "2412018e-b714-4252-8674-1f7ced3b4c8e", "2afdc4cd-6699-4fdb-8774-02fd3c52c7a9", "2b93aa72-d810-46ea-9679-957fe4f5d997", "2e51431a-b769-4d0d-a627-d944f0bd2957", "30c7e095-e70e-4266-814f-cc421663c6e6", "354a271e-7e20-40fc-ba5a-1f23db8c00f5", "35bdff65-5393-4c72-a885-1026cdfdaed6", "3629349a-e969-47f6-ba61-2d75c110dba7", "3927daac-5667-4d64-9c1e-ba804d715ad6", "3aeb613c-d5c4-448c-b376-503252c7aa48", "3d8ae353-3736-40b9-bb1b-44b60b67eb1c", "3ec2b653-f948-46a8-8c57-aa28c8435923", "40904c07-fe95-4a4f-93e2-4ca303de8647", "41646f45-ea80-4049-8d6b-afd1ebbac87c", "426eedee-9489-48b4-93f7-c6e591342236", "4296cf9b-3658-4625-842a-5b66171da48b", "42ecc46f-d988-4079-8fcc-e80f086ae0b8", "435cce6d-9645-4243-814e-c9918615d5e4", "4705379c-5fb2-4c32-9cd0-f8510de5f8c1", "47b4ee0e-8478-464e-acf8-c3d7c612113c", "48092a19-ba5a-4dad-b20f-5cb59aedc777", "4975676e-a170-44c6-b3ad-4304a9be5eb8", "4d9d0b21-b8b2-4abf-ac97-1a9368604300", "4ecdb1cd-aeb4-4fd0-8485-338b7439dc9d", "4f1223e9-a91b-433d-ac70-e30bec7566ad", "536ec0f4-7462-4ceb-9b39-802455cc5764", "5874e370-59e5-4e6a-8e41-52546e466492", "59b49c35-3850-4a9d-8635-d31c77e3f4bc", "5af03417-8b42-4c89-bdfb-7110826d1441", "5d2d475f-60d4-4d60-978c-cfabae2cf488", "5e8771fe-bb86-438b-b196-c4da91e6cd10", "66da0bfd-7aa5-40f6-a76c-24194c4e786a", "6725a57f-7506-43df-92a6-9b29421b93b8", "6bb01a1d-2ec1-46c9-8b6f-47b9beb76e96", "6d1dce4c-9857-41f4-84a7-f469f554b804", "6d3265c5-86a7-4f93-a5e5-dbacf271bf08", "6d34fee1-1985-4885-863d-a7f34134c662", "6e655b95-8adb-45e6-9bad-aedb2917aa95", "6eb47bc6-8aa1-4548-965e-b17280f8265e", "6eda8f6d-c16f-4137-8142-d8c6e942d8c7", "70fbab85-b7ff-4e67-8474-14459141bb23", "710fd92b-bb27-4596-96a1-cc4cc9d4da53", "713480b2-6382-4387-8d29-31a3f1f103a2", "714c0690-9a04-401b-b80b-48ed26a1c645", "74c092e4-f8bb-4d6f-996d-b1c88406adf7", "751def3a-b03e-44d2-83d3-bc5c4df78c9d", "75e3e698-2b7b-4499-b449-feca72328d2e", "7afebf9b-18a3-4663-945b-cbba8403c5e5", "7c8ba58d-5450-4607-859c-152617371562", "7d9f2c14-aea1-4d6b-8a11-42d0a619f292", "7e61030c-af9c-4edd-984b-4c414fc05b45", "7ff92f91-6239-4529-84cb-c510eaaf7394", "81212dac-52a2-4f5a-9d02-985d5b0b811c", "814fc401-5b17-4101-9816-5bab2bd106b6", "81a05ce5-b3e7-460e-b679-92eb66688c00", "835ccc11-1949-4614-a50e-577c14a03347", "843d55ee-7c77-451f-88c1-e30f3b0248e4", "84aa075a-cbf0-4863-93a0-a2659a2cd8ed", "86110001-4b90-4979-923f-9de27628d239", "88df6019-8237-471b-a436-83cc1cb0056a", "89c3ba3a-beca-4eaf-b825-9df62b18f100", "8a7c26db-38c1-4dba-8d0a-e3b76201abc8", "8b6c271e-5599-4599-a037-6f36cbaf5df4", "8c0247b3-7f55-453d-91a5-9c5b41a94b94", "8e781df6-5a3f-4f0b-bea1-865c4a659a9b", "908d7755-397d-459a-8dd9-b5c1cf2933a1", "9297b20b-2f4f-44cc-b6dc-19b46c15f549", "92fa1318-1a87-4ecf-871e-293596788039", "95a301cc-df41-4af8-a735-42a88252cc28", "9775dc51-db4d-4ff3-a0eb-bc02582b2519", "9963370e-03d2-47fc-bf10-d8afb45f2777", "9963370e-03d2-47fc-bf10-d8afb45f2778", "9c7a42d5-daf8-48fe-aa83-fa1e5c2a857b", "9e8352da-5e4d-49be-910e-1ffc6cfa909f", "9faed98a-be0f-41a8-b766-12bf76d40a22", "a022819b-21cf-450c-93a5-6ec9841d2743", "a15ff6a6-42e8-4a60-bdbb-ea80f41a9dac", "a663bdd0-dd56-435c-b01c-56914d355319", "a79cf485-f37f-4328-b89b-b119339398ef", "a858dc43-a5b2-47ac-8b19-2122748997d7", "a987c846-5c72-447b-a68c-733498ed977a", "a99e9853-75af-41ad-ab42-e367c434342e", "ab6c7821-03a9-4d1e-b62d-4aa1e75ac2bc", "acd0dd1c-a5b0-4049-affe-e95ebb296478", "ae8388f0-5f1e-4723-8de6-e708293ec9c4", "aecd278f-abed-4f66-b295-c7aa1b078e27", "b0bd9344-18bf-4960-9eef-bed0b2da1546", "b1a17b3b-208f-4345-810c-ece101c456a7", "b1ce929b-2658-464d-8423-f83025eba65a", "b58b7b6f-5c00-4977-9fbd-076996aab6d0", "b6ae8a73-8645-411b-9f3a-664ca8d0d0fb", "b88aa02c-a949-490e-82dd-dd99fde4384d", "b948eccc-ac77-4cca-bcbf-cb22e39fe1c1", "ba1d3b8c-b6e1-4a5c-b0ba-9fcb73895e79", "bb734894-6d11-465f-b58f-fbdbbcd3f210", "c02e2a45-bc33-40ea-88ca-8fb995680830", "c15fc73a-270c-4044-92d1-154d10a4e503", "c28d32ca-ae7f-44be-8058-ebddf4cea773", "c29e03e8-4c41-4f25-9d45-ca2ee037d2a8", "c43d7685-d7fa-45c2-bc4d-bc9242d4b688", "c45901c9-5b09-4904-bf85-8030ba2022ed", "c56a1871-b751-4414-8b30-1614d1eb01b8", "c6f57b96-9ce4-4f4c-9e7a-e673aa3ba05d", "c8562214-65c1-4aaa-a7a5-90e8d247bf04", "c98db184-64a9-448e-b4b5-f8f94e6bb3f6", "c9961577-ba7a-4723-bfc8-66e0227d9655", "cace2334-2bf3-448c-b648-076e1924d4d5", "cb56118d-3ec1-45ff-9d02-90afacb29444", "cd3961dc-f993-46e4-b8bb-e2f95b366f7c", "d302114a-df52-4183-9416-17beeb3ed264", "d75631a4-7999-4d30-820e-68ee58aff55b", "e0a4f8a8-7ea4-4f50-aed0-e8c6cde2f477", "e0f71c5e-1081-472e-bff8-d696445331fe", "e32d1b6a-6991-4822-a4b6-367d023be2ef", "e3408275-6056-4c66-9fda-50d2c505f806", "e6d88533-cf7f-4ef3-b23a-caeaa0c2c08f", "e8895d0e-590b-445b-ae8d-1bad4f81180f", "eadc51c2-6e22-401f-8780-2d898a8db951", "f392d4c0-6807-41ff-81a8-6c82e8753c89", "f4cc69d3-6dcc-4dc0-ada2-5c11b4253391", "f4cc69d3-6dcc-4dc0-ada2-5c11b4253392", "f594b66d-2f6d-4f16-8a4e-8c81539be1d8", "f8a46c4d-7463-464c-abd5-a2187680201c", "fb06a851-d01d-4cfd-a738-fcbc90b2ea43", "fb3a59bb-2e5b-423f-aae6-2b54a491cdb7", "fb3f014b-7f39-4ffe-a02a-86d490496ee4", "fb962710-d5f6-4ba6-aeb4-ef7a749091ed", "fc6d2d25-de43-4ec1-bb16-df8a56986091", "fc7448fc-3f39-4894-a8e3-d194c63ee73e", "fc77a5a4-0e46-4650-8ba9-a1b32a449466", "fe956fd4-b5ee-4b7d-a33f-e9e56328e493"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": "middleware", "name": "roles", "line": 23}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00379, "duration_str": "3.79ms", "stmt_id": "middleware::roles:23", "connection": "juragan_beku", "start_percent": 23.48, "width_percent": 1.141}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'b88aa02c-a949-490e-82dd-dd99fde4384d' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["b88aa02c-a949-490e-82dd-dd99fde4384d"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00099, "duration_str": "990μs", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 24.621, "width_percent": 0.298}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'a663bdd0-dd56-435c-b01c-56914d355319' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["a663bdd0-dd56-435c-b01c-56914d355319"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00088, "duration_str": "880μs", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 24.919, "width_percent": 0.265}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'b6ae8a73-8645-411b-9f3a-664ca8d0d0fb' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["b6ae8a73-8645-411b-9f3a-664ca8d0d0fb"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00058, "duration_str": "580μs", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 25.184, "width_percent": 0.175}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'b6ae8a73-8645-411b-9f3a-664ca8d0d0fb' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["b6ae8a73-8645-411b-9f3a-664ca8d0d0fb"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.0006, "duration_str": "600μs", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 25.358, "width_percent": 0.181}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '7afebf9b-18a3-4663-945b-cbba8403c5e5' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7afebf9b-18a3-4663-945b-cbba8403c5e5"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00068, "duration_str": "680μs", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 25.539, "width_percent": 0.205}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '3927daac-5667-4d64-9c1e-ba804d715ad6' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["3927daac-5667-4d64-9c1e-ba804d715ad6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00055, "duration_str": "550μs", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 25.743, "width_percent": 0.166}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '710fd92b-bb27-4596-96a1-cc4cc9d4da53' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["710fd92b-bb27-4596-96a1-cc4cc9d4da53"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00047, "duration_str": "470μs", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 25.909, "width_percent": 0.141}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'b88aa02c-a949-490e-82dd-dd99fde4384d' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["b88aa02c-a949-490e-82dd-dd99fde4384d"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00047999999999999996, "duration_str": "480μs", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 26.05, "width_percent": 0.144}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'a99e9853-75af-41ad-ab42-e367c434342e' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["a99e9853-75af-41ad-ab42-e367c434342e"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00047999999999999996, "duration_str": "480μs", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 26.195, "width_percent": 0.144}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'b6ae8a73-8645-411b-9f3a-664ca8d0d0fb' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["b6ae8a73-8645-411b-9f3a-664ca8d0d0fb"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00041999999999999996, "duration_str": "420μs", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 26.339, "width_percent": 0.126}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '908d7755-397d-459a-8dd9-b5c1cf2933a1' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["908d7755-397d-459a-8dd9-b5c1cf2933a1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00044, "duration_str": "440μs", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 26.466, "width_percent": 0.132}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '95a301cc-df41-4af8-a735-42a88252cc28' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["95a301cc-df41-4af8-a735-42a88252cc28"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00041999999999999996, "duration_str": "420μs", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 26.598, "width_percent": 0.126}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'b88aa02c-a949-490e-82dd-dd99fde4384d' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["b88aa02c-a949-490e-82dd-dd99fde4384d"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00046, "duration_str": "460μs", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 26.724, "width_percent": 0.138}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '6e655b95-8adb-45e6-9bad-aedb2917aa95' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6e655b95-8adb-45e6-9bad-aedb2917aa95"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.0006, "duration_str": "600μs", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 26.863, "width_percent": 0.181}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'b88aa02c-a949-490e-82dd-dd99fde4384d' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["b88aa02c-a949-490e-82dd-dd99fde4384d"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00053, "duration_str": "530μs", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 27.043, "width_percent": 0.16}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '42ecc46f-d988-4079-8fcc-e80f086ae0b8' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["42ecc46f-d988-4079-8fcc-e80f086ae0b8"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00047, "duration_str": "470μs", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 27.203, "width_percent": 0.141}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'b1ce929b-2658-464d-8423-f83025eba65a' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["b1ce929b-2658-464d-8423-f83025eba65a"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.0005200000000000001, "duration_str": "520μs", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 27.344, "width_percent": 0.156}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '6eb47bc6-8aa1-4548-965e-b17280f8265e' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6eb47bc6-8aa1-4548-965e-b17280f8265e"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00047, "duration_str": "470μs", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 27.501, "width_percent": 0.141}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'b88aa02c-a949-490e-82dd-dd99fde4384d' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["b88aa02c-a949-490e-82dd-dd99fde4384d"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00053, "duration_str": "530μs", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 27.642, "width_percent": 0.16}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = 'fb06a851-d01d-4cfd-a738-fcbc90b2ea43' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["fb06a851-d01d-4cfd-a738-fcbc90b2ea43"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.0006, "duration_str": "600μs", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 27.802, "width_percent": 0.181}, {"sql": "select * from `conf_menu` where `conf_menu`.`id` = '6e655b95-8adb-45e6-9bad-aedb2917aa95' and `conf_menu`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6e655b95-8adb-45e6-9bad-aedb2917aa95"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "middleware", "name": "roles", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.0005200000000000001, "duration_str": "520μs", "stmt_id": "middleware::roles:31", "connection": "juragan_beku", "start_percent": 27.982, "width_percent": 0.156}, {"sql": "select * from `conf_group_menu` as `gp` inner join `conf_menu` as `m` on `gp`.`menu_id` = `m`.`id` where `gp`.`deleted_at` is null and `gp`.`group_id` = 'ff9759b5-2dc5-410b-b86c-f8146c1f66d8'", "type": "query", "params": [], "bindings": ["ff9759b5-2dc5-410b-b86c-f8146c1f66d8"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "\\app\\Http\\Controllers\\CoreController.php", "line": 107}, {"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\CoreController.php", "line": 57}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 149}, {"index": 15, "namespace": "middleware", "name": "roles", "line": 47}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.00217, "duration_str": "2.17ms", "stmt_id": "\\app\\Http\\Controllers\\CoreController.php:107", "connection": "juragan_beku", "start_percent": 28.139, "width_percent": 0.653}, {"sql": "select * from `conf_group_menu` as `gp` inner join `conf_menu` as `m` on `gp`.`menu_id` = `m`.`id` where `gp`.`deleted_at` is null and `gp`.`group_id` = 'ff9759b5-2dc5-410b-b86c-f8146c1f66d8'", "type": "query", "params": [], "bindings": ["ff9759b5-2dc5-410b-b86c-f8146c1f66d8"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "\\app\\Http\\Controllers\\CoreController.php", "line": 107}, {"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\CoreController.php", "line": 75}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\Generator\\OrderCancelHistoryController.php", "line": 92}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.00307, "duration_str": "3.07ms", "stmt_id": "\\app\\Http\\Controllers\\CoreController.php:107", "connection": "juragan_beku", "start_percent": 28.792, "width_percent": 0.924}, {"sql": "select * from `tbl_order_cancel` where `tbl_order_cancel`.`deleted_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Service\\Generator\\OrderCancelHistoryService.php", "line": 66}, {"index": 15, "namespace": null, "name": "\\app\\Repository\\Generator\\OrderCancelHistoryRepository.php", "line": 36}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\CoreController.php", "line": 100}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Controllers\\Generator\\OrderCancelHistoryController.php", "line": 92}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00376, "duration_str": "3.76ms", "stmt_id": "\\app\\Service\\Generator\\OrderCancelHistoryService.php:66", "connection": "juragan_beku", "start_percent": 29.716, "width_percent": 1.132}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'juragan_beku' and table_name = 'tbl_order'", "type": "query", "params": [], "bindings": ["juragan_beku", "tbl_order"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php", "line": 33}, {"index": 12, "namespace": null, "name": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php", "line": 16}, {"index": 20, "namespace": null, "name": "\\app\\Models\\Generator\\OrderCancelHistory.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\app\\Service\\Generator\\OrderCancelHistoryService.php", "line": 66}, {"index": 28, "namespace": null, "name": "\\app\\Repository\\Generator\\OrderCancelHistoryRepository.php", "line": 36}], "duration": 0.00268, "duration_str": "2.68ms", "stmt_id": "\\vendor\\digitalcloud\\laravel-blameable\\src\\Traits\\Blameable.php:33", "connection": "juragan_beku", "start_percent": 30.847, "width_percent": 0.807}, {"sql": "select * from `tbl_order` where `tbl_order`.`id` in ('002db08b-46a5-44a3-8249-ec005dc974b8', '010db125-0995-4aef-9692-b67bf23115ae', '015eefa3-b36b-47c4-b31f-71993d25befe', '01a8a41d-f372-4b60-9830-40228eb060f5', '020fea02-a037-47b9-a2ff-c3cb4c7934c1', '033105dd-cb75-4cbc-8678-d92e36ca542c', '03595ec1-6682-41cc-9e72-36e180bbfc7b', '0388dfbc-4676-4d25-b5ec-ec2e77272c4f', '040ff991-cdd2-4916-8fdb-b0a51de3ca06', '045183f1-9ce9-4f9c-8a29-daf7db5c8205', '046cf951-9db1-4359-91fb-16140ebbd532', '04d400f9-81b0-4010-ad3e-73b641c10243', '04d4c81b-26b0-4873-bc9c-aa580e98e176', '04ed56c5-8609-4448-ae4b-5d3f168a164a', '0557dbed-db0f-43b2-a523-cab32e8f0ad7', '078794a5-6ba4-45b5-b350-f7a1d428950c', '07c3bc0c-fb8d-45f9-b945-74509ce9d833', '081295cd-d624-449b-8ad9-4854e8c75ffc', '082891cd-a975-484b-b571-4debeeb87d83', '0866d534-ce7a-4572-b633-e37472ad6794', '086ac7be-0df6-401d-a80c-d59a085f8cbc', '09a88dfd-eb25-44cf-ada3-85813b11e43d', '09cb47c9-472c-4a16-929b-ca11a86062b4', '09fdac73-9869-4e68-931d-6d9b4a4615e1', '0ac154bc-446b-41c6-bca3-6475f514f1c5', '0b1709ad-0d54-49dd-9142-0e492e7787dc', '0b61f35a-be43-4b33-92ac-5b7454984d04', '0b99306f-2403-49a7-a47d-ed828ec14bb0', '0bf533fd-fd43-471c-ac54-cf023ec3578f', '0c3e96f3-f46e-493c-821d-71f1944c20e0', '0c65006d-13ef-4b4c-b943-f930f5a2238a', '0cb5c6ae-63d7-404e-9910-a4af0df9337e', '0d01d080-ee62-4610-95e9-e2bcd64d8ff5', '0d18562e-f4b9-4507-b091-95c4ec344a04', '0d7451e9-1520-4d97-be1e-6ef4fe3eac1f', '1072485e-3b9b-4d5b-8093-a3a6fac9540c', '10ad17a5-3ea4-41b4-80ea-a066f77b3008', '10af44c0-c565-43ba-aeee-392cad181a68', '111abfbf-f6e7-423a-a8c5-3d7b6cd3cb62', '122a7797-33f3-4397-bf51-9b5d5dd27586', '137554d4-5108-4a21-b939-30f7a0b93409', '145cfa56-98a4-48c7-9e5a-158ac5c789a8', '151e3dc5-ced7-4048-86fb-32b0723c9445', '168ebc3b-1570-44c2-b4d8-93475b131132', '16a68abb-8ba2-4586-b136-0bba06c21271', '16b12ddf-957b-4444-9d31-85ea8badbf2c', '16ef712a-2c9c-4bf1-abac-3bd7b29c8268', '17a2230e-057c-4ea9-9787-5f630c7eecc8', '17e79d25-81c8-418f-9026-fc59517c57e8', '1840d092-1eb0-41a9-84b9-f83358fb7e6f', '185bddae-8191-4729-882f-24efd7598012', '18df8505-1f41-473c-bf2f-0e0055297401', '193f690a-348c-43f8-968f-3b6a66a058e0', '19c5bd1b-9e52-44f0-bd54-378b06327705', '1a0415cf-fc3d-433e-a07d-afbf09ed90e8', '1a6fc32a-5d70-4c04-ba12-6dc2e5798222', '1b091e99-df0f-488e-885b-e83ceed214ee', '1b3ffdae-7110-4485-9ec1-e31da6276a72', '1d4dec3c-493e-49b0-8600-d8e65c83cf83', '1e4ebaef-cceb-4d6d-86e8-ac295810c6d5', '1ee1567a-7761-43df-a4b0-b671c07fb473', '1f7eb132-a254-46d8-932a-e6f5345a4d07', '20125525-1b06-469c-a102-c8673de6ecf7', '22134299-547a-4263-9293-aa0804f47120', '2232a7e1-afe4-4188-ac47-747be77eda53', '22351c1d-3d8e-490e-8027-8bde115481f7', '227ca44e-af60-4820-93f8-60eff38c85e6', '228624a5-f559-44d0-a895-bc5d83b04f2e', '22d21874-a35b-4c86-91f5-7dab807831e5', '243cb96b-22d2-49b4-9c5d-5e6c7b273a8b', '245f7286-e48b-4b24-9505-9cd2de91239b', '2485647c-339c-47b6-8ff2-576222fab185', '24bf7b30-5767-4f3a-b0ce-7d2f3a06fc31', '24d03e3b-941b-48af-81ba-f33218a5f78a', '252256d3-e73d-420a-9f97-ea20e584044b', '25355be1-1629-48fb-aa7d-fb5e5a227305', '25ae94e9-598b-4e20-92e0-95615d0a5f99', '2617ac7e-cae7-4189-9057-31e331fbf221', '2777addf-e458-4f57-84ea-c16fdfdb9f74', '278a1ac9-8a22-4c34-b821-d2d23152c319', '27a424ee-5b5d-4516-aecd-6859b25709f3', '280cfa30-52f8-4923-9991-a9f950150dc0', '2a260987-c481-4002-a43a-88a00dcdc904', '2a316763-7208-479e-b435-a15c18e9b1ed', '2b587779-22b9-4b95-9252-d2aee5c61805', '2bdd9c04-bbb8-43c0-b62e-ecf9eb9a8d17', '2c62372d-7eb7-45a5-825f-e2d612dd67fa', '2cbed2af-e891-42e6-b460-a795564f8c96', '2d8575f1-efc9-48cf-a935-442f7418e4a9', '2ecc5049-751a-4352-baab-46ded71840a7', '2f1ce27c-3ac3-4f93-8245-5c50f26f313d', '2f4fa95a-8d39-4684-96ed-9c303088e079', '3044dba8-4da6-4169-ae1e-b50fb77011d9', '304856d1-6d21-488a-8ede-f9b8b2910f72', '305a3906-3d06-46f0-a93b-087e158f493f', '307628e9-b9e4-44c6-8694-9f1ea825edc9', '307831f7-23a7-4623-a5df-dcfe6115a5ba', '3154e814-d4b3-47f8-a49e-3c39bdcaf5db', '31eaeba8-8bcb-4bc1-b066-273003791d5a', '3348895b-caee-4cb5-84b1-2b99f570805f', '3390ec31-31a0-4d09-af59-8e1a4c2158f2', '33bca360-245d-4179-a1bb-23597505ec72', '33bd91a2-c147-4471-99bb-93a0f151aa08', '34375f83-40bc-414f-ba87-36b13f1c686c', '347102a4-6b67-489f-917e-74a9098df4a4', '35f6b4c9-4a6a-407f-a7b7-4406dbb2580c', '3616bb06-4643-48d1-8494-aeb53c13afb9', '36f65f27-563b-46b7-a3b3-7eba9df507f2', '371e77d3-c0a5-4460-9e5b-89616ef9c56b', '3726f29b-7945-483d-bbf3-1faa720cbab4', '377a0da6-7514-44e7-9aff-799a34310353', '37b05732-cb25-40f5-9955-8f5de7053598', '37b5fa09-7db3-4721-bcaf-ccefdcf13212', '38b7a6b9-508f-4e33-9a03-3b50f37f3e76', '38c45c7f-055f-40ef-a46b-1dda0eddcdea', '396b866f-fe33-4b26-bff2-2d38f576b94e', '397bb41d-e026-4886-ba03-65e6a7b86ed1', '39b12bc5-182e-41f7-a64a-835fc858005b', '39ff381b-cb79-4137-8835-0b2ca52bc4f4', '39ff6960-150e-41ac-9a60-87ee28cc82cd', '3a200c69-5fa0-4d4f-b609-e6a790b43986', '3a2d2f07-56fa-4c88-86de-9bd2a4f07d77', '3b172f4a-54cc-4d47-ab77-84e80539aee7', '3bc2c04e-7657-4576-bd8f-54f4fd2e56bd', '3c272d53-ad74-4efa-98ee-723404ab157a', '3c300c84-8744-40d5-8f6d-6322620f228e', '3c82f4be-d49e-4745-b97e-a6d3f9400718', '3e4bd26d-473b-457b-8b06-46fe89ca0fd7', '3e804e3e-e3fa-4ca0-b25b-a9863f3d3cb0', '3e8aa41b-ceb1-451d-9c88-93fd9190230a', '3ea84ff5-4489-4f1d-929b-17d0a5ed96e6', '3f4c3deb-6de1-40ad-957e-1a0e9d640e96', '40287d61-ec99-4e29-8971-011e9fe91d6e', '4045491f-dfd2-4de0-8907-d6815c1adb50', '409aae2f-3960-4fe1-bb5f-48582c594b1a', '40b6a9e3-1d40-4c59-8725-a644a31130c0', '410496dd-8e03-48cd-8971-5281cf4404f8', '417ccacb-55d9-4935-bafd-bd0df2be4a67', '4290fc56-5414-4a58-93e0-d1afefc1ec57', '42ac5721-97b7-47dc-9250-cffe968107d3', '42e27c24-8857-42ab-a9ec-b825dd892612', '431a5ce6-6ccc-4805-b06e-592e5b3d498d', '43db108c-d66e-4213-8844-7d0226c10615', '44eb9829-129d-41ac-91b9-caf41689ebb7', '44f03bcd-8990-4f22-9617-e6f6023b9e2f', '456a5510-164e-4470-b41f-8a91d800999b', '456a76bd-98ce-4ca7-ade4-d4ea643f64dd', '456a8e11-55ac-47d7-a498-9bdcb394da11', '458a3be9-0f6c-46c9-b017-68361565d31a', '45b7e019-743e-48c8-9732-6016d534f803', '46aac104-c63e-44be-9b40-2b59fa92305b', '46ec7e2c-014f-4713-adf2-22b6bcd4b075', '474f05cd-a757-4169-8190-a8f681648677', '47c5a009-7b06-4946-9e55-a515b5501a31', '47ca0ddb-43ed-4b0c-96ee-65a8eedf6528', '47d5952a-0915-403f-a876-282b1a006686', '480b62e9-ad5c-4aa1-85c8-c5e4180a13e7', '4821dc56-7efb-4d59-8e28-7abac1585e21', '48e90174-b40c-444f-a7cc-af8e9f12c9ad', '48edf79d-2885-4b2f-b378-e185a591e626', '4952c61b-9607-4fd1-9715-1720c5e2c00c', '49cab3ee-197d-4856-ad9f-de50c4109323', '49ddc9bb-8b72-4ce3-befb-95e0d93bb64e', '4a22259d-d62f-4a5a-9aa9-c929e447fb92', '4a360a50-e630-4b30-8a99-3b4b30841184', '4a37b19b-5593-41c6-8a8d-32dd356aa029', '4a5efe5b-3d44-46f0-8309-5f741eabd4ca', '4a7a668d-b8d0-447f-aba5-3f5b355f9f5c', '4b163a63-ff24-4af7-9155-994cff7217e1', '4b9dfa7e-fdb1-405e-8cb7-4a383552f2f1', '4bc41301-fdad-4663-915f-f7691cdf7e69', '4bc92dc3-66cb-4832-95c9-8168e27c029b', '4d90020d-253c-4569-8717-89a1acf669fd', '4da6c001-f57c-4c82-8624-d2ef95047a11', '4e838c00-2fc1-45f3-8afc-d149dfff3643', '4ea9eb9d-ded2-496f-9643-053042d21f97', '4f25c9dc-9e33-4463-a6bf-601655c2593d', '500c419a-d285-4dd4-8de2-3a6acea0ec0e', '50475e12-3269-405b-ad84-b1916e1d9299', '5080f083-76a4-4e40-8a5b-2d15601ae9c0', '50f58d32-bda4-4a90-b2e4-9a902e4106d7', '51197598-1b0a-4961-9d44-d21f156ec54e', '514b9870-2ef6-4692-bf8f-2a0e53546c6b', '515251da-4061-471c-ac1d-772dbb1650d3', '51c641cd-eac6-477b-bf77-6224e0e414d1', '51db9553-686f-460b-aae3-cb0d6de9e93c', '52ad3256-31ba-4cf2-81b4-3e192fa508fe', '53479271-77d7-471c-b144-5dcbc737535e', '537f1632-93df-4b8a-835b-f7a9a1a6c925', '5428447b-e926-43cf-9705-a7e060f99946', '54b967da-2aaf-4107-8786-1ca4b3b35c92', '5571c673-feb3-4488-9fd5-eb2559c53d46', '565dade1-2e50-4e64-acf2-f5a33c88651c', '569b6151-2b73-4c9a-a984-1e0f9f7eb0ff', '56ce0314-30da-4a21-ac40-50122faf4122', '56dec9d8-1d4e-4a19-a48e-3aac73c11534', '57e445c5-6782-4739-930f-abfdf8fd089a', '58a38ef3-12be-4eba-b39f-84f22e55d6dd', '58f6bf3a-0cef-4e71-9ddb-bfbf23bb92b3', '591f8668-6957-494c-bd17-49e202aa2e00', '59cc3a4d-79a9-465f-9d45-4e9d0860d107', '59eb492a-13d5-4ced-af62-709fa61a8b5f', '5a513ad9-8af6-4a23-8ef1-b7067d04fe9e', '5a6730fa-9b66-4c54-88be-d8de76ed5376', '5af9debd-b2b9-409e-b7c2-fd891c5ebf11', '5b09bf29-16fe-4a5b-a95d-210482c43080', '5b874efe-379b-4096-8ee0-6124f84c2585', '5c1ed8f8-8a2d-4acd-b2fe-4b1609261771', '5cccc4fc-0e11-4055-b872-8d0896573e49', '5d03a99e-f109-4434-9b87-5b6fe926d6f5', '5f6c6c85-a88d-4d47-96e5-227c227d9ba2', '611600ec-bd0b-44d7-b9b7-7dc46a3d8e4e', '61480f13-f0b0-45b1-929a-058941387694', '6196e0f9-d2dc-4eed-8944-9399e7714fb4', '62d1dc7a-e934-46fe-933b-8825a583c30d', '62d45185-1120-4a1b-ab93-b76d5c10bac2', '635fc1dc-4324-4136-a8cd-cc963e56f74f', '63bdff14-77de-46c4-acd6-6a123a27e233', '64b27adf-b1db-4626-a3bc-f4d7c0971562', '6557be1e-f684-4e46-a49d-bea635532fa4', '655f9522-5100-4492-a671-14e0749fc524', '662d41d5-780f-426f-9638-096058ec28fb', '664437dd-550f-42e9-b689-4ca071f8610d', '66d77c7e-8e4f-458e-adca-be20b8e7379f', '671f9fb0-1c0a-4278-9f2a-d968eac98886', '674ac4b3-c936-4d22-b118-0c1542ad6f3c', '67b13eb1-fa8e-49df-919c-d7a76c49e44d', '67f615a4-c7db-4fde-b3ac-0bc26cd2f9dd', '67f9a674-8d4b-4e78-af7a-9497f9927b42', '683aaa4a-b0a7-41e8-a005-d0ada3674fe6', '68658f86-485c-4587-9f22-7b6f99d2a900', '68a68f86-effd-4b4b-a8e2-9f7538bc8497', '6948692b-c830-4199-b020-d7f30fadc74b', '69957d3b-b5c1-4c1f-8666-a020766d384c', '69d91281-2d6b-4dec-81f1-a5f5abc52af9', '6acc6c25-1561-4ba5-b275-155331c89821', '6b80276c-bcb8-4c49-97e2-4d4389220fb7', '6bd6cf82-c39b-4f2c-9109-406fe13a9c15', '6c14d0cd-288b-4848-b679-91a51e68d340', '6c6f7a27-177b-4626-b255-8d55f725d74e', '6c8b8252-315d-4bd4-8b61-e574fe1413c2', '6cd90825-871c-49c5-bf7d-5eb5f4645b33', '6cf8c428-3fad-4b53-9ef5-4c719802f541', '6d29e6b9-eb9d-482c-b076-1231fa674901', '6d7e22c6-c4c5-40a1-b7c0-402de4e65855', '6dae6954-5780-4d9b-be4e-3e68888d625d', '6e187fa5-a30a-4756-8abf-32b144504010', '6e50b7c8-1652-4688-8da0-daae44990020', '6ef173ec-3e3f-4dea-aef1-587af7ab1350', '6f2124d7-3c1b-45ee-84e6-06bf4115310d', '6f4bfaaf-2f44-4a70-8f5d-949443d5169e', '6f8d91f0-0c5d-4708-81ec-35479a83675c', '6fb470d5-b52f-4ba8-99f0-f7889f56be83', '6ffba926-a96d-44be-ae8d-0a2c7b9c5471', '70049b44-ffae-421a-afeb-ce3eebaa2010', '705eb389-1f0c-40a9-977d-6b8ccca1f565', '707fed9a-f1c8-441d-bb5b-b9ead7218027', '70f58448-819d-4140-ba69-9cf60c6a51ba', '7191fbcf-b259-408e-bc17-7458aeb7b568', '735733d7-482e-4172-9cfd-b49d9827ce8f', '7398d93b-21ec-4bd2-a572-fb3ca67d1786', '73acc81b-c5c8-482a-b290-0b4ff4d06212', '7427d348-24a3-47b5-93a9-17d5233a4ec9', '7471a870-ba60-4855-95f7-5f5272a5bf40', '7528fc28-769a-4b9b-acf6-31b2072bc64a', '754f2995-9e99-463d-b21e-da2a8f5be818', '755f8f15-a1d2-464f-a6f9-bca086c405b9', '760f21d4-f00b-42b8-836a-400766619bc5', '7662255e-0a62-49b9-bc15-df59047c1a68', '766e0fef-e081-4b5c-8e03-f3ab4de4e8b3', '771316c2-f7d6-40da-9aa7-ef7297912987', '77a86135-2edb-49a4-8433-a58a76332bf0', '77d8946c-2576-4cd0-9b62-60c0d172653a', '789c6dba-b66d-4739-b996-f740518116f1', '78da52b1-8a9d-4af3-b8f1-a5af40d99318', '790a1048-8fc7-47ef-816d-f0f7f26ede3b', '790c52bb-368c-4cc5-8aae-9ffebe3c6ca2', '792e6b22-c1c9-41c6-be33-5ba6f1c4ac24', '7935fa3a-3dd0-4c66-aa96-7dd16bcc6d86', '7959fd56-4759-4e13-b59f-66cdd2577971', '7971956f-eb7e-42e8-82bf-20aa4eec3730', '79c540e8-7869-441b-a080-8609a0a10509', '79ec818f-69ea-42c0-a65c-9dac41bdad9e', '7ac8cdc0-b8f3-4390-bf25-7c692df664c1', '7af1ff01-076e-4bcd-913d-c625e9ac0124', '7ca8f6d2-5d7e-4bac-bca8-d1850090406a', '7ccae1b7-dbd1-475b-8c5f-160a4b6922b4', '7e0c39ae-ff12-4865-9faf-dc8c197ee8a8', '7e7ce528-ee55-4fbc-bf72-ed00bc71f75e', '7e8d3223-f7f4-4129-b5fa-ceb25b14e4c0', '7ebfca45-bddb-43c3-9faf-292bc2d2546e', '800a4aca-de82-4f3c-b92e-1c8c02a8cf99', '80c2ece9-3058-4411-ac12-766e795a9cec', '813a969e-d3b1-4511-b355-657f7295b06a', '815e7594-2449-4b8c-abd6-921acaf3838f', '817dafcb-4166-44af-8a88-87979a114307', '81fe3a5e-98f4-4681-bc03-7e3e6f662e97', '83c916d7-fbed-4944-904e-c142c05c8f5b', '83f0b042-0f42-4822-8904-eec97e950919', '840f133a-a385-4bdb-96be-4ea34150772a', '8469a7b5-b6e0-42fb-a785-f9ae29c70fff', '84c48cb0-6546-4b0c-8f9e-d9e3d06c229f', '86783be2-ba12-47a3-8335-b391517bb419', '8692179e-fdfd-4622-a4a8-0efa61917dc8', '869fc17f-2613-42a1-84ff-3d91e5e05c80', '876a8bff-2109-45e4-86db-e9449a139c58', '87ba7521-01cf-497d-bb81-a5783454aa9b', '87c3b5d9-1c47-4a8a-92a8-5beeeed99ed5', '87f899ce-bd03-415d-8b02-d6e9c0beeeb3', '882f0c63-3118-43e1-a9c1-23e113873e97', '8863dd8c-6074-4d19-a52a-e7e8438a53da', '88956826-a602-44e3-ba44-0cb08bd6e054', '89dba837-ef91-4d1e-ac3c-2b75b853ce85', '8a5613a7-719c-40d8-9553-999c1cf3e4f2', '8a610b22-fc72-4380-ad1a-2f8cca5ed25b', '8a8dfdd8-169b-44df-9767-410883686ed6', '8af158d9-d5a5-4fb3-86af-59a5519f352c', '8b0f7e47-d007-44bd-9575-428e008ba89f', '8cf3af7f-0544-4fb1-8c22-4c4f09ac3af8', '8cfa7e0b-6d9d-4791-8004-3aaca50c12e9', '8d02de6a-aa09-43ad-9789-d8788bbee6a4', '8d2da0aa-98b2-4068-839e-218ac39b6653', '8e177171-d778-4336-a516-62fb0934ddfe', '8eba0816-e340-4896-a5f8-c6976cf4a5a9', '8f0880c2-0d27-4afb-842c-4b22be1f52e1', '8f3468c6-9fd7-4acb-93ac-e084245ac805', '90536053-cffc-46e0-86d8-fc2c9f97bc09', '91459e13-42d1-458e-87a3-d99c7b361ca5', '91a0fba6-0f6e-452b-8387-fbe623e48124', '924f66a2-89ca-4850-8197-9c10f9a1fbe4', '92a6b5d3-4602-4651-a5b8-a67fa0dc77c8', '92cd0b49-eab0-4483-98d2-c156689d8d12', '92f5495e-6ec5-4984-913b-15a4a0f9baf7', '9334abb9-d64e-4033-917f-8d19b65c167c', '93501315-e8f6-4d8a-8ac8-4b70deab096c', '939f4d07-bfc0-4785-84a4-702503673018', '93a7ad5a-5721-43c7-844f-d4c028fed771', '93ce78de-fb31-48f3-ae00-91b8725e2b13', '941c060d-f4bb-493b-84d7-629e211896f6', '9435f03d-29ee-4f57-b4fd-d470a7e5fc72', '9442be5e-b4fa-461e-8206-68bf4d149e2c', '948ef801-5d92-494f-b338-ae0d6992030a', '95843ea1-2b75-488c-94a7-84dc7f3db139', '95f4dae1-ef00-4b7d-957a-7b24296f5f35', '96bc5fc8-e863-463c-8db1-8a943e2aa048', '96bf99c3-aad7-496b-b342-3a7471869d5f', '97976636-e266-4fba-a173-3e85245391de', '97ecb2e7-29fb-4337-9f48-c0fe0e4ebc38', '98288de7-2b93-4dbe-bf4b-83fc9abc6d34', '98411435-7d69-4366-90ef-e1a70157c67e', '98c25e64-d0b9-45ff-a535-c841ac318133', '99ace31a-84c5-4d2e-91d8-0601595664a6', '99e794d9-b669-44df-bf3f-3005eb1ccd36', '9a193546-c08a-484f-b1f0-1be688b94ef7', '9a257f45-c1cf-4213-bfb1-f66a4eb11b0a', '9a6ac261-7e72-42cb-b450-d7652ea3bf55', '9acd1a79-d36f-4e78-bfc9-1d9b63492d97', '9b52faac-f153-4231-8e46-9026dbcda662', '9bb671d0-5bcb-4f9d-b0e9-61d3c3cd89cb', '9bdf370a-05e2-4d62-aa7d-1e93cc1b4d2b', '9ca6583f-2649-47ec-89f2-eb134882c5dc', '9d3f7ae0-6317-4c4a-80af-d2f4216d6f07', '9d8538e7-0738-422d-8fbb-843fed6887ae', '9e08efc6-ec71-4cd0-afd3-3c16672f0ed4', '9e3cd2cc-ff40-4a89-9b8d-0f2211821787', '9ebe48d4-def4-45b6-b299-0046bdf2412f', '9eea4bb6-e7bd-4c9c-9159-f86119cc2340', '9f82d69c-e6b2-4680-b540-5e56baf0d234', '9fa938de-9cee-4f09-bdb5-dda37181ce3e', '9fd83ad8-19d6-467e-a34a-551a8b182bff', '9ffb90ce-cb83-4e00-bec7-62c5962caaa6', 'a03fefa8-af90-4ab7-ab50-97a9cbcfbbe3', 'a0f3366e-f7a7-48ad-a20f-bb6bcbe97dfb', 'a1236bd9-75d8-4ac2-89d5-2fbeeeecc4d7', 'a2b7f17d-be3f-4d45-a7c0-f4f1d96ae41a', 'a2d4f9e4-8389-4f06-8b1a-06cb034cb4c8', 'a3bfdd60-3e99-429f-9c12-49b4a592156e', 'a41d4d90-2c38-4e95-adc5-851826a62a1b', 'a43f6f5a-d768-4a19-8e29-5bebde3b4a03', 'a4706d4c-6e29-48d1-b967-52f3445b88cc', 'a4baae25-8e29-4cf6-952f-ed7d5921b5a1', 'a597d94e-67c1-47d1-9d71-ee2ffbc20cc3', 'a5aee176-0b57-4558-b8a9-0fc515c2cb24', 'a6452741-bf1f-457c-b14f-873ce7cf93e0', 'a65005a5-010f-415c-8f5c-2b14f08ed8d1', 'a6b417fa-739a-4e3e-b638-7dc8dd3f0bf5', 'a6f91e87-d40a-4407-bacd-1f38921bbf71', 'a734968c-f720-4a6d-b9ca-90ea9cfc550a', 'a773b473-0210-495f-b599-05531190470a', 'a78535e9-1f8e-4805-97a9-fa5fb5e273cf', 'a8ad7a90-5d86-4643-87fa-96963f50b2ad', 'a938ea33-2e65-4b37-8b97-1be9134fef7a', 'a9527021-c33e-4fb1-aab6-c4d9a11da4bb', 'aa168e29-2f3a-40a9-913f-21c5d99061b8', 'ab3f49d0-c5f6-4472-8356-5ea429fd025b', 'ab75a8a3-d472-426f-b4c1-a7f0fc17d1a6', 'ad3d7dbd-2efc-4d53-af48-94ee81c3db8c', 'ad8ff5fc-e719-4a6e-868d-d22cc02f8f2a', 'ad909fb3-6e4e-4d04-a314-f4b4668bd988', 'ae18110f-703e-438f-a07e-3c04e3e42118', 'aeea1aa3-f7ce-4d26-a767-9ae080092f91', 'af2c6f7f-c006-4fa6-8f33-6c37a4d9ec44', 'af3ff33c-42da-446d-af61-d82640a550f7', 'af44a142-8528-4cab-8cca-2479fa01b405', 'afae0877-7d6b-49b2-b915-8c46b25e7a17', 'afd79332-4625-492b-95b8-49e0d45d264f', 'afdfb121-5c79-4ee7-b373-97b07b09cb9d', 'b0149b36-301a-4a5b-8f02-f6cece67ae40', 'b0660ab9-f0c7-4a30-9c9e-01f4558cff82', 'b18366d1-789a-42e3-9632-64cfc1d067bb', 'b185f6b2-e8c4-4fc6-bc3d-ae615ed974bd', 'b1bbf9ea-4515-44c4-9b63-0445f531bf11', 'b2c0fb18-0eac-454e-be9f-4826aeb5c28b', 'b37ec435-fb90-4b5f-aac1-27e70ac173ab', 'b37fb115-d062-41c0-96d6-635a0d653412', 'b492c9aa-a8c5-4210-a9a8-b83edd2cbae7', 'b4afea5a-a177-4dfd-b0b1-7516c4028a03', 'b4c410b4-18c5-4586-867a-7d32738cf175', 'b4ed7c3e-9954-4ae8-a935-d834a7797e74', 'b607854b-d793-4916-8350-47eb4357eb2a', 'b62d3435-469d-409d-9e96-0b7e239fc4d2', 'b644129e-94c8-4da6-92aa-afcdf8a8bac4', 'b6c75b4c-ab71-4391-9b24-7d1dd2c4ac35', 'b7fde35c-4d78-4c25-a4a0-61755aad0d0c', 'b8bdd44c-be4b-4a79-b427-e87fee157334', 'ba526c09-72c7-4fa9-a49d-68fda111b6ed', 'ba8b078d-b3d2-417d-b015-daac6e411480', 'bac585b6-69a6-4967-aa91-8e7329c8d392', 'bc30fe8d-aabd-43c3-85b3-0e730f9e27f2', 'bc4057e8-97ef-447e-9aa8-4853b6a77f8a', 'bd0c935d-573c-4267-bda1-7960d1782949', 'bd4ca201-5a18-4f4c-8ec9-dfa5d78f1d98', 'bdb4dcfe-bfe7-4560-b591-cc7683356da7', 'be222ebd-863f-402c-a82d-351cb3bfefbf', 'be48e5c8-9c10-4e2e-adea-c15291ba0b30', 'be7a1dad-04a9-47c6-8d3a-1d2500e929fa', 'bec02306-1da1-4f3a-b871-42a3712c52d1', 'bf00bf1f-5d21-423b-9854-dc89428a97c8', 'bf5d6180-e854-4033-ba73-0cc439e08f34', 'bfbbb483-e162-4304-a77e-9934f0a4d601', 'bfc0e8ae-2fe2-440c-859d-58d3e90e1ba0', 'bfda0a8d-f89d-43cb-9dcc-e8cc021361b0', 'c0d01d45-13c3-4472-8230-66f3f5b8a10a', 'c10b8cdd-31e1-4684-a6b3-bfdaa480ea33', 'c1a86efc-cc12-401c-ae7b-741a7f1c773c', 'c1fa454b-1cc8-4554-8722-468ba81a0b88', 'c20e64d3-3f5a-4f04-bb69-81c419010e71', 'c227e31a-96fb-42f1-936f-31b6f4c7dc13', 'c269165b-1481-4f55-a1ed-322ea2dc052c', 'c2fd3515-db6f-4c49-b2b4-e0421098b393', 'c38ce383-a1fd-45d0-8d97-cb11889a20cb', 'c39d0681-9ed7-4c49-ab0e-e08aadc8a28c', 'c3d8a435-6538-4b74-ad73-79d5574dc4b6', 'c43b36de-360c-445d-9a10-151e0980c158', 'c46449b5-355e-4ac9-bc31-2d4506629a29', 'c498afe8-4b6c-4b1c-9424-e93d1afb8caa', 'c4c0ca04-0d43-4f04-aeaf-04f2dc851db4', 'c537e2e6-0100-415b-ab5c-9da8d3c61596', 'c6fcf7cc-c47e-42a9-a0b9-381cebea7e95', 'c805c67e-00dd-4f16-9b77-d58bea61b122', 'c8260c48-997b-4e70-aaf9-07da4a06eec6', 'c8ba93ea-f481-4d67-83a6-555377d1fed3', 'c8c87ae2-450f-4487-ad99-e457facc0143', 'c902cda6-2de6-4718-a4c0-4e0ecc8eb055', 'ca3a8b45-95a0-437f-a6da-cecc9fbf5356', 'ca62be8d-c1e0-46d1-bcc2-8152b0d35838', 'cb336031-0958-48af-8a15-c2f5eb2215c3', 'cc64042e-7f1e-401a-98b3-80ca554a6609', 'cd3edfdc-a65c-4345-b0aa-ff29212efd6d', 'cd4582c8-1aea-4b9e-a7cc-5f6584c75d01', 'cd55118a-b428-4ea5-8b05-690eb507e42f', 'cd7143c8-3fcf-4c28-82bf-de778231da01', 'cdc4eed4-17ec-4f27-873b-833450b0d82d', 'ce8a4f29-fcad-4a8f-9809-510756bddb5f', 'cec010e3-cb2b-4a65-94c6-f7b1258b2cd1', 'cec9a32a-87a9-4e3e-8282-c165f7d82f83', 'cf1780c4-8fbf-4b3a-ab75-94284f9d358b', 'cfb83ec3-2fb1-449a-9405-9d723c20ff6b', 'd01f9e8f-349f-4e79-b870-6e3c34140b4c', 'd069e259-7ac7-40cc-b820-84f21ed06d72', 'd07ba739-66a8-4384-a44e-e1502b75a26f', 'd0a337c5-301f-4055-ae2c-20216e6838a6', 'd10a595a-5242-44bd-8833-029c8eb9d138', 'd13db0f8-8f02-4a60-b91c-d79fa10a62b5', 'd219f6ea-6742-45ed-8ff5-ac108926f4c6', 'd2b0e2fc-20f6-4797-b9bf-98c9f394f763', 'd3889f62-78cc-4fec-af0a-064bca631119', 'd426c19b-0132-4e4f-a5c0-8d3795cb208d', 'd53b6bae-3ac4-4dd8-b537-842f276c4204', 'd5784d0a-3a8c-4a0d-b8d4-6b9c84fca603', 'd5c3f537-9169-4edc-b7cf-0360bc3e996b', 'd8efa3ff-588c-47ea-8884-69a4107fe3bc', 'd954ccc1-e6c7-492f-8304-2b281b0ca186', 'd978706d-f8a2-4063-92a5-714fd99294d3', 'd9be7115-6515-4514-a466-8bd828d56d0d', 'daa6322e-f83f-43fb-bdf1-5371a7531c78', 'dae10db9-c4ff-4705-a3f6-ffefcd760af8', 'dc8a1752-20df-47cc-b170-1771713216d4', 'dca7f194-cc63-46e0-8f79-c4b967bb8cb9', 'ddad4d99-07ef-4523-848e-e9718ba789a8', 'ddf910aa-3091-44ff-9572-f6250de2fa84', 'de22014c-65cf-4b35-970d-6f408e97eb00', 'de620fff-3821-4aef-a849-d992d5dc82fd', 'de9b36ef-9c54-48a6-ac95-f1fa5e44ea9c', 'df0696ee-b0e6-48ad-8fe6-c1354fb4cfd2', 'df8e0afd-538c-48b2-a8d6-6551427851ae', 'dfb0ca94-8d2b-4cb0-9c7d-b81b95263fee', 'e05f0a8e-7b95-4bf8-8a5e-3751d1664cc6', 'e06244c8-6156-4a57-873b-8284ceb7c7d2', 'e0e3563e-044d-4e9d-9fe9-6bde5782f985', 'e1076621-1acb-4c90-afce-579315d244de', 'e154aede-89ef-4fcc-b7e9-cf21650f2802', 'e15fa181-c01b-41fc-acf4-43060d0c2ca2', 'e19645a5-5e4b-472a-9da2-4ad2b5ea8917', 'e200c023-6f44-4f32-b0a5-b0797ef73b73', 'e27d3c72-82ad-43b1-a6d4-d628f9d58a9f', 'e316d391-86f3-423e-a1f9-e3a95d600445', 'e3204393-0844-44ca-a1f6-85fff0f5c7f8', 'e3433c25-0b71-4c98-99e7-a6a059d45586', 'e3f74e0c-1659-4147-a2ea-3d333d2a96f5', 'e4107c0a-8a8e-4c10-9016-64cc85dd76d5', 'e49c90d5-5a78-4827-90f2-acb57d84998f', 'e4a0991e-d6e3-49ee-84fb-6aa588c38497', 'e6349ba5-93b7-4078-a81a-9db2cdf268c0', 'e706da79-2593-44eb-a302-50cea3259f13', 'e73ea7fb-ec52-42b9-a43b-627f9ccb2a57', 'e7c12c4a-8f08-4d9e-925a-892fd5de72c9', 'e834c6d2-967a-4057-ad0a-a16486afc2b8', 'e846d880-3838-41ad-9aa5-1f365d959ad8', 'e8e54672-6326-400d-8ec8-283d03493e12', 'e96d90b9-db63-4284-99cf-2d5312bee283', 'e9b32641-b62a-4b6c-87ef-3bf77961848b', 'e9eae364-db62-4849-85e0-ab36493eab75', 'ea21ca67-5ca9-4c19-80b6-dc317d541128', 'ea7c3a6c-ba2e-4302-8a37-80d00f51db16', 'ea93bd49-cb67-4bdc-b926-430bb1b5d3b3', 'eac0b022-772b-42f4-a10d-ee368e31b182', 'eb4fbb4c-8bf8-4dfc-963e-9a92b1b3f6b4', 'eb50a798-0a58-4fb7-8b53-38d88fbda5a8', 'ec2adc2e-95bb-4756-9a7c-54f41c6eb3bb', 'ec6ccc81-5ca8-4a82-841b-ec3a4003d8b8', 'eca1ff0d-8126-450a-8a09-dd2dc0bb86db', 'eca8b817-670d-4a72-9ebc-9a074d69f8f1', 'edb8b189-f6e0-40d3-ad8a-ed0ff1100697', 'edea6965-e80f-4c46-8163-8c9a6ff434ae', 'ee21a9ce-f29e-4831-8731-3106b9f21524', 'eed47971-e8cd-441a-8fcb-4597747b96dd', 'f0fd0526-0002-4f6d-b320-6369b5731508', 'f145c92e-7498-4f8e-a313-3a0f2ae5d5e5', 'f198a5bb-c177-4b70-b04a-ff3a4fd7cba5', 'f1bfd29a-e884-454d-9ec1-5bc1482fcf5f', 'f1f88ad0-b8d2-4173-9704-a98f9f3c7018', 'f307a4c0-0b82-436b-a31c-b1bc6273a4d7', 'f380ea34-0888-4049-b690-c81ff1882f86', 'f4842a76-0e82-4d7d-b279-27fb8a430fdd', 'f4d70926-0cbb-40c1-93ee-ffea2601ea66', 'f4e10e0e-ffba-4635-bdd6-5d0480029895', 'f5ddc1ac-1d77-4957-9f2d-0c617b00bb3a', 'f663d2c5-5042-403e-911c-4a85ec438dd4', 'f6880204-fb7b-4667-adbd-15e0cb59d9c7', 'f6f5ee37-deb8-4217-9bd1-e244ed29c0ad', 'f757e8cd-01c3-440b-832a-c71ecff4c424', 'f773cddf-bd5d-4c60-9287-26b9afae2b17', 'f82bb892-c3eb-46ae-ad55-6049f96449af', 'f84ba984-a4fc-4eb1-8d88-b48d23a6946e', 'f8ae3a70-c61a-4389-abf4-7fae1392463c', 'f8b840e2-3f5f-43f4-aff7-6bbc98912426', 'f8c3b842-e274-4443-ab96-a7caabcbafed', 'f8c67727-943d-4426-970a-5ecbcd582671', 'f92adef1-43ee-4d96-99b1-61b76ed252dc', 'f9e1e2af-8652-4219-bb22-b938df27fa4f', 'fa28a464-554d-41cc-b349-e53dacd84e59', 'fc4464fb-258a-4dc6-b7de-8cc568f96c6b', 'fcb0e366-9da1-4888-a9e7-c45607f0e7cf', 'fda2a7f5-0f0a-4f8b-acdb-728d6c3be25a', 'fdb0093e-094b-479c-91fe-1867dcaeb796', 'fdbd6a93-1e0c-41ea-82d0-369da0a85d37', 'fdc3f009-d502-4432-8508-582e0ef7b16e', 'fe12b6eb-d0dc-4748-b980-dab1515e41bc', 'fe257bec-5709-4159-ade8-3cdd956c7c00', 'fe921428-7f2c-473a-ac62-d4f68b08f06f', 'fe9fd289-7eaf-4f2f-9bea-7b22d4ca0648', 'feb8c163-517f-49aa-bcb2-ad0233b374f0', 'ff4c5a7f-47d1-409d-b333-15fc9d984698') and `tbl_order`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["002db08b-46a5-44a3-8249-ec005dc974b8", "010db125-0995-4aef-9692-b67bf23115ae", "015eefa3-b36b-47c4-b31f-71993d25befe", "01a8a41d-f372-4b60-9830-40228eb060f5", "020fea02-a037-47b9-a2ff-c3cb4c7934c1", "033105dd-cb75-4cbc-8678-d92e36ca542c", "03595ec1-6682-41cc-9e72-36e180bbfc7b", "0388dfbc-4676-4d25-b5ec-ec2e77272c4f", "040ff991-cdd2-4916-8fdb-b0a51de3ca06", "045183f1-9ce9-4f9c-8a29-daf7db5c8205", "046cf951-9db1-4359-91fb-16140ebbd532", "04d400f9-81b0-4010-ad3e-73b641c10243", "04d4c81b-26b0-4873-bc9c-aa580e98e176", "04ed56c5-8609-4448-ae4b-5d3f168a164a", "0557dbed-db0f-43b2-a523-cab32e8f0ad7", "078794a5-6ba4-45b5-b350-f7a1d428950c", "07c3bc0c-fb8d-45f9-b945-74509ce9d833", "081295cd-d624-449b-8ad9-4854e8c75ffc", "082891cd-a975-484b-b571-4<PERSON>beeb87d83", "0866d534-ce7a-4572-b633-e37472ad6794", "086ac7be-0df6-401d-a80c-d59a085f8cbc", "09a88dfd-eb25-44cf-ada3-85813b11e43d", "09cb47c9-472c-4a16-929b-ca11a86062b4", "09fdac73-9869-4e68-931d-6d9b4a4615e1", "0ac154bc-446b-41c6-bca3-6475f514f1c5", "0b1709ad-0d54-49dd-9142-0e492e7787dc", "0b61f35a-be43-4b33-92ac-5b7454984d04", "0b99306f-2403-49a7-a47d-ed828ec14bb0", "0bf533fd-fd43-471c-ac54-cf023ec3578f", "0c3e96f3-f46e-493c-821d-71f1944c20e0", "0c65006d-13ef-4b4c-b943-f930f5a2238a", "0cb5c6ae-63d7-404e-9910-a4af0df9337e", "0d01d080-ee62-4610-95e9-e2bcd64d8ff5", "0d18562e-f4b9-4507-b091-95c4ec344a04", "0d7451e9-1520-4d97-be1e-6ef4fe3eac1f", "1072485e-3b9b-4d5b-8093-a3a6fac9540c", "10ad17a5-3ea4-41b4-80ea-a066f77b3008", "10af44c0-c565-43ba-aeee-392cad181a68", "111abfbf-f6e7-423a-a8c5-3d7b6cd3cb62", "122a7797-33f3-4397-bf51-9b5d5dd27586", "137554d4-5108-4a21-b939-30f7a0b93409", "145cfa56-98a4-48c7-9e5a-158ac5c789a8", "151e3dc5-ced7-4048-86fb-32b0723c9445", "168ebc3b-1570-44c2-b4d8-93475b131132", "16a68abb-8ba2-4586-b136-0bba06c21271", "16b12ddf-957b-4444-9d31-85ea8badbf2c", "16ef712a-2c9c-4bf1-abac-3bd7b29c8268", "17a2230e-057c-4ea9-9787-5f630c7eecc8", "17e79d25-81c8-418f-9026-fc59517c57e8", "1840d092-1eb0-41a9-84b9-f83358fb7e6f", "185bddae-8191-4729-882f-24efd7598012", "18df8505-1f41-473c-bf2f-0e0055297401", "193f690a-348c-43f8-968f-3b6a66a058e0", "19c5bd1b-9e52-44f0-bd54-378b06327705", "1a0415cf-fc3d-433e-a07d-afbf09ed90e8", "1a6fc32a-5d70-4c04-ba12-6dc2e5798222", "1b091e99-df0f-488e-885b-e83ceed214ee", "1b3ffdae-7110-4485-9ec1-e31da6276a72", "1d4dec3c-493e-49b0-8600-d8e65c83cf83", "1e4ebaef-cceb-4d6d-86e8-ac295810c6d5", "1ee1567a-7761-43df-a4b0-b671c07fb473", "1f7eb132-a254-46d8-932a-e6f5345a4d07", "20125525-1b06-469c-a102-c8673de6ecf7", "22134299-547a-4263-9293-aa0804f47120", "2232a7e1-afe4-4188-ac47-747be77eda53", "22351c1d-3d8e-490e-8027-8bde115481f7", "227ca44e-af60-4820-93f8-60eff38c85e6", "228624a5-f559-44d0-a895-bc5d83b04f2e", "22d21874-a35b-4c86-91f5-7dab807831e5", "243cb96b-22d2-49b4-9c5d-5e6c7b273a8b", "245f7286-e48b-4b24-9505-9cd2de91239b", "2485647c-339c-47b6-8ff2-576222fab185", "24bf7b30-5767-4f3a-b0ce-7d2f3a06fc31", "24d03e3b-941b-48af-81ba-f33218a5f78a", "252256d3-e73d-420a-9f97-ea20e584044b", "25355be1-1629-48fb-aa7d-fb5e5a227305", "25ae94e9-598b-4e20-92e0-95615d0a5f99", "2617ac7e-cae7-4189-9057-31e331fbf221", "2777addf-e458-4f57-84ea-c16fdfdb9f74", "278a1ac9-8a22-4c34-b821-d2d23152c319", "27a424ee-5b5d-4516-aecd-6859b25709f3", "280cfa30-52f8-4923-9991-a9f950150dc0", "2a260987-c481-4002-a43a-88a00dcdc904", "2a316763-7208-479e-b435-a15c18e9b1ed", "2b587779-22b9-4b95-9252-d2aee5c61805", "2bdd9c04-bbb8-43c0-b62e-ecf9eb9a8d17", "2c62372d-7eb7-45a5-825f-e2d612dd67fa", "2cbed2af-e891-42e6-b460-a795564f8c96", "2d8575f1-efc9-48cf-a935-442f7418e4a9", "2ecc5049-751a-4352-baab-46ded71840a7", "2f1ce27c-3ac3-4f93-8245-5c50f26f313d", "2f4fa95a-8d39-4684-96ed-9c303088e079", "3044dba8-4da6-4169-ae1e-b50fb77011d9", "304856d1-6d21-488a-8ede-f9b8b2910f72", "305a3906-3d06-46f0-a93b-087e158f493f", "307628e9-b9e4-44c6-8694-9f1ea825edc9", "307831f7-23a7-4623-a5df-dcfe6115a5ba", "3154e814-d4b3-47f8-a49e-3c39bdcaf5db", "31eaeba8-8bcb-4bc1-b066-273003791d5a", "3348895b-caee-4cb5-84b1-2b99f570805f", "3390ec31-31a0-4d09-af59-8e1a4c2158f2", "33bca360-245d-4179-a1bb-23597505ec72", "33bd91a2-c147-4471-99bb-93a0f151aa08", "34375f83-40bc-414f-ba87-36b13f1c686c", "347102a4-6b67-489f-917e-74a9098df4a4", "35f6b4c9-4a6a-407f-a7b7-4406dbb2580c", "3616bb06-4643-48d1-8494-aeb53c13afb9", "36f65f27-563b-46b7-a3b3-7eba9df507f2", "371e77d3-c0a5-4460-9e5b-89616ef9c56b", "3726f29b-7945-483d-bbf3-1faa720cbab4", "377a0da6-7514-44e7-9aff-799a34310353", "37b05732-cb25-40f5-9955-8f5de7053598", "37b5fa09-7db3-4721-bcaf-ccefdcf13212", "38b7a6b9-508f-4e33-9a03-3b50f37f3e76", "38c45c7f-055f-40ef-a46b-1dda0eddcdea", "396b866f-fe33-4b26-bff2-2d38f576b94e", "397bb41d-e026-4886-ba03-65e6a7b86ed1", "39b12bc5-182e-41f7-a64a-835fc858005b", "39ff381b-cb79-4137-8835-0b2ca52bc4f4", "39ff6960-150e-41ac-9a60-87ee28cc82cd", "3a200c69-5fa0-4d4f-b609-e6a790b43986", "3a2d2f07-56fa-4c88-86de-9bd2a4f07d77", "3b172f4a-54cc-4d47-ab77-84e80539aee7", "3bc2c04e-7657-4576-bd8f-54f4fd2e56bd", "3c272d53-ad74-4efa-98ee-723404ab157a", "3c300c84-8744-40d5-8f6d-6322620f228e", "3c82f4be-d49e-4745-b97e-a6d3f9400718", "3e4bd26d-473b-457b-8b06-46fe89ca0fd7", "3e804e3e-e3fa-4ca0-b25b-a9863f3d3cb0", "3e8aa41b-ceb1-451d-9c88-93fd9190230a", "3ea84ff5-4489-4f1d-929b-17d0a5ed96e6", "3f4c3deb-6de1-40ad-957e-1a0e9d640e96", "40287d61-ec99-4e29-8971-011e9fe91d6e", "4045491f-dfd2-4de0-8907-d6815c1adb50", "409aae2f-3960-4fe1-bb5f-48582c594b1a", "40b6a9e3-1d40-4c59-8725-a644a31130c0", "410496dd-8e03-48cd-8971-5281cf4404f8", "417ccacb-55d9-4935-bafd-bd0df2be4a67", "4290fc56-5414-4a58-93e0-d1afefc1ec57", "42ac5721-97b7-47dc-9250-cffe968107d3", "42e27c24-8857-42ab-a9ec-b825dd892612", "431a5ce6-6ccc-4805-b06e-592e5b3d498d", "43db108c-d66e-4213-8844-7d0226c10615", "44eb9829-129d-41ac-91b9-caf41689ebb7", "44f03bcd-8990-4f22-9617-e6f6023b9e2f", "456a5510-164e-4470-b41f-8a91d800999b", "456a76bd-98ce-4ca7-ade4-d4ea643f64dd", "456a8e11-55ac-47d7-a498-9bdcb394da11", "458a3be9-0f6c-46c9-b017-68361565d31a", "45b7e019-743e-48c8-9732-6016d534f803", "46aac104-c63e-44be-9b40-2b59fa92305b", "46ec7e2c-014f-4713-adf2-22b6bcd4b075", "474f05cd-a757-4169-8190-a8f681648677", "47c5a009-7b06-4946-9e55-a515b5501a31", "47ca0ddb-43ed-4b0c-96ee-65a8eedf6528", "47d5952a-0915-403f-a876-282b1a006686", "480b62e9-ad5c-4aa1-85c8-c5e4180a13e7", "4821dc56-7efb-4d59-8e28-7abac1585e21", "48e90174-b40c-444f-a7cc-af8e9f12c9ad", "48edf79d-2885-4b2f-b378-e185a591e626", "4952c61b-9607-4fd1-9715-1720c5e2c00c", "49cab3ee-197d-4856-ad9f-de50c4109323", "49ddc9bb-8b72-4ce3-befb-95e0d93bb64e", "4a22259d-d62f-4a5a-9aa9-c929e447fb92", "4a360a50-e630-4b30-8a99-3b4b30841184", "4a37b19b-5593-41c6-8a8d-32dd356aa029", "4a5efe5b-3d44-46f0-8309-5f741eabd4ca", "4a7a668d-b8d0-447f-aba5-3f5b355f9f5c", "4b163a63-ff24-4af7-9155-994cff7217e1", "4b9dfa7e-fdb1-405e-8cb7-4a383552f2f1", "4bc41301-fdad-4663-915f-f7691cdf7e69", "4bc92dc3-66cb-4832-95c9-8168e27c029b", "4d90020d-253c-4569-8717-89a1acf669fd", "4da6c001-f57c-4c82-8624-d2ef95047a11", "4e838c00-2fc1-45f3-8afc-d149dfff3643", "4ea9eb9d-ded2-496f-9643-053042d21f97", "4f25c9dc-9e33-4463-a6bf-601655c2593d", "500c419a-d285-4dd4-8de2-3a6acea0ec0e", "50475e12-3269-405b-ad84-b1916e1d9299", "5080f083-76a4-4e40-8a5b-2d15601ae9c0", "50f58d32-bda4-4a90-b2e4-9a902e4106d7", "51197598-1b0a-4961-9d44-d21f156ec54e", "514b9870-2ef6-4692-bf8f-2a0e53546c6b", "515251da-4061-471c-ac1d-772dbb1650d3", "51c641cd-eac6-477b-bf77-6224e0e414d1", "51db9553-686f-460b-aae3-cb0d6de9e93c", "52ad3256-31ba-4cf2-81b4-3e192fa508fe", "53479271-77d7-471c-b144-5dcbc737535e", "537f1632-93df-4b8a-835b-f7a9a1a6c925", "5428447b-e926-43cf-9705-a7e060f99946", "54b967da-2aaf-4107-8786-1ca4b3b35c92", "5571c673-feb3-4488-9fd5-eb2559c53d46", "565dade1-2e50-4e64-acf2-f5a33c88651c", "569b6151-2b73-4c9a-a984-1e0f9f7eb0ff", "56ce0314-30da-4a21-ac40-50122faf4122", "56dec9d8-1d4e-4a19-a48e-3aac73c11534", "57e445c5-6782-4739-930f-abfdf8fd089a", "58a38ef3-12be-4eba-b39f-84f22e55d6dd", "58f6bf3a-0cef-4e71-9ddb-bfbf23bb92b3", "591f8668-6957-494c-bd17-49e202aa2e00", "59cc3a4d-79a9-465f-9d45-4e9d0860d107", "59eb492a-13d5-4ced-af62-709fa61a8b5f", "5a513ad9-8af6-4a23-8ef1-b7067d04fe9e", "5a6730fa-9b66-4c54-88be-d8de76ed5376", "5af9debd-b2b9-409e-b7c2-fd891c5ebf11", "5b09bf29-16fe-4a5b-a95d-210482c43080", "5b874efe-379b-4096-8ee0-6124f84c2585", "5c1ed8f8-8a2d-4acd-b2fe-4b1609261771", "5cccc4fc-0e11-4055-b872-8d0896573e49", "5d03a99e-f109-4434-9b87-5b6fe926d6f5", "5f6c6c85-a88d-4d47-96e5-227c227d9ba2", "611600ec-bd0b-44d7-b9b7-7dc46a3d8e4e", "61480f13-f0b0-45b1-929a-058941387694", "6196e0f9-d2dc-4eed-8944-9399e7714fb4", "62d1dc7a-e934-46fe-933b-8825a583c30d", "62d45185-1120-4a1b-ab93-b76d5c10bac2", "635fc1dc-4324-4136-a8cd-cc963e56f74f", "63bdff14-77de-46c4-acd6-6a123a27e233", "64b27adf-b1db-4626-a3bc-f4d7c0971562", "6557be1e-f684-4e46-a49d-bea635532fa4", "655f9522-5100-4492-a671-14e0749fc524", "662d41d5-780f-426f-9638-096058ec28fb", "664437dd-550f-42e9-b689-4ca071f8610d", "66d77c7e-8e4f-458e-adca-be20b8e7379f", "671f9fb0-1c0a-4278-9f2a-d968eac98886", "674ac4b3-c936-4d22-b118-0c1542ad6f3c", "67b13eb1-fa8e-49df-919c-d7a76c49e44d", "67f615a4-c7db-4fde-b3ac-0bc26cd2f9dd", "67f9a674-8d4b-4e78-af7a-9497f9927b42", "683aaa4a-b0a7-41e8-a005-d0ada3674fe6", "68658f86-485c-4587-9f22-7b6f99d2a900", "68a68f86-effd-4b4b-a8e2-9f7538bc8497", "6948692b-c830-4199-b020-d7f30fadc74b", "69957d3b-b5c1-4c1f-8666-a020766d384c", "69d91281-2d6b-4dec-81f1-a5f5abc52af9", "6acc6c25-1561-4ba5-b275-155331c89821", "6b80276c-bcb8-4c49-97e2-4d4389220fb7", "6bd6cf82-c39b-4f2c-9109-406fe13a9c15", "6c14d0cd-288b-4848-b679-91a51e68d340", "6c6f7a27-177b-4626-b255-8d55f725d74e", "6c8b8252-315d-4bd4-8b61-e574fe1413c2", "6cd90825-871c-49c5-bf7d-5eb5f4645b33", "6cf8c428-3fad-4b53-9ef5-4c719802f541", "6d29e6b9-eb9d-482c-b076-1231fa674901", "6d7e22c6-c4c5-40a1-b7c0-402de4e65855", "6dae6954-5780-4d9b-be4e-3e68888d625d", "6e187fa5-a30a-4756-8abf-32b144504010", "6e50b7c8-1652-4688-8da0-daae44990020", "6ef173ec-3e3f-4dea-aef1-587af7ab1350", "6f2124d7-3c1b-45ee-84e6-06bf4115310d", "6f4bfaaf-2f44-4a70-8f5d-949443d5169e", "6f8d91f0-0c5d-4708-81ec-35479a83675c", "6fb470d5-b52f-4ba8-99f0-f7889f56be83", "6ffba926-a96d-44be-ae8d-0a2c7b9c5471", "70049b44-ffae-421a-afeb-ce3eebaa2010", "705eb389-1f0c-40a9-977d-6b8ccca1f565", "707fed9a-f1c8-441d-bb5b-b9ead7218027", "70f58448-819d-4140-ba69-9cf60c6a51ba", "7191fbcf-b259-408e-bc17-7458aeb7b568", "735733d7-482e-4172-9cfd-b49d9827ce8f", "7398d93b-21ec-4bd2-a572-fb3ca67d1786", "73acc81b-c5c8-482a-b290-0b4ff4d06212", "7427d348-24a3-47b5-93a9-17d5233a4ec9", "7471a870-ba60-4855-95f7-5f5272a5bf40", "7528fc28-769a-4b9b-acf6-31b2072bc64a", "754f2995-9e99-463d-b21e-da2a8f5be818", "755f8f15-a1d2-464f-a6f9-bca086c405b9", "760f21d4-f00b-42b8-836a-400766619bc5", "7662255e-0a62-49b9-bc15-df59047c1a68", "766e0fef-e081-4b5c-8e03-f3ab4de4e8b3", "771316c2-f7d6-40da-9aa7-ef7297912987", "77a86135-2edb-49a4-8433-a58a76332bf0", "77d8946c-2576-4cd0-9b62-60c0d172653a", "789c6dba-b66d-4739-b996-f740518116f1", "78da52b1-8a9d-4af3-b8f1-a5af40d99318", "790a1048-8fc7-47ef-816d-f0f7f26ede3b", "790c52bb-368c-4cc5-8aae-9ffebe3c6ca2", "792e6b22-c1c9-41c6-be33-5ba6f1c4ac24", "7935fa3a-3dd0-4c66-aa96-7dd16bcc6d86", "7959fd56-4759-4e13-b59f-66cdd2577971", "7971956f-eb7e-42e8-82bf-20aa4eec3730", "79c540e8-7869-441b-a080-8609a0a10509", "79ec818f-69ea-42c0-a65c-9dac41bdad9e", "7ac8cdc0-b8f3-4390-bf25-7c692df664c1", "7af1ff01-076e-4bcd-913d-c625e9ac0124", "7ca8f6d2-5d7e-4bac-bca8-d1850090406a", "7ccae1b7-dbd1-475b-8c5f-160a4b6922b4", "7e0c39ae-ff12-4865-9faf-dc8c197ee8a8", "7e7ce528-ee55-4fbc-bf72-ed00bc71f75e", "7e8d3223-f7f4-4129-b5fa-ceb25b14e4c0", "7ebfca45-bddb-43c3-9faf-292bc2d2546e", "800a4aca-de82-4f3c-b92e-1c8c02a8cf99", "80c2ece9-3058-4411-ac12-766e795a9cec", "813a969e-d3b1-4511-b355-657f7295b06a", "815e7594-2449-4b8c-abd6-921acaf3838f", "817dafcb-4166-44af-8a88-87979a114307", "81fe3a5e-98f4-4681-bc03-7e3e6f662e97", "83c916d7-fbed-4944-904e-c142c05c8f5b", "83f0b042-0f42-4822-8904-eec97e950919", "840f133a-a385-4bdb-96be-4ea34150772a", "8469a7b5-b6e0-42fb-a785-f9ae29c70fff", "84c48cb0-6546-4b0c-8f9e-d9e3d06c229f", "86783be2-ba12-47a3-8335-b391517bb419", "8692179e-fdfd-4622-a4a8-0efa61917dc8", "869fc17f-2613-42a1-84ff-3d91e5e05c80", "876a8bff-2109-45e4-86db-e9449a139c58", "87ba7521-01cf-497d-bb81-a5783454aa9b", "87c3b5d9-1c47-4a8a-92a8-5beeeed99ed5", "87f899ce-bd03-415d-8b02-d6e9c0beeeb3", "882f0c63-3118-43e1-a9c1-23e113873e97", "8863dd8c-6074-4d19-a52a-e7e8438a53da", "88956826-a602-44e3-ba44-0cb08bd6e054", "89dba837-ef91-4d1e-ac3c-2b75b853ce85", "8a5613a7-719c-40d8-9553-999c1cf3e4f2", "8a610b22-fc72-4380-ad1a-2f8cca5ed25b", "8a8dfdd8-169b-44df-9767-410883686ed6", "8af158d9-d5a5-4fb3-86af-59a5519f352c", "8b0f7e47-d007-44bd-9575-428e008ba89f", "8cf3af7f-0544-4fb1-8c22-4c4f09ac3af8", "8cfa7e0b-6d9d-4791-8004-3aaca50c12e9", "8d02de6a-aa09-43ad-9789-d8788bbee6a4", "8d2da0aa-98b2-4068-839e-218ac39b6653", "8e177171-d778-4336-a516-62fb0934ddfe", "8eba0816-e340-4896-a5f8-c6976cf4a5a9", "8f0880c2-0d27-4afb-842c-4b22be1f52e1", "8f3468c6-9fd7-4acb-93ac-e084245ac805", "90536053-cffc-46e0-86d8-fc2c9f97bc09", "91459e13-42d1-458e-87a3-d99c7b361ca5", "91a0fba6-0f6e-452b-8387-fbe623e48124", "924f66a2-89ca-4850-8197-9c10f9a1fbe4", "92a6b5d3-4602-4651-a5b8-a67fa0dc77c8", "92cd0b49-eab0-4483-98d2-c156689d8d12", "92f5495e-6ec5-4984-913b-15a4a0f9baf7", "9334abb9-d64e-4033-917f-8d19b65c167c", "93501315-e8f6-4d8a-8ac8-4b70deab096c", "939f4d07-bfc0-4785-84a4-702503673018", "93a7ad5a-5721-43c7-844f-d4c028fed771", "93ce78de-fb31-48f3-ae00-91b8725e2b13", "941c060d-f4bb-493b-84d7-629e211896f6", "9435f03d-29ee-4f57-b4fd-d470a7e5fc72", "9442be5e-b4fa-461e-8206-68bf4d149e2c", "948ef801-5d92-494f-b338-ae0d6992030a", "95843ea1-2b75-488c-94a7-84dc7f3db139", "95f4dae1-ef00-4b7d-957a-7b24296f5f35", "96bc5fc8-e863-463c-8db1-8a943e2aa048", "96bf99c3-aad7-496b-b342-3a7471869d5f", "97976636-e266-4fba-a173-3e85245391de", "97ecb2e7-29fb-4337-9f48-c0fe0e4ebc38", "98288de7-2b93-4dbe-bf4b-83fc9abc6d34", "98411435-7d69-4366-90ef-e1a70157c67e", "98c25e64-d0b9-45ff-a535-c841ac318133", "99ace31a-84c5-4d2e-91d8-0601595664a6", "99e794d9-b669-44df-bf3f-3005eb1ccd36", "9a193546-c08a-484f-b1f0-1be688b94ef7", "9a257f45-c1cf-4213-bfb1-f66a4eb11b0a", "9a6ac261-7e72-42cb-b450-d7652ea3bf55", "9acd1a79-d36f-4e78-bfc9-1d9b63492d97", "9b52faac-f153-4231-8e46-9026dbcda662", "9bb671d0-5bcb-4f9d-b0e9-61d3c3cd89cb", "9bdf370a-05e2-4d62-aa7d-1e93cc1b4d2b", "9ca6583f-2649-47ec-89f2-eb134882c5dc", "9d3f7ae0-6317-4c4a-80af-d2f4216d6f07", "9d8538e7-0738-422d-8fbb-843fed6887ae", "9e08efc6-ec71-4cd0-afd3-3c16672f0ed4", "9e3cd2cc-ff40-4a89-9b8d-0f2211821787", "9ebe48d4-def4-45b6-b299-0046bdf2412f", "9eea4bb6-e7bd-4c9c-9159-f86119cc2340", "9f82d69c-e6b2-4680-b540-5e56baf0d234", "9fa938de-9cee-4f09-bdb5-dda37181ce3e", "9fd83ad8-19d6-467e-a34a-551a8b182bff", "9ffb90ce-cb83-4e00-bec7-62c5962caaa6", "a03fefa8-af90-4ab7-ab50-97a9cbcfbbe3", "a0f3366e-f7a7-48ad-a20f-bb6bcbe97dfb", "a1236bd9-75d8-4ac2-89d5-2fbeeeecc4d7", "a2b7f17d-be3f-4d45-a7c0-f4f1d96ae41a", "a2d4f9e4-8389-4f06-8b1a-06cb034cb4c8", "a3bfdd60-3e99-429f-9c12-49b4a592156e", "a41d4d90-2c38-4e95-adc5-851826a62a1b", "a43f6f5a-d768-4a19-8e29-5bebde3b4a03", "a4706d4c-6e29-48d1-b967-52f3445b88cc", "a4baae25-8e29-4cf6-952f-ed7d5921b5a1", "a597d94e-67c1-47d1-9d71-ee2ffbc20cc3", "a5aee176-0b57-4558-b8a9-0fc515c2cb24", "a6452741-bf1f-457c-b14f-873ce7cf93e0", "a65005a5-010f-415c-8f5c-2b14f08ed8d1", "a6b417fa-739a-4e3e-b638-7dc8dd3f0bf5", "a6f91e87-d40a-4407-bacd-1f38921bbf71", "a734968c-f720-4a6d-b9ca-90ea9cfc550a", "a773b473-0210-495f-b599-05531190470a", "a78535e9-1f8e-4805-97a9-fa5fb5e273cf", "a8ad7a90-5d86-4643-87fa-96963f50b2ad", "a938ea33-2e65-4b37-8b97-1be9134fef7a", "a9527021-c33e-4fb1-aab6-c4d9a11da4bb", "aa168e29-2f3a-40a9-913f-21c5d99061b8", "ab3f49d0-c5f6-4472-8356-5ea429fd025b", "ab75a8a3-d472-426f-b4c1-a7f0fc17d1a6", "ad3d7dbd-2efc-4d53-af48-94ee81c3db8c", "ad8ff5fc-e719-4a6e-868d-d22cc02f8f2a", "ad909fb3-6e4e-4d04-a314-f4b4668bd988", "ae18110f-703e-438f-a07e-3c04e3e42118", "aeea1aa3-f7ce-4d26-a767-9ae080092f91", "af2c6f7f-c006-4fa6-8f33-6c37a4d9ec44", "af3ff33c-42da-446d-af61-d82640a550f7", "af44a142-8528-4cab-8cca-2479fa01b405", "afae0877-7d6b-49b2-b915-8c46b25e7a17", "afd79332-4625-492b-95b8-49e0d45d264f", "afdfb121-5c79-4ee7-b373-97b07b09cb9d", "b0149b36-301a-4a5b-8f02-f6cece67ae40", "b0660ab9-f0c7-4a30-9c9e-01f4558cff82", "b18366d1-789a-42e3-9632-64cfc1d067bb", "b185f6b2-e8c4-4fc6-bc3d-ae615ed974bd", "b1bbf9ea-4515-44c4-9b63-0445f531bf11", "b2c0fb18-0eac-454e-be9f-4826aeb5c28b", "b37ec435-fb90-4b5f-aac1-27e70ac173ab", "b37fb115-d062-41c0-96d6-635a0d653412", "b492c9aa-a8c5-4210-a9a8-b83edd2cbae7", "b4afea5a-a177-4dfd-b0b1-7516c4028a03", "b4c410b4-18c5-4586-867a-7d32738cf175", "b4ed7c3e-9954-4ae8-a935-d834a7797e74", "b607854b-d793-4916-8350-47eb4357eb2a", "b62d3435-469d-409d-9e96-0b7e239fc4d2", "b644129e-94c8-4da6-92aa-afcdf8a8bac4", "b6c75b4c-ab71-4391-9b24-7d1dd2c4ac35", "b7fde35c-4d78-4c25-a4a0-61755aad0d0c", "b8bdd44c-be4b-4a79-b427-e87fee157334", "ba526c09-72c7-4fa9-a49d-68fda111b6ed", "ba8b078d-b3d2-417d-b015-daac6e411480", "bac585b6-69a6-4967-aa91-8e7329c8d392", "bc30fe8d-aabd-43c3-85b3-0e730f9e27f2", "bc4057e8-97ef-447e-9aa8-4853b6a77f8a", "bd0c935d-573c-4267-bda1-7960d1782949", "bd4ca201-5a18-4f4c-8ec9-dfa5d78f1d98", "bdb4dcfe-bfe7-4560-b591-cc7683356da7", "be222ebd-863f-402c-a82d-351cb3bfefbf", "be48e5c8-9c10-4e2e-adea-c15291ba0b30", "be7a1dad-04a9-47c6-8d3a-1d2500e929fa", "bec02306-1da1-4f3a-b871-42a3712c52d1", "bf00bf1f-5d21-423b-9854-dc89428a97c8", "bf5d6180-e854-4033-ba73-0cc439e08f34", "bfbbb483-e162-4304-a77e-9934f0a4d601", "bfc0e8ae-2fe2-440c-859d-58d3e90e1ba0", "bfda0a8d-f89d-43cb-9dcc-e8cc021361b0", "c0d01d45-13c3-4472-8230-66f3f5b8a10a", "c10b8cdd-31e1-4684-a6b3-bfdaa480ea33", "c1a86efc-cc12-401c-ae7b-741a7f1c773c", "c1fa454b-1cc8-4554-8722-468ba81a0b88", "c20e64d3-3f5a-4f04-bb69-81c419010e71", "c227e31a-96fb-42f1-936f-31b6f4c7dc13", "c269165b-1481-4f55-a1ed-322ea2dc052c", "c2fd3515-db6f-4c49-b2b4-e0421098b393", "c38ce383-a1fd-45d0-8d97-cb11889a20cb", "c39d0681-9ed7-4c49-ab0e-e08aadc8a28c", "c3d8a435-6538-4b74-ad73-79d5574dc4b6", "c43b36de-360c-445d-9a10-151e0980c158", "c46449b5-355e-4ac9-bc31-2d4506629a29", "c498afe8-4b6c-4b1c-9424-e93d1afb8caa", "c4c0ca04-0d43-4f04-aeaf-04f2dc851db4", "c537e2e6-0100-415b-ab5c-9da8d3c61596", "c6fcf7cc-c47e-42a9-a0b9-381cebea7e95", "c805c67e-00dd-4f16-9b77-d58bea61b122", "c8260c48-997b-4e70-aaf9-07da4a06eec6", "c8ba93ea-f481-4d67-83a6-555377d1fed3", "c8c87ae2-450f-4487-ad99-e457facc0143", "c902cda6-2de6-4718-a4c0-4e0ecc8eb055", "ca3a8b45-95a0-437f-a6da-cecc9fbf5356", "ca62be8d-c1e0-46d1-bcc2-8152b0d35838", "cb336031-0958-48af-8a15-c2f5eb2215c3", "cc64042e-7f1e-401a-98b3-80ca554a6609", "cd3edfdc-a65c-4345-b0aa-ff29212efd6d", "cd4582c8-1aea-4b9e-a7cc-5f6584c75d01", "cd55118a-b428-4ea5-8b05-690eb507e42f", "cd7143c8-3fcf-4c28-82bf-de778231da01", "cdc4eed4-17ec-4f27-873b-833450b0d82d", "ce8a4f29-fcad-4a8f-9809-510756bddb5f", "cec010e3-cb2b-4a65-94c6-f7b1258b2cd1", "cec9a32a-87a9-4e3e-8282-c165f7d82f83", "cf1780c4-8fbf-4b3a-ab75-94284f9d358b", "cfb83ec3-2fb1-449a-9405-9d723c20ff6b", "d01f9e8f-349f-4e79-b870-6e3c34140b4c", "d069e259-7ac7-40cc-b820-84f21ed06d72", "d07ba739-66a8-4384-a44e-e1502b75a26f", "d0a337c5-301f-4055-ae2c-20216e6838a6", "d10a595a-5242-44bd-8833-029c8eb9d138", "d13db0f8-8f02-4a60-b91c-d79fa10a62b5", "d219f6ea-6742-45ed-8ff5-ac108926f4c6", "d2b0e2fc-20f6-4797-b9bf-98c9f394f763", "d3889f62-78cc-4fec-af0a-064bca631119", "d426c19b-0132-4e4f-a5c0-8d3795cb208d", "d53b6bae-3ac4-4dd8-b537-842f276c4204", "d5784d0a-3a8c-4a0d-b8d4-6b9c84fca603", "d5c3f537-9169-4edc-b7cf-0360bc3e996b", "d8efa3ff-588c-47ea-8884-69a4107fe3bc", "d954ccc1-e6c7-492f-8304-2b281b0ca186", "d978706d-f8a2-4063-92a5-714fd99294d3", "d9be7115-6515-4514-a466-8bd828d56d0d", "daa6322e-f83f-43fb-bdf1-5371a7531c78", "dae10db9-c4ff-4705-a3f6-ffefcd760af8", "dc8a1752-20df-47cc-b170-1771713216d4", "dca7f194-cc63-46e0-8f79-c4b967bb8cb9", "ddad4d99-07ef-4523-848e-e9718ba789a8", "ddf910aa-3091-44ff-9572-f6250de2fa84", "de22014c-65cf-4b35-970d-6f408e97eb00", "de620fff-3821-4aef-a849-d992d5dc82fd", "de9b36ef-9c54-48a6-ac95-f1fa5e44ea9c", "df0696ee-b0e6-48ad-8fe6-c1354fb4cfd2", "df8e0afd-538c-48b2-a8d6-6551427851ae", "dfb0ca94-8d2b-4cb0-9c7d-b81b95263fee", "e05f0a8e-7b95-4bf8-8a5e-3751d1664cc6", "e06244c8-6156-4a57-873b-8284ceb7c7d2", "e0e3563e-044d-4e9d-9fe9-6bde5782f985", "e1076621-1acb-4c90-afce-579315d244de", "e154aede-89ef-4fcc-b7e9-cf21650f2802", "e15fa181-c01b-41fc-acf4-43060d0c2ca2", "e19645a5-5e4b-472a-9da2-4ad2b5ea8917", "e200c023-6f44-4f32-b0a5-b0797ef73b73", "e27d3c72-82ad-43b1-a6d4-d628f9d58a9f", "e316d391-86f3-423e-a1f9-e3a95d600445", "e3204393-0844-44ca-a1f6-85fff0f5c7f8", "e3433c25-0b71-4c98-99e7-a6a059d45586", "e3f74e0c-1659-4147-a2ea-3d333d2a96f5", "e4107c0a-8a8e-4c10-9016-64cc85dd76d5", "e49c90d5-5a78-4827-90f2-acb57d84998f", "e4a0991e-d6e3-49ee-84fb-6aa588c38497", "e6349ba5-93b7-4078-a81a-9db2cdf268c0", "e706da79-2593-44eb-a302-50cea3259f13", "e73ea7fb-ec52-42b9-a43b-627f9ccb2a57", "e7c12c4a-8f08-4d9e-925a-892fd5de72c9", "e834c6d2-967a-4057-ad0a-a16486afc2b8", "e846d880-3838-41ad-9aa5-1f365d959ad8", "e8e54672-6326-400d-8ec8-283d03493e12", "e96d90b9-db63-4284-99cf-2d5312bee283", "e9b32641-b62a-4b6c-87ef-3bf77961848b", "e9eae364-db62-4849-85e0-ab36493eab75", "ea21ca67-5ca9-4c19-80b6-dc317d541128", "ea7c3a6c-ba2e-4302-8a37-80d00f51db16", "ea93bd49-cb67-4bdc-b926-430bb1b5d3b3", "eac0b022-772b-42f4-a10d-ee368e31b182", "eb4fbb4c-8bf8-4dfc-963e-9a92b1b3f6b4", "eb50a798-0a58-4fb7-8b53-38d88fbda5a8", "ec2adc2e-95bb-4756-9a7c-54f41c6eb3bb", "ec6ccc81-5ca8-4a82-841b-ec3a4003d8b8", "eca1ff0d-8126-450a-8a09-dd2dc0bb86db", "eca8b817-670d-4a72-9ebc-9a074d69f8f1", "edb8b189-f6e0-40d3-ad8a-ed0ff1100697", "edea6965-e80f-4c46-8163-8c9a6ff434ae", "ee21a9ce-f29e-4831-8731-3106b9f21524", "eed47971-e8cd-441a-8fcb-4597747b96dd", "f0fd0526-0002-4f6d-b320-6369b5731508", "f145c92e-7498-4f8e-a313-3a0f2ae5d5e5", "f198a5bb-c177-4b70-b04a-ff3a4fd7cba5", "f1bfd29a-e884-454d-9ec1-5bc1482fcf5f", "f1f88ad0-b8d2-4173-9704-a98f9f3c7018", "f307a4c0-0b82-436b-a31c-b1bc6273a4d7", "f380ea34-0888-4049-b690-c81ff1882f86", "f4842a76-0e82-4d7d-b279-27fb8a430fdd", "f4d70926-0cbb-40c1-93ee-ffea2601ea66", "f4e10e0e-ffba-4635-bdd6-5d0480029895", "f5ddc1ac-1d77-4957-9f2d-0c617b00bb3a", "f663d2c5-5042-403e-911c-4a85ec438dd4", "f6880204-fb7b-4667-adbd-15e0cb59d9c7", "f6f5ee37-deb8-4217-9bd1-e244ed29c0ad", "f757e8cd-01c3-440b-832a-c71ecff4c424", "f773cddf-bd5d-4c60-9287-26b9afae2b17", "f82bb892-c3eb-46ae-ad55-6049f96449af", "f84ba984-a4fc-4eb1-8d88-b48d23a6946e", "f8ae3a70-c61a-4389-abf4-7fae1392463c", "f8b840e2-3f5f-43f4-aff7-6bbc98912426", "f8c3b842-e274-4443-ab96-a7<PERSON>abcbafed", "f8c67727-943d-4426-970a-5ecbcd582671", "f92adef1-43ee-4d96-99b1-61b76ed252dc", "f9e1e2af-8652-4219-bb22-b938df27fa4f", "fa28a464-554d-41cc-b349-e53dacd84e59", "fc4464fb-258a-4dc6-b7de-8cc568f96c6b", "fcb0e366-9da1-4888-a9e7-c45607f0e7cf", "fda2a7f5-0f0a-4f8b-acdb-728d6c3be25a", "fdb0093e-094b-479c-91fe-1867dcaeb796", "fdbd6a93-1e0c-41ea-82d0-369da0a85d37", "fdc3f009-d502-4432-8508-582e0ef7b16e", "fe12b6eb-d0dc-4748-b980-dab1515e41bc", "fe257bec-5709-4159-ade8-3cdd956c7c00", "fe921428-7f2c-473a-ac62-d4f68b08f06f", "fe9fd289-7eaf-4f2f-9bea-7b22d4ca0648", "feb8c163-517f-49aa-bcb2-ad0233b374f0", "ff4c5a7f-47d1-409d-b333-15fc9d984698"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Service\\Generator\\OrderCancelHistoryService.php", "line": 66}, {"index": 20, "namespace": null, "name": "\\app\\Repository\\Generator\\OrderCancelHistoryRepository.php", "line": 36}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\CoreController.php", "line": 100}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\Generator\\OrderCancelHistoryController.php", "line": 92}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.20217, "duration_str": "202ms", "stmt_id": "\\app\\Service\\Generator\\OrderCancelHistoryService.php:66", "connection": "juragan_beku", "start_percent": 31.654, "width_percent": 60.843}, {"sql": "select * from `conf_users` where `conf_users`.`id` in (0, 0, ********, 38, 57, 59, 814, 84, 8, 0, 0) and `conf_users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Service\\Generator\\OrderCancelHistoryService.php", "line": 66}, {"index": 20, "namespace": null, "name": "\\app\\Repository\\Generator\\OrderCancelHistoryRepository.php", "line": 36}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\CoreController.php", "line": 100}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\Generator\\OrderCancelHistoryController.php", "line": 92}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.01078, "duration_str": "10.78ms", "stmt_id": "\\app\\Service\\Generator\\OrderCancelHistoryService.php:66", "connection": "juragan_beku", "start_percent": 92.497, "width_percent": 3.244}, {"sql": "select * from `conf_group` where `conf_group`.`id` in ('', '0c8233e1-2175-452a-ba1a-8a14b9c3b00f', '17611e84-3f05-429f-81d0-b6e3a4fdb658', '3b2cce7c-c8e3-4d6d-a289-4809e985549e', '4aa39254-ce2c-4b67-9cee-d4ecbc6d6aef', '63f5b38f-11ee-4439-b86f-5eefcaaf8cab', '8888b855-0e42-46a6-aa79-a0e9c8aed957', '8bf7370b-e2ae-4304-8851-a07d0b2c27a5', '940952c5-19b3-4bb7-8088-91ec74251295', '9b27fced-93e8-4541-aaa5-3e536ca189d2', 'a126c5e2-3cec-496f-be95-50e5b863c4d1', 'fca7d997-63dc-4474-9b50-b6dce3e30208', 'ff9759b5-2dc5-410b-b86c-f8146c1f66d8') and `conf_group`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["", "0c8233e1-2175-452a-ba1a-8a14b9c3b00f", "17611e84-3f05-429f-81d0-b6e3a4fdb658", "3b2cce7c-c8e3-4d6d-a289-4809e985549e", "4aa39254-ce2c-4b67-9cee-d4ecbc6d6aef", "63f5b38f-11ee-4439-b86f-5eefcaaf8cab", "8888b855-0e42-46a6-aa79-a0e9c8aed957", "8bf7370b-e2ae-4304-8851-a07d0b2c27a5", "940952c5-19b3-4bb7-8088-91ec74251295", "9b27fced-93e8-4541-aaa5-3e536ca189d2", "a126c5e2-3cec-496f-be95-50e5b863c4d1", "fca7d997-63dc-4474-9b50-b6dce3e30208", "ff9759b5-2dc5-410b-b86c-f8146c1f66d8"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Service\\Generator\\OrderCancelHistoryService.php", "line": 66}, {"index": 25, "namespace": null, "name": "\\app\\Repository\\Generator\\OrderCancelHistoryRepository.php", "line": 36}, {"index": 26, "namespace": null, "name": "\\app\\Http\\Controllers\\CoreController.php", "line": 100}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\Generator\\OrderCancelHistoryController.php", "line": 92}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.0010400000000000001, "duration_str": "1.04ms", "stmt_id": "\\app\\Service\\Generator\\OrderCancelHistoryService.php:66", "connection": "juragan_beku", "start_percent": 95.742, "width_percent": 0.313}, {"sql": "select count(*) as aggregate from (select * from `tbl_order_cancel` where `tbl_order_cancel`.`deleted_at` is null order by `created_at` desc) count_row_table", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 215}, {"index": 15, "namespace": null, "name": "\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 190}, {"index": 16, "namespace": null, "name": "\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 139}, {"index": 17, "namespace": null, "name": "\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 99}, {"index": 18, "namespace": null, "name": "\\app\\Service\\Generator\\OrderCancelHistoryService.php", "line": 93}], "duration": 0.0008900000000000001, "duration_str": "890μs", "stmt_id": "\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php:215", "connection": "juragan_beku", "start_percent": 96.055, "width_percent": 0.268}, {"sql": "select * from `tbl_order_cancel` where `tbl_order_cancel`.`deleted_at` is null order by `created_at` desc, `id` asc limit 10 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 272}, {"index": 15, "namespace": null, "name": "\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 101}, {"index": 16, "namespace": null, "name": "\\app\\Service\\Generator\\OrderCancelHistoryService.php", "line": 93}, {"index": 17, "namespace": null, "name": "\\app\\Repository\\Generator\\OrderCancelHistoryRepository.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Controllers\\CoreController.php", "line": 100}], "duration": 0.00191, "duration_str": "1.91ms", "stmt_id": "\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php:272", "connection": "juragan_beku", "start_percent": 96.322, "width_percent": 0.575}, {"sql": "select * from `tbl_order` where `tbl_order`.`id` in ('7af1ff01-076e-4bcd-913d-c625e9ac0124', '89dba837-ef91-4d1e-ac3c-2b75b853ce85', '8a5613a7-719c-40d8-9553-999c1cf3e4f2', '939f4d07-bfc0-4785-84a4-702503673018', '93ce78de-fb31-48f3-ae00-91b8725e2b13', '9d3f7ae0-6317-4c4a-80af-d2f4216d6f07', 'c10b8cdd-31e1-4684-a6b3-bfdaa480ea33', 'f145c92e-7498-4f8e-a313-3a0f2ae5d5e5', 'f84ba984-a4fc-4eb1-8d88-b48d23a6946e', 'ff4c5a7f-47d1-409d-b333-15fc9d984698') and `tbl_order`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["7af1ff01-076e-4bcd-913d-c625e9ac0124", "89dba837-ef91-4d1e-ac3c-2b75b853ce85", "8a5613a7-719c-40d8-9553-999c1cf3e4f2", "939f4d07-bfc0-4785-84a4-702503673018", "93ce78de-fb31-48f3-ae00-91b8725e2b13", "9d3f7ae0-6317-4c4a-80af-d2f4216d6f07", "c10b8cdd-31e1-4684-a6b3-bfdaa480ea33", "f145c92e-7498-4f8e-a313-3a0f2ae5d5e5", "f84ba984-a4fc-4eb1-8d88-b48d23a6946e", "ff4c5a7f-47d1-409d-b333-15fc9d984698"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 272}, {"index": 20, "namespace": null, "name": "\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 101}, {"index": 21, "namespace": null, "name": "\\app\\Service\\Generator\\OrderCancelHistoryService.php", "line": 93}, {"index": 22, "namespace": null, "name": "\\app\\Repository\\Generator\\OrderCancelHistoryRepository.php", "line": 36}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Controllers\\CoreController.php", "line": 100}], "duration": 0.00086, "duration_str": "860μs", "stmt_id": "\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php:272", "connection": "juragan_beku", "start_percent": 96.897, "width_percent": 0.259}, {"sql": "select * from `conf_users` where `conf_users`.`id` in (0, 0) and `conf_users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 272}, {"index": 20, "namespace": null, "name": "\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 101}, {"index": 21, "namespace": null, "name": "\\app\\Service\\Generator\\OrderCancelHistoryService.php", "line": 93}, {"index": 22, "namespace": null, "name": "\\app\\Repository\\Generator\\OrderCancelHistoryRepository.php", "line": 36}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Controllers\\CoreController.php", "line": 100}], "duration": 0.00874, "duration_str": "8.74ms", "stmt_id": "\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php:272", "connection": "juragan_beku", "start_percent": 97.156, "width_percent": 2.63}, {"sql": "select * from `conf_group` where `conf_group`.`id` in ('', '0c8233e1-2175-452a-ba1a-8a14b9c3b00f', '17611e84-3f05-429f-81d0-b6e3a4fdb658', '3b2cce7c-c8e3-4d6d-a289-4809e985549e', '4aa39254-ce2c-4b67-9cee-d4ecbc6d6aef', '63f5b38f-11ee-4439-b86f-5eefcaaf8cab', '8888b855-0e42-46a6-aa79-a0e9c8aed957', '8bf7370b-e2ae-4304-8851-a07d0b2c27a5', '940952c5-19b3-4bb7-8088-91ec74251295', '9b27fced-93e8-4541-aaa5-3e536ca189d2', 'a126c5e2-3cec-496f-be95-50e5b863c4d1', 'fca7d997-63dc-4474-9b50-b6dce3e30208', 'ff9759b5-2dc5-410b-b86c-f8146c1f66d8') and `conf_group`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["", "0c8233e1-2175-452a-ba1a-8a14b9c3b00f", "17611e84-3f05-429f-81d0-b6e3a4fdb658", "3b2cce7c-c8e3-4d6d-a289-4809e985549e", "4aa39254-ce2c-4b67-9cee-d4ecbc6d6aef", "63f5b38f-11ee-4439-b86f-5eefcaaf8cab", "8888b855-0e42-46a6-aa79-a0e9c8aed957", "8bf7370b-e2ae-4304-8851-a07d0b2c27a5", "940952c5-19b3-4bb7-8088-91ec74251295", "9b27fced-93e8-4541-aaa5-3e536ca189d2", "a126c5e2-3cec-496f-be95-50e5b863c4d1", "fca7d997-63dc-4474-9b50-b6dce3e30208", "ff9759b5-2dc5-410b-b86c-f8146c1f66d8"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 272}, {"index": 25, "namespace": null, "name": "\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 101}, {"index": 26, "namespace": null, "name": "\\app\\Service\\Generator\\OrderCancelHistoryService.php", "line": 93}, {"index": 27, "namespace": null, "name": "\\app\\Repository\\Generator\\OrderCancelHistoryRepository.php", "line": 36}, {"index": 28, "namespace": null, "name": "\\app\\Http\\Controllers\\CoreController.php", "line": 100}], "duration": 0.0007099999999999999, "duration_str": "710μs", "stmt_id": "\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php:272", "connection": "juragan_beku", "start_percent": 99.786, "width_percent": 0.214}]}, "models": {"data": {"App\\Models\\Generator\\Order": 550, "App\\Models\\Generator\\OrderCancelHistory": 628, "App\\Models\\User": 1215, "App\\Models\\Setting": 4, "App\\Models\\Group": 2619, "App\\Models\\Menu": 313}, "count": 5329}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "QoZh1I8mypHP2aMUh0DEtCk2CpnpWBdxW5ZtIuuB", "url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/administrator/ordercancelhistories\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "c065dc93-6b2f-4bf5-b746-6bb48713d4bf"}, "request": {"path_info": "/administrator/ordercancelhistories/datatable.json", "status_code": "<pre class=sf-dump id=sf-dump-93834725 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-93834725\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-737462194 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>draw</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>columns</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"11 characters\">DT_RowIndex</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"16 characters\">order.order_code</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"16 characters\">order.order_code</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"6 characters\">reason</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">reason</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"13 characters\">user.fullname</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">user.fullname</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"6 characters\">action</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">action</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>column</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>dir</span>\" => \"<span class=sf-dump-str title=\"3 characters\">asc</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>start</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>length</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>no_order</span>\" => \"<span class=sf-dump-str title=\"5 characters\">20185</span>\"\n  \"<span class=sf-dump-key>_</span>\" => \"<span class=sf-dump-str title=\"13 characters\">1752049867294</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-737462194\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1840069809 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>draw</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>columns</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"11 characters\">DT_RowIndex</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"16 characters\">order.order_code</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"16 characters\">order.order_code</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"6 characters\">reason</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">reason</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"13 characters\">user.fullname</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">user.fullname</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"6 characters\">action</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">action</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>column</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>dir</span>\" => \"<span class=sf-dump-str title=\"3 characters\">asc</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>start</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>length</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>no_order</span>\" => \"<span class=sf-dump-str title=\"5 characters\">20185</span>\"\n  \"<span class=sf-dump-key>_</span>\" => \"<span class=sf-dump-str title=\"13 characters\">1752049867294</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1840069809\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">http://127.0.0.1:8000/administrator/ordercancelhistories</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">en-US,en;q=0.9,id;q=0.8,zh-CN;q=0.7,zh;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"808 characters\">_ga=GA1.1.*********.1749434964; _ga_GBZ3SGGX85=GS2.1.s1751877726$o26$g0$t1751877726$j60$l0$h0; XSRF-TOKEN=eyJpdiI6IjV4RWM0WStHd3NoNGdmanFJQ2VpNVE9PSIsInZhbHVlIjoiTXhROWZGMURJQzZLNlZzS2RzQ0xpYXhGa2dKUkVJWEZGT2Q1dEFVWXM2VUl5Zzk1VmhOZUVsUXFVZGp4SzlaYzRZSG4ybDFQNWpEbW5jdjZ5MEhrRDZyT05tZGJKQTd4REhaN2Rxc00yL3NNWnBiREdLNWlPbmFhQ1FQNFpNVGwiLCJtYWMiOiJmYWQ4NjgwMzFlYTNhNGQ2YWZmOGJhOTZlNzdkOTgzMzVjZDBkZDg3Nzc0MTk3NDk2MTA2NTczZTAxNjcwM2Q0IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IktESUlPUkNTcmhHRzJud3gvSG4waHc9PSIsInZhbHVlIjoialRHRzNvUHh6S0hhOXFVMDRneFc1b3dlRzRhZzI0SS9oNXlpZkNNZDdsOWlCbUNyTlorUXhxR0JPMG5hRmY1YXIwd2haVG8vUTZnVFdiS3hQTmlucnZuNVBzbFNGOTBaSlpzU2dNYUFMVnJJYmlRN0V0MTFxbCtRbWVsdk9PakgiLCJtYWMiOiJmY2I0MjA2ZTY0NWI4ODIzOTk1YTE0ZDFkNTdiMjE2NTdlOGI3MjY3NTkyMGYwYTAzNzE5ZDNjYzcwYWI1NmQ3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:30</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">D:\\Topan\\cms-juraganbeku\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">50372</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"30 characters\">PHP/8.3.6 (Development Server)</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"1559 characters\">/administrator/ordercancelhistories/datatable.json?draw=1&amp;columns%5B0%5D%5Bdata%5D=DT_RowIndex&amp;columns%5B0%5D%5Bname%5D=id&amp;columns%5B0%5D%5Bsearchable%5D=true&amp;columns%5B0%5D%5Borderable%5D=true&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order.order_code&amp;columns%5B1%5D%5Bname%5D=order.order_code&amp;columns%5B1%5D%5Bsearchable%5D=false&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=reason&amp;columns%5B2%5D%5Bname%5D=reason&amp;columns%5B2%5D%5Bsearchable%5D=false&amp;columns%5B2%5D%5Borderable%5D=true&amp;columns%5B2%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B2%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B3%5D%5Bdata%5D=user.fullname&amp;columns%5B3%5D%5Bname%5D=user.fullname&amp;columns%5B3%5D%5Bsearchable%5D=false&amp;columns%5B3%5D%5Borderable%5D=true&amp;columns%5B3%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B3%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B4%5D%5Bdata%5D=created_at&amp;columns%5B4%5D%5Bname%5D=created_at&amp;columns%5B4%5D%5Bsearchable%5D=false&amp;columns%5B4%5D%5Borderable%5D=true&amp;columns%5B4%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B4%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B5%5D%5Bdata%5D=action&amp;columns%5B5%5D%5Bname%5D=action&amp;columns%5B5%5D%5Bsearchable%5D=false&amp;columns%5B5%5D%5Borderable%5D=false&amp;columns%5B5%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B5%5D%5Bsearch%5D%5Bregex%5D=false&amp;order%5B0%5D%5Bcolumn%5D=0&amp;order%5B0%5D%5Bdir%5D=asc&amp;start=0&amp;length=10&amp;search%5Bvalue%5D=&amp;search%5Bregex%5D=false&amp;no_order=20185&amp;_=1752049867294</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"50 characters\">/administrator/ordercancelhistories/datatable.json</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"35 characters\">D:\\Topan\\cms-juraganbeku\\server.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"50 characters\">/administrator/ordercancelhistories/datatable.json</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"1508 characters\">draw=1&amp;columns%5B0%5D%5Bdata%5D=DT_RowIndex&amp;columns%5B0%5D%5Bname%5D=id&amp;columns%5B0%5D%5Bsearchable%5D=true&amp;columns%5B0%5D%5Borderable%5D=true&amp;columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B1%5D%5Bdata%5D=order.order_code&amp;columns%5B1%5D%5Bname%5D=order.order_code&amp;columns%5B1%5D%5Bsearchable%5D=false&amp;columns%5B1%5D%5Borderable%5D=true&amp;columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B2%5D%5Bdata%5D=reason&amp;columns%5B2%5D%5Bname%5D=reason&amp;columns%5B2%5D%5Bsearchable%5D=false&amp;columns%5B2%5D%5Borderable%5D=true&amp;columns%5B2%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B2%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B3%5D%5Bdata%5D=user.fullname&amp;columns%5B3%5D%5Bname%5D=user.fullname&amp;columns%5B3%5D%5Bsearchable%5D=false&amp;columns%5B3%5D%5Borderable%5D=true&amp;columns%5B3%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B3%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B4%5D%5Bdata%5D=created_at&amp;columns%5B4%5D%5Bname%5D=created_at&amp;columns%5B4%5D%5Bsearchable%5D=false&amp;columns%5B4%5D%5Borderable%5D=true&amp;columns%5B4%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B4%5D%5Bsearch%5D%5Bregex%5D=false&amp;columns%5B5%5D%5Bdata%5D=action&amp;columns%5B5%5D%5Bname%5D=action&amp;columns%5B5%5D%5Bsearchable%5D=false&amp;columns%5B5%5D%5Borderable%5D=false&amp;columns%5B5%5D%5Bsearch%5D%5Bvalue%5D=&amp;columns%5B5%5D%5Bsearch%5D%5Bregex%5D=false&amp;order%5B0%5D%5Bcolumn%5D=0&amp;order%5B0%5D%5Bdir%5D=asc&amp;start=0&amp;length=10&amp;search%5Bvalue%5D=&amp;search%5Bregex%5D=false&amp;no_order=20185&amp;_=1752049867294</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_REQUESTED_WITH</span>\" => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"56 characters\">http://127.0.0.1:8000/administrator/ordercancelhistories</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"44 characters\">en-US,en;q=0.9,id;q=0.8,zh-CN;q=0.7,zh;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"808 characters\">_ga=GA1.1.*********.1749434964; _ga_GBZ3SGGX85=GS2.1.s1751877726$o26$g0$t1751877726$j60$l0$h0; XSRF-TOKEN=eyJpdiI6IjV4RWM0WStHd3NoNGdmanFJQ2VpNVE9PSIsInZhbHVlIjoiTXhROWZGMURJQzZLNlZzS2RzQ0xpYXhGa2dKUkVJWEZGT2Q1dEFVWXM2VUl5Zzk1VmhOZUVsUXFVZGp4SzlaYzRZSG4ybDFQNWpEbW5jdjZ5MEhrRDZyT05tZGJKQTd4REhaN2Rxc00yL3NNWnBiREdLNWlPbmFhQ1FQNFpNVGwiLCJtYWMiOiJmYWQ4NjgwMzFlYTNhNGQ2YWZmOGJhOTZlNzdkOTgzMzVjZDBkZDg3Nzc0MTk3NDk2MTA2NTczZTAxNjcwM2Q0IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IktESUlPUkNTcmhHRzJud3gvSG4waHc9PSIsInZhbHVlIjoialRHRzNvUHh6S0hhOXFVMDRneFc1b3dlRzRhZzI0SS9oNXlpZkNNZDdsOWlCbUNyTlorUXhxR0JPMG5hRmY1YXIwd2haVG8vUTZnVFdiS3hQTmlucnZuNVBzbFNGOTBaSlpzU2dNYUFMVnJJYmlRN0V0MTFxbCtRbWVsdk9PakgiLCJtYWMiOiJmY2I0MjA2ZTY0NWI4ODIzOTk1YTE0ZDFkNTdiMjE2NTdlOGI3MjY3NTkyMGYwYTAzNzE5ZDNjYzcwYWI1NmQ3IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1752049988.2811</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1752049988</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_GBZ3SGGX85</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QoZh1I8mypHP2aMUh0DEtCk2CpnpWBdxW5ZtIuuB</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OXEmGaq68dByCoDl0SJP8YEGquktzNtL3aXCzLzt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-849323072 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 09 Jul 2025 08:33:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkRieVA3TmdDREN2QXRnRVBuNjV2ckE9PSIsInZhbHVlIjoiaUU4bnQ3RWVhME9LZUVaMExVb0hULzJWcEdFQlNVSXc1dGFJS2tUTUF2NzhlUlYvZTJSeTR5RjBnZ1hmUHNxdDhFWGR5Q28wR2pRNUF0WVV5Y3F2V3pNalUrM0FucEJCK0FtL0Y1SzJoeGZiK2F4bXA2VHNIT2lIRm82Wnd3SGkiLCJtYWMiOiI4N2I5ZWU3ZTMzMTAzYTJjNmViNmZlMTIyZDkwNTUwOGEyNDNlOGVkNzkyYTUwMmJlNDY0ZTUyZGNlNmQ2ODcyIiwidGFnIjoiIn0%3D; expires=Wed, 09-Jul-2025 10:33:09 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6Im1zSDMrOXZsLzdmMDBkWk4ySEZtVkE9PSIsInZhbHVlIjoibEdDUEtIMkhUT2RPcS84YVhOUEtHeHZTVEIrSXlBVW5UVHRjR3FoaE1obUlmTkV1VlRxT2JTS3ovQnY4Y3VVMjRmcEc2R1BFUlZ0aW0yYUZtZEY2aUhjZy8zSUpnM3N4anEzb3JGYVFWc0lIV200ejg5MHJydVg0djVKRkRBTmEiLCJtYWMiOiIwZmRhMDQxOTFlYzIwZTI0OTljYTJiNTNiOWZmZTYyODJmNjc2YTkwM2Y0MzRiNjU5YjNhNmJiYTY2NWMxOTc0IiwidGFnIjoiIn0%3D; expires=Wed, 09-Jul-2025 10:33:09 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkRieVA3TmdDREN2QXRnRVBuNjV2ckE9PSIsInZhbHVlIjoiaUU4bnQ3RWVhME9LZUVaMExVb0hULzJWcEdFQlNVSXc1dGFJS2tUTUF2NzhlUlYvZTJSeTR5RjBnZ1hmUHNxdDhFWGR5Q28wR2pRNUF0WVV5Y3F2V3pNalUrM0FucEJCK0FtL0Y1SzJoeGZiK2F4bXA2VHNIT2lIRm82Wnd3SGkiLCJtYWMiOiI4N2I5ZWU3ZTMzMTAzYTJjNmViNmZlMTIyZDkwNTUwOGEyNDNlOGVkNzkyYTUwMmJlNDY0ZTUyZGNlNmQ2ODcyIiwidGFnIjoiIn0%3D; expires=Wed, 09-Jul-2025 10:33:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6Im1zSDMrOXZsLzdmMDBkWk4ySEZtVkE9PSIsInZhbHVlIjoibEdDUEtIMkhUT2RPcS84YVhOUEtHeHZTVEIrSXlBVW5UVHRjR3FoaE1obUlmTkV1VlRxT2JTS3ovQnY4Y3VVMjRmcEc2R1BFUlZ0aW0yYUZtZEY2aUhjZy8zSUpnM3N4anEzb3JGYVFWc0lIV200ejg5MHJydVg0djVKRkRBTmEiLCJtYWMiOiIwZmRhMDQxOTFlYzIwZTI0OTljYTJiNTNiOWZmZTYyODJmNjc2YTkwM2Y0MzRiNjU5YjNhNmJiYTY2NWMxOTc0IiwidGFnIjoiIn0%3D; expires=Wed, 09-Jul-2025 10:33:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-849323072\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2013986343 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QoZh1I8mypHP2aMUh0DEtCk2CpnpWBdxW5ZtIuuB</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"56 characters\">http://127.0.0.1:8000/administrator/ordercancelhistories</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"36 characters\">c065dc93-6b2f-4bf5-b746-6bb48713d4bf</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2013986343\", {\"maxDepth\":0})</script>\n"}}